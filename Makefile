##########################################################################################################################
# File automatically-generated by tool: [projectgenerator] version: [3.13.0-B3] date: [Wed Sep 01 15:48:13 CST 2021] 
##########################################################################################################################

# ------------------------------------------------
# Generic Makefile (based on gcc)
#
# ChangeLog :
#	2017-02-10 - Several enhancements + project update mode
#   2015-07-22 - first version
# ------------------------------------------------


######################################
# target
######################################
TARGET = c3_mcu_main


######################################
# building variables
######################################
# debug build?
DEBUG = 1
# optimization
OPT = -Og


#######################################
# paths
#######################################
# Build path
BUILD_DIR = build
EXTENSIONS_DIR = $(shell pwd)
TOPFOLDER = $(EXTENSIONS_DIR)
UTILITIES_DIR = $(TOPFOLDER)/utilities
MIDDLEWARE_DIR = $(TOPFOLDER)/middlewares
DRIVERS_DIR = $(TOPFOLDER)/drivers/stm32_fw_f4

#UROS_APP_FOLDER = $(shell pwd)/../apps/ping_pong

######################################
# code format
######################################
#FORMAT_RESULT:=$(shell ../../Utilities/tools/astyle-format.sh)
FORMAT_RESULT:=$(shell $(UTILITIES_DIR)/tools/clang-format-all fal pal common hal utilities/devices/)

######################################
# source
######################################
# C sources
C_SOURCES =  \
$(EXTENSIONS_DIR)/hal/core/Src/main.c \
$(EXTENSIONS_DIR)/hal/core/Src/gpio.c \
$(EXTENSIONS_DIR)/hal/core/Src/freertos.c \
$(EXTENSIONS_DIR)/hal/core/Src/rtc.c \
$(EXTENSIONS_DIR)/hal/core/Src/adc.c \
$(EXTENSIONS_DIR)/hal/core/Src/tim.c \
$(EXTENSIONS_DIR)/hal/core/Src/dma.c \
$(EXTENSIONS_DIR)/hal/core/Src/usart.c \
$(EXTENSIONS_DIR)/hal/core/Src/usb_device.c \
$(EXTENSIONS_DIR)/hal/core/Src/usbd_cdc_if.c \
$(EXTENSIONS_DIR)/hal/core/Src/usbd_conf.c \
$(EXTENSIONS_DIR)/hal/core/Src/usbd_desc.c \
$(EXTENSIONS_DIR)/hal/core/Src/stm32f4xx_it.c \
$(EXTENSIONS_DIR)/hal/core/Src/stm32f4xx_hal_msp.c \
$(EXTENSIONS_DIR)/hal/core/Src/stm32f4xx_hal_timebase_tim.c \
$(EXTENSIONS_DIR)/hal/core/Src/system_stm32f4xx.c \
$(EXTENSIONS_DIR)/hal/core/Src/wwdg.c \
$(EXTENSIONS_DIR)/hal/core/Src/rng.c \
$(EXTENSIONS_DIR)/hal/core/Src/soft_i2c.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_eth.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pcd.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pcd_ex.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_usb.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_can.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc_ex.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_wwdg.c \
$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rng.c \
$(DRIVERS_DIR)/STM32_USB_Device_Library/Core/Src/usbd_core.c \
$(DRIVERS_DIR)/STM32_USB_Device_Library/Core/Src/usbd_ctlreq.c \
$(DRIVERS_DIR)/STM32_USB_Device_Library/Core/Src/usbd_ioreq.c \
$(DRIVERS_DIR)/STM32_USB_Device_Library/Class/CDC/Src/usbd_cdc.c \
$(UTILITIES_DIR)/cm_backtrace/fault_test.c \
$(UTILITIES_DIR)/cm_backtrace/cm_backtrace.c \
$(UTILITIES_DIR)/letter-shell/src/shell.c \
$(UTILITIES_DIR)/letter-shell/src/shell_cmd_list.c \
$(UTILITIES_DIR)/letter-shell/src/shell_ext.c \
$(UTILITIES_DIR)/letter-shell/src/shell_companion.c \
$(UTILITIES_DIR)/MultiButton/multi_button.c \
$(UTILITIES_DIR)/lwrb/lwrb.c \
$(UTILITIES_DIR)/cjson/cJSON.c \
$(UTILITIES_DIR)/RTT_FAL/src/rtt_fal.c \
$(UTILITIES_DIR)/RTT_FAL/src/rtt_fal_flash.c \
$(UTILITIES_DIR)/RTT_FAL/src/rtt_fal_partition.c \
$(UTILITIES_DIR)/FlashDB/src/fdb.c \
$(UTILITIES_DIR)/FlashDB/src/fdb_file.c \
$(UTILITIES_DIR)/FlashDB/src/fdb_kvdb.c \
$(UTILITIES_DIR)/FlashDB/src/fdb_tsdb.c \
$(UTILITIES_DIR)/FlashDB/src/fdb_utils.c \
$(UTILITIES_DIR)/common_config/system_status/system_status.c \
$(UTILITIES_DIR)/common_config/autotest/autotest.c \
$(UTILITIES_DIR)/easylogger/src/elog_async.c \
$(UTILITIES_DIR)/easylogger/src/elog_buf.c \
$(UTILITIES_DIR)/easylogger/src/elog_utils.c \
$(UTILITIES_DIR)/easylogger/src/elog.c \
$(UTILITIES_DIR)/check/md5.c \
$(UTILITIES_DIR)/check/crc16.c \
$(UTILITIES_DIR)/SignalLed/src/signal_led.c \
$(UTILITIES_DIR)/common_config/sliding_average_filter/sliding_average_filter.c \
$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS/Source/croutine.c \
$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS/Source/event_groups.c \
$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS/Source/list.c \
$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS/Source/queue.c \
$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS/Source/stream_buffer.c \
$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS/Source/tasks.c \
$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS/Source/timers.c \
$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c \
$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS/Source/portable/MemMang/heap_4.c \
$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.c \
$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS-Plus-POSIX/source/FreeRTOS_POSIX_utils.c \
$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS-Plus-POSIX/source/FreeRTOS_POSIX_clock.c \
$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS-Plus-POSIX/source/FreeRTOS_POSIX_sched.c \
$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS-Plus-POSIX/source/FreeRTOS_POSIX_unistd.c \
$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS-Plus-POSIX/source/FreeRTOS_POSIX_pthread.c \
$(MIDDLEWARE_DIR)/zbus_client/zbus.c \
$(MIDDLEWARE_DIR)/zbus_client/zbus_core.c \
$(MIDDLEWARE_DIR)/zbus_client/zbus_zcm_p.c \
$(MIDDLEWARE_DIR)/zbus_client/zbus_lcm_p.c \
$(MIDDLEWARE_DIR)/zbus_client/zcm/zcm.c \
$(MIDDLEWARE_DIR)/zbus_client/zcm/nonblocking.c \
$(MIDDLEWARE_DIR)/zbus_client/zcm/transport/generic_serial_transport.c \
$(MIDDLEWARE_DIR)/zbus_client/zcm/transport/generic_serial_circ_buff.c \
$(MIDDLEWARE_DIR)/zbus_client/zcm/transport/serial/zcm_transport_serial.c \
$(MIDDLEWARE_DIR)/zbus_client/mpack/mpack-common.c \
$(MIDDLEWARE_DIR)/zbus_client/mpack/mpack-expect.c \
$(MIDDLEWARE_DIR)/zbus_client/mpack/mpack-node.c \
$(MIDDLEWARE_DIR)/zbus_client/mpack/mpack-platform.c \
$(MIDDLEWARE_DIR)/zbus_client/mpack/mpack-reader.c \
$(MIDDLEWARE_DIR)/zbus_client/mpack/mpack-writer.c \
$(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/ros_msgpack_translate.c \
$(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_typesupport_introspection_c/src/identifier.c \
$(TOPFOLDER)/port_middlewares/zbus_client/custom_memory_manager.c \
$(TOPFOLDER)/port_middlewares/zbus_client/allocators.c \
$(TOPFOLDER)/port_middlewares/zbus_client/transport_stm32f4_serial.c \

#ifdef USING_SEGGER
C_SOURCES += $(UTILITIES_DIR)/segger-rtt/RTT/SEGGER_RTT.c
C_SOURCES += $(UTILITIES_DIR)/segger-rtt/RTT/SEGGER_RTT_printf.c
#endif


C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/common/pubsub/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/common/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/fal/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/fal/pid/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/fal/letter-shell/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/fal/easylogger/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/fal/motor/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/fal/flashdb/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/fal/ota/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/fal/charge/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/fal/security/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/fal/rkupdate/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/fal/workstation/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/fal/led/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/fal/button/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/fal/task_control/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/fal/clean/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/fal/tall/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/fal/common/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/hal/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/gpio/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/gpio/sw/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/gpio/button/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/gpio/motor/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/gpio/gpio_ctl/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/uart/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/usb/shell/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/usb/log/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/uart/bms/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/uart/bms/mei_ming/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/uart/carpet_uart/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/uart/water_position_scw/*.c)
# C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/iic/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/tim/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/tim/dig_usound/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/tim/pwmtest/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/tim/motor/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/tim/ir/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/tim/breathe_led/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/flash/gd32f4/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/pal/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/pal/microros/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/adc/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/adc/ir_obstacle/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/adc/psd_cliff/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/gpio/switch_group/*.c)
C_SOURCES += $(wildcard $(UTILITIES_DIR)/devices/adc/adc_reference/*.c)
C_SOURCES += $(wildcard $(EXTENSIONS_DIR)/hal/port/*.c)

C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_typesupport_introspection_c/builtin_interfaces/msg/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_typesupport_introspection_c/chassis_interfaces/msg/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_typesupport_introspection_c/chassis_interfaces/srv/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_typesupport_introspection_c/cvte_sensor_msgs/msg/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_typesupport_introspection_c/cvte_sensor_msgs/srv/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_typesupport_introspection_c/geometry_msgs/msg/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_typesupport_introspection_c/sensor_msgs/msg/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_typesupport_introspection_c/sensor_msgs/srv/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_typesupport_introspection_c/std_msgs/msg/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_typesupport_introspection_c/std_srvs/msg/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_typesupport_introspection_c/std_srvs/srv/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_typesupport_introspection_c/nav_msgs/msg/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_typesupport_introspection_c/nav_msgs/srv/detail/*.c)

C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_generator_c/chassis_interfaces/msg/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_generator_c/chassis_interfaces/srv/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_generator_c/cvte_sensor_msgs/msg/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_generator_c/cvte_sensor_msgs/srv/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_generator_c/geometry_msgs/msg/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_generator_c/sensor_msgs/msg/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_generator_c/sensor_msgs/srv/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_generator_c/std_msgs/msg/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_generator_c/std_srvs/msg/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_generator_c/std_srvs/srv/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_generator_c/nav_msgs/msg/detail/*.c)
C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_generator_c/nav_msgs/srv/detail/*.c)
# C_SOURCES += $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_generator_c/std_srvs/srv/detail/set_bool__functions.c

# C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_runtime_c/src/*.c)
C_SOURCES += $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_runtime_c/src/message_type_support.c
C_SOURCES += $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_runtime_c/src/service_type_support.c
C_SOURCES += $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_runtime_c/src/string_functions.c
C_SOURCES += $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_runtime_c/src/primitives_sequence_functions.c

# C_SOURCES += $(wildcard $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rcutils/src/*.c)
C_SOURCES += $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rcutils/src/allocator.c

#$(wildcard $(UROS_APP_FOLDER)/*.c)
# Removing heap4 manager while being polite with STM32CubeMX
TMPVAR := $(C_SOURCES)
C_SOURCES := $(filter-out $(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS/Source/portable/MemMang/heap_4.c, $(TMPVAR))

TMPVAR1 := $(C_SOURCES)
C_SOURCES := $(filter-out $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_generator_c/chassis_interfaces/msg/detail/camera_info__functions.c, $(TMPVAR1))

TMPVAR2 := $(C_SOURCES)
C_SOURCES := $(filter-out $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_generator_c/std_msgs/msg/detail/empty__functions.c, $(TMPVAR2))

TMPVAR3 := $(C_SOURCES)
C_SOURCES := $(filter-out $(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_generator_c/chassis_interfaces/srv/detail/set_bool__functions.c, $(TMPVAR3))

# ASM sources
ASM_SOURCES =  \
$(EXTENSIONS_DIR)/hal/core/startup_stm32f429xx.s \
$(UTILITIES_DIR)/cm_backtrace/fault_handler/gcc/cmb_fault.s

#######################################
# binaries
#######################################
PREFIX = arm-none-eabi-
# The gcc compiler bin path can be either defined in make command via GCC_PATH variable (> make GCC_PATH=xxx)
# either it can be added to the PATH environment variable.
ifdef GCC_PATH
CC = $(GCC_PATH)/$(PREFIX)gcc
AS = $(GCC_PATH)/$(PREFIX)gcc -x assembler-with-cpp
CP = $(GCC_PATH)/$(PREFIX)objcopy
SZ = $(GCC_PATH)/$(PREFIX)size
else
CC = $(PREFIX)gcc
AS = $(PREFIX)gcc -x assembler-with-cpp
CP = $(PREFIX)objcopy
SZ = $(PREFIX)size
endif
HEX = $(CP) -O ihex
BIN = $(CP) -O binary -S
 
#######################################
# CFLAGS
#######################################
# cpu
CPU = -mcpu=cortex-m4

# fpu
FPU = -mfpu=fpv4-sp-d16

# float-abi
FLOAT-ABI = -mfloat-abi=hard

# mcu
MCU = $(CPU) -mthumb $(FPU) $(FLOAT-ABI)

# macros for gcc
# AS defines
AS_DEFS = 

# C defines
C_DEFS =  \
-DUSE_HAL_DRIVER \
-DSTM32F429xx


# AS includes
AS_INCLUDES =  \
-IInc

# C includes
C_INCLUDES += -I$(EXTENSIONS_DIR)/common/pubsub
C_INCLUDES += -I$(EXTENSIONS_DIR)/common
C_INCLUDES += -I$(EXTENSIONS_DIR)/fal 
C_INCLUDES += -I$(EXTENSIONS_DIR)/fal/pid
C_INCLUDES += -I$(EXTENSIONS_DIR)/fal/letter-shell
C_INCLUDES += -I$(EXTENSIONS_DIR)/fal/easylogger
C_INCLUDES += -I$(EXTENSIONS_DIR)/fal/cm_backtrace
C_INCLUDES += -I$(EXTENSIONS_DIR)/fal/motor
C_INCLUDES += -I$(EXTENSIONS_DIR)/fal/flashdb
C_INCLUDES += -I$(EXTENSIONS_DIR)/fal/ota
C_INCLUDES += -I$(EXTENSIONS_DIR)/fal/charge
C_INCLUDES += -I$(EXTENSIONS_DIR)/fal/security
C_INCLUDES += -I$(EXTENSIONS_DIR)/fal/rkupdate
C_INCLUDES += -I$(EXTENSIONS_DIR)/fal/workstation
C_INCLUDES += -I$(EXTENSIONS_DIR)/fal/led
C_INCLUDES += -I$(EXTENSIONS_DIR)/fal/button
C_INCLUDES += -I$(EXTENSIONS_DIR)/fal/task_control
C_INCLUDES += -I$(EXTENSIONS_DIR)/fal/tall
C_INCLUDES += -I$(EXTENSIONS_DIR)/fal/clean
C_INCLUDES += -I$(EXTENSIONS_DIR)/fal/common
C_INCLUDES += -I$(EXTENSIONS_DIR)/hal
C_INCLUDES += -I$(UTILITIES_DIR)/devices
C_INCLUDES += -I$(UTILITIES_DIR)/devices/include
C_INCLUDES += -I$(UTILITIES_DIR)/devices/gpio
C_INCLUDES += -I$(UTILITIES_DIR)/devices/gpio/sw
C_INCLUDES += -I$(UTILITIES_DIR)/devices/gpio/switch_group
C_INCLUDES += -I$(UTILITIES_DIR)/devices/gpio/button
C_INCLUDES += -I$(UTILITIES_DIR)/devices/gpio/motor
C_INCLUDES += -I$(UTILITIES_DIR)/devices/gpio/gpio_ctl
C_INCLUDES += -I$(UTILITIES_DIR)/devices/uart
C_INCLUDES += -I$(UTILITIES_DIR)/devices/usb/shell
C_INCLUDES += -I$(UTILITIES_DIR)/devices/usb/log
C_INCLUDES += -I$(UTILITIES_DIR)/devices/uart/bms
C_INCLUDES += -I$(UTILITIES_DIR)/devices/uart/bms/mei_ming
C_INCLUDES += -I$(UTILITIES_DIR)/devices/uart/carpet_uart
C_INCLUDES += -I$(UTILITIES_DIR)/devices/uart/water_position_scw
C_INCLUDES += -I$(UTILITIES_DIR)/devices/iic
C_INCLUDES += -I$(UTILITIES_DIR)/devices/iic/LSM6DS3
C_INCLUDES += -I$(UTILITIES_DIR)/devices/tim/dig_usound
C_INCLUDES += -I$(UTILITIES_DIR)/devices/tim
C_INCLUDES += -I$(UTILITIES_DIR)/devices/tim/pwmtest
C_INCLUDES += -I$(UTILITIES_DIR)/devices/tim/motor
C_INCLUDES += -I$(UTILITIES_DIR)/devices/tim/ir
C_INCLUDES += -I$(UTILITIES_DIR)/devices/tim/breathe_led
C_INCLUDES += -I$(UTILITIES_DIR)/devices/can
C_INCLUDES += -I$(UTILITIES_DIR)/devices/can/motor
C_INCLUDES += -I$(UTILITIES_DIR)/devices/adc
C_INCLUDES += -I$(UTILITIES_DIR)/devices/adc/ir_obstacle
C_INCLUDES += -I$(UTILITIES_DIR)/devices/adc/adc_reference
C_INCLUDES += -I$(UTILITIES_DIR)/devices/adc/psd_cliff
C_INCLUDES += -I$(UTILITIES_DIR)/devices/flash
C_INCLUDES += -I$(EXTENSIONS_DIR)/hal/port
C_INCLUDES += -I$(EXTENSIONS_DIR)/hal/core/Inc
C_INCLUDES += -I$(EXTENSIONS_DIR)/pal 
C_INCLUDES += -I$(EXTENSIONS_DIR)/pal/microros
C_INCLUDES += -I$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Inc
C_INCLUDES += -I$(DRIVERS_DIR)/STM32F4xx_HAL_Driver/Inc/Legacy
C_INCLUDES += -I$(DRIVERS_DIR)/STM32_USB_Device_Library/Core/Inc
C_INCLUDES += -I$(DRIVERS_DIR)/STM32_USB_Device_Library/Class/CDC/Inc
C_INCLUDES += -I$(DRIVERS_DIR)/CMSIS/Device/ST/STM32F4xx/Include 
C_INCLUDES += -I$(DRIVERS_DIR)/CMSIS/Include 
C_INCLUDES += -I$(MIDDLEWARE_DIR)/FreeRTOS/include 
C_INCLUDES += -I$(MIDDLEWARE_DIR)/FreeRTOS/include/private
C_INCLUDES += -I$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS/
C_INCLUDES += -I$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS/Source/include 
C_INCLUDES += -I$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS/Source/CMSIS_RTOS_V2 
C_INCLUDES += -I$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS/Source/portable/GCC/ARM_CM4F 
C_INCLUDES += -I$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS-Plus-POSIX/include 
C_INCLUDES += -I$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS-Plus-POSIX/include/portable/empty_portable 
C_INCLUDES += -I$(MIDDLEWARE_DIR)/FreeRTOS/FreeRTOS-Plus-POSIX/include/portable 
C_INCLUDES += -I$(MIDDLEWARE_DIR)/zbus_client 
C_INCLUDES += -I$(MIDDLEWARE_DIR)/zbus_client/types 
C_INCLUDES += -I$(MIDDLEWARE_DIR)/zbus_client/zcm/transport 
C_INCLUDES += -I$(MIDDLEWARE_DIR)/zbus_client/zcm/transport/serial 
C_INCLUDES += -I$(MIDDLEWARE_DIR)/zbus_client/mpack 
C_INCLUDES += -I$(MIDDLEWARE_DIR)/zbus_client/types/ros_msg 
C_INCLUDES += -I$(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_generator_c 
C_INCLUDES += -I$(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_typesupport_introspection_c 
C_INCLUDES += -I$(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_typesupport_introspection_c/include 
C_INCLUDES += -I$(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_runtime_c/include 
C_INCLUDES += -I$(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rosidl_typesupport_interface/include 
C_INCLUDES += -I$(MIDDLEWARE_DIR)/zbus_client/types/ros_msg/rcutils/include 
C_INCLUDES += -I$(TOPFOLDER)/port_middlewares/zbus_client 
C_INCLUDES += -I$(UTILITIES_DIR)/cm_backtrace 
C_INCLUDES += -I$(UTILITIES_DIR)/cm_backtrace/Languages/en-US 
C_INCLUDES += -I$(UTILITIES_DIR)/cm_backtrace/Languages/zh-CN 
C_INCLUDES += -I$(UTILITIES_DIR)/letter-shell/src 
C_INCLUDES += -I$(UTILITIES_DIR)/MultiButton 
C_INCLUDES += -I$(UTILITIES_DIR)/lwrb 
C_INCLUDES += -I$(UTILITIES_DIR)/check
C_INCLUDES += -I$(UTILITIES_DIR)/RTT_FAL/inc
C_INCLUDES += -I$(UTILITIES_DIR)/FlashDB/inc
C_INCLUDES += -I$(UTILITIES_DIR)/common_config/system_status
C_INCLUDES += -I$(UTILITIES_DIR)/common_config/autotest
C_INCLUDES += -I$(UTILITIES_DIR)/common_config/sliding_average_filter/
C_INCLUDES += -I$(UTILITIES_DIR)/cjson 
C_INCLUDES += -I$(UTILITIES_DIR)/easylogger/inc
C_INCLUDES += -I$(UTILITIES_DIR)/SignalLed/inc

#ifdef USING_SEGGER
C_INCLUDES += -I$(UTILITIES_DIR)/segger-rtt/RTT
#endif


# compile gcc flags
ASFLAGS = $(MCU) $(AS_DEFS) $(AS_INCLUDES) $(OPT) -Wall -fdata-sections -ffunction-sections

CFLAGS = $(MCU) $(C_DEFS) $(C_INCLUDES) $(OPT) -Wall -fdata-sections -ffunction-sections

ifeq ($(DEBUG), 1)
CFLAGS += -g -gdwarf-2 -Wall
endif

# 使用BLDCM电源管理(闲时关闭电源功能）
CFLAGS += -DUSING_BLDCM_POWER_MANAGER

#直流电机
CFLAGS += -DBLDCM_DRIVER
#森创电机
#CFLAGS += -DSYTRON_DRIVER
#抽污检测
CFLAGS += -DSEWAGE_WATRE_CHECK
#污水槽在位检测
CFLAGS += -DSEWAGE_WATRE_GROOVE
#污水满检测
CFLAGS += -DWATER_FULL
#水空检测
CFLAGS += -DWATER_EMPTY
#尘盒检测
CFLAGS += -DDIRT_BOX
#急停
CFLAGS += -DEMERG
#碰撞传感器
CFLAGS += -DCRASH
#左碰撞传感器
CFLAGS += -DCRASH_LEFT
#右碰撞传感器
CFLAGS += -DCRASH_RIGHT
#驱动轮
CFLAGS += -DDEFINE_DRIVER
#推杆电机
CFLAGS += -DDEFINE_UP_DOWN_PUSH_ROD
#边刷
CFLAGS += -DDEFINE_SIDE_BRUSH
#滚刷
CFLAGS += -DDEFINE_ROLLER_BRUSH
#滚筒
CFLAGS += -DDEFINE_ROLLER_TUBE
#清水泵
CFLAGS += -DDEFINE_CLEAN_WATER_PUMP
#污水泵
CFLAGS += -DDEFINE_SEWAGE_WATER_PUMP
#风机
CFLAGS += -DDEFINE_FAN_MOTOR
#水位传感器
CFLAGS += -DCLEAN_WATER_SENSOR
#集水槽满传感器
CFLAGS += -DSEWAGE_TANK_FULL_DET_SENSOR
#清水阀
#CFLAGS += -DDEFINE_CLEAN_WATER_VALVE
#污水阀
#CFLAGS += -DDEFINE_SEWAGE_WATER_VALVE
CFLAGS += -DCVTE_GD32_VBUS_SENSING_DISABLE
# 海帕
CFLAGS += -DDEFINE_FAN_HEPA

CI_BUILD_NUM=0
#获取commit_id的前8个字符
COMMIT_ID=$(shell git rev-parse --short=8 HEAD)
#获取编译时间
BUILD_TIME=$(shell date +%Y-%m-%d_%H:%M:%S)
#获取当前分支名
CURRENT_BRANCH=$(shell git rev-parse --abbrev-ref HEAD)
#设置软件版本号
SOFTWARE_VERSION=0.0.1
#设置型号
MODEL_NAME=$(TARGET)

#增加SEGGER-RTT输入输出的缓存
ifdef USING_SEGGER
CFLAGS += -DBUFFER_SIZE_UP=2048 -DBUFFER_SIZE_DOWN=32
CFLAGS += -DUSING_SEGGER
endif

# Generate dependency information
CFLAGS += -MMD -MP -MF"$(@:%.o=%.d)" -DCOMMIT_ID=\"$(COMMIT_ID)\" -DBUILD_TIME=\"$(BUILD_TIME)\" -DCURRENT_BRANCH=\"$(CURRENT_BRANCH)\" 
CFLAGS += -DTARGET=\"$(TARGET)\" -DSOFTWARE_VERSION=\"$(SOFTWARE_VERSION).$(CI_BUILD_NUM)\" -DMODEL_NAME=\"$(MODEL_NAME)\"
CFLAGS += -Wno-unused-function -Wno-unused-but-set-variable -Wno-unused-variable -Wno-unused-result -Wno-implicit-function-declaration

# Microros multithread support platform
CFLAGS += -DPLATFORM_NAME_FREERTOS

#######################################
# LDFLAGS
#######################################
# link script
ifdef IAP
CFLAGS += -DIAP
LDSCRIPT = $(EXTENSIONS_DIR)/hal/STM32F429ZITx_FLASH_IAP.ld
else
LDSCRIPT = $(EXTENSIONS_DIR)/hal/STM32F429ZITx_FLASH.ld
endif

# libraries
LIBS = -lc -lm -lnosys 
LIBDIR = 
LDFLAGS = $(MCU) -specs=nano.specs -T$(LDSCRIPT) $(LIBDIR) $(LIBS) -Wl,-Map=$(BUILD_DIR)/$(TARGET).map,--cref -Wl,--gc-sections

# printf float
LDFLAGS += -lc -lrdimon -u _printf_float

# default action: build all
all: $(BUILD_DIR)/$(TARGET).elf $(BUILD_DIR)/$(TARGET).hex $(BUILD_DIR)/$(TARGET).bin
	cp $(BUILD_DIR)/$(TARGET).elf $(EXTENSIONS_DIR)/${TARGET}.elf

#######################################
# build the application
#######################################
# list of objects
OBJECTS = $(addprefix $(BUILD_DIR)/,$(notdir $(C_SOURCES:.c=.o)))
vpath %.c $(sort $(dir $(C_SOURCES)))
# list of ASM program objects
OBJECTS += $(addprefix $(BUILD_DIR)/,$(notdir $(ASM_SOURCES:.s=.o)))
vpath %.s $(sort $(dir $(ASM_SOURCES)))

 ifneq ($(V),1)
 Q       := @
 NULL    := 2>/dev/null
 endif

$(BUILD_DIR)/%.o: %.c Makefile | $(BUILD_DIR) 
	@printf "  CC      $(*).c\n"
	$(Q) $(CC) -c $(CFLAGS) -Wa,-a,-ad,-alms=$(BUILD_DIR)/$(notdir $(<:.c=.lst)) $< -o $@

$(BUILD_DIR)/%.o: %.s Makefile | $(BUILD_DIR)
	@printf "  AS      $(*).c\n"
	$(Q) $(AS) -c $(CFLAGS) $< -o $@

$(BUILD_DIR)/$(TARGET).elf: $(OBJECTS) Makefile
	@printf "  LD      $(TARGET).elf\n"
	$(Q) $(CC) $(OBJECTS) $(LDFLAGS) -o $@
	@printf "  SZ      $(TARGET).elf\n"
	$(Q) $(SZ) -A -x $@

$(BUILD_DIR)/%.hex: $(BUILD_DIR)/%.elf | $(BUILD_DIR)
	$(HEX) $< $@
	
$(BUILD_DIR)/%.bin: $(BUILD_DIR)/%.elf | $(BUILD_DIR)
	$(BIN) $< $@	
	
$(BUILD_DIR):
	mkdir $@		

#######################################
# microros static lib compile
#######################################
lib_build:
	./lib_build.bash

microros: lib_build all

#######################################
# clean up
#######################################
clean:
	-rm -fR $(BUILD_DIR)
  
#######################################
# dependencies
#######################################
-include $(wildcard $(BUILD_DIR)/*.d)

OPENOCD := openocd -f interface/jlink.cfg \
        -c 'transport select swd' \
        -f target/stm32f4x.cfg
# download your program
flash: all
	$(OPENOCD) -c init \
		-c 'reset halt' \
		-c 'flash write_image erase $(BUILD_DIR)/$(TARGET).elf' \
		-c 'reset run' \
		-c exit

images:all
	rm -rf $(BUILD_DIR)/*.o
	rm -rf $(BUILD_DIR)/*.d
	rm -rf $(BUILD_DIR)/*.lst
	rm -rf $(BUILD_DIR)/*.map       
	rm -rf $(BUILD_DIR)/${TARGET}_*
	cp $(BUILD_DIR)/$(TARGET).bin $(BUILD_DIR)/${TARGET}_${SOFTWARE_VERSION}.${CI_BUILD_NUM}_${CURRENT_BRANCH}_${COMMIT_ID}.bin
	rm -rf $(BUILD_DIR)/$(TARGET).elf
	rm -rf $(BUILD_DIR)/$(TARGET).hex
	rm -rf $(BUILD_DIR)/$(TARGET).bin

iap:
	cd c3_mcu_bootloader/ && make iap -j32
	touch Makefile
	make images -j32 IAP=1 USING_SEGGER=1
	python3 no_compress_ota_packager_python.py
	rm -rf $(BUILD_DIR)/${TARGET}_*
	cp ${TARGET}.elf $(BUILD_DIR)/${TARGET}_${SOFTWARE_VERSION}.${CI_BUILD_NUM}.${CURRENT_BRANCH}.${COMMIT_ID}.elf
	mv $(BUILD_DIR)/ota_${TARGET}_${SOFTWARE_VERSION}.${CI_BUILD_NUM}_${CURRENT_BRANCH}_${COMMIT_ID}.bin $(BUILD_DIR)/${TARGET}_${SOFTWARE_VERSION}.${CI_BUILD_NUM}.${CURRENT_BRANCH}.${COMMIT_ID}.bin
	mv $(BUILD_DIR)/ota_${TARGET}_${SOFTWARE_VERSION}.${CI_BUILD_NUM}_${CURRENT_BRANCH}_${COMMIT_ID}.rbl $(BUILD_DIR)/${TARGET}_${SOFTWARE_VERSION}.${CI_BUILD_NUM}.${CURRENT_BRANCH}.${COMMIT_ID}.rbl
	cp $(BUILD_DIR)/${TARGET}_${SOFTWARE_VERSION}.${CI_BUILD_NUM}.${CURRENT_BRANCH}.${COMMIT_ID}.bin $(BUILD_DIR)/${TARGET}_lastest.${CURRENT_BRANCH}.bin
	cp $(BUILD_DIR)/${TARGET}_${SOFTWARE_VERSION}.${CI_BUILD_NUM}.${CURRENT_BRANCH}.${COMMIT_ID}.rbl $(BUILD_DIR)/${TARGET}_lastest.${CURRENT_BRANCH}.rbl
download:
	rm -rf flash.jlink
	touch flash.jlink
	echo "r" > flash.jlink
	echo "loadfile build/${TARGET}_${SOFTWARE_VERSION}.${CI_BUILD_NUM}.${CURRENT_BRANCH}.${COMMIT_ID}.bin" >> flash.jlink
	echo "r" >> flash.jlink
	echo "exit" >> flash.jlink
	JLinkExe -device STM32F429ZI  -si SWD -speed 4000 -CommanderScript ./flash.jlink

# *** EOF ***
