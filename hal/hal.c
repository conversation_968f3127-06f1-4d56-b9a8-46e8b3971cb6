﻿/*************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:      hal.c
 ** Author:
 ** Version:        V1.0
 ** Date:           2021-01-
 ** Description:
 ** Others:
 ** Function List:
 ** History:        2021-01 robot创建
 ** <time>   <author>    <version >   <desc>
 **
 *************************************************/

/*****************************************************************
 * 包含头文件
 ******************************************************************/

#include "hal.h"
#include "devices.h"
#include "define.h"
#include "define_motor.h"
#include "define_simuart.h"
#include "tim.h"
#include "cliff_oml.h"
#include "breathe_led.h"
#include "psd_cliff.h"

/**
 * @addtogroup Robot-NAV_407
 * @{
 */

/**
 * @defgroup Robot_HAL 硬件适配层 - HAL
 *
 * @brief
 * HAL层屏蔽了硬件平台的差异，向上层提供了统一的调用函数接口，支持不同的硬件平台
 * \n \n
 * @{
 */

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 宏定义
 ******************************************************************/

/*****************************************************************
 * 结构定义
 ******************************************************************/

/*****************************************************************
 * 全局变量定义
 ******************************************************************/
int battery_handle                    = -1;
int shell_handle                      = -1;
int log_handle                        = -1;
int ir_rx1_handle                     = -1;
int ir_rx2_handle                     = -1;
int ir_tx1_handle                     = -1;
int charge_en_handle                  = -1;
int breathe_led_handle                = -1;
int power_3V3_handle                  = -1;
int power_5V_handle                   = -1;
int power_12V_handle                  = -1;
int dig_usound_S12_handle             = -1;
int cliff_oml_handle1                 = -1;
int cliff_oml_handle2                 = -1;
int cliff_oml_handle3                 = -1;
int cliff_oml_handle4                 = -1;
int cliff_oml_handle5                 = -1;
int cliff_oml_handle6                 = -1;
int switch_usound_handle              = -1;
int switch_oc1_handle                 = -1;
int switch_oc2_handle                 = -1;
int button_handle                     = -1;
int clean_water_handle                = -1;
int gpio_motor_handle                 = -1;
int dvt_switch_handle                 = -1;
int reset_emerg_handle                = -1;
int cliff_psd_handle1                 = -1;
int wheel_handle                      = -1;
int side_brush_handle                 = -1;
int roller_brush_handle               = -1;
int roller_tube_handle                = -1;
int clean_water_pump_handle           = -1;
int sewage_water_pump_handle          = -1;
int fan_motor_handle                  = -1;
int up_elevator_handle                = -1;
int down_elevator_handle              = -1;
int sewage_valve_handle               = -1;
int switch_ir_obstacle_handle         = -1;
int get_sewage_empty_handle           = -1;
int detect_sewage_full_handle         = -1;
int adc_reference_handle              = -1;
int mcu_refint_handle                 = -1;
int sewageTank_full_detect_ver_handle = -1;
int charge_voltage_handle             = -1;
int switch_charge_handle              = -1;
int adc_version_handle                = -1;
int adc_light_det_handle              = -1;
int gpio_version1_handle              = -1;
int gpio_version2_handle              = -1;
int gpio_ctl_cwde_handle              = -1;
/*****************************************************************
 * 私有全局变量定义
 ******************************************************************/
struct bus_rs485 rs4851 = {
    .common =
        {
            .bus_name = "rs4851",
        },
    .uart =
        {
            .bus_name = "uart2",
        },
    .gpio_ctrl =
        {
            .bus_name = "gpioD",
            .bus_addr = 4,
        },
    .gpio_read_status = GPIO_ACTIVE_HIGH,
};

struct bus_sw sw1 = {
    .common =
        {
            .bus_name = "sw1",
        },
    .gpio_ctrl[0] =
        {
            .bus_name = "gpioD",
            .bus_addr = 5,
        },
    .gpio_ctrl[1] =
        {
            .bus_name = "gpioD",
            .bus_addr = 6,
        },
    .gpio_ctrl[2] =
        {
            .bus_name = "gpioB",
            .bus_addr = 3,
        },

};
struct bus_info bus_gpio_lift_motor_sewage = {
    .bus_name = "gpioF",
    .bus_addr = 9,
};
struct bus_info bus_gpio_lift_motor_down = {
    .bus_name = "gpioG",
    .bus_addr = 14,
};
struct bus_info bus_gpio_lift_motor_up = {
    .bus_name = "gpioG",
    .bus_addr = 15,
};

struct bus_info bus_gpio_button_left = {
    .bus_name = "gpioB",
    .bus_addr = 13,
};
struct bus_info bus_gpio_button_right = {
    .bus_name = "gpioB",
    .bus_addr = 12,
};

struct bus_info bus_gpio_tall_button_left = {
    .bus_name = "ioexp1",
    .bus_addr = 2,
};
struct bus_info bus_gpio_tall_button_right = {
    .bus_name = "ioexp1",
    .bus_addr = 3,
};

struct bus_info bus_gpio_tall_button_left_v2 = {
    .bus_name = "gpioG",
    .bus_addr = 0,
};

struct bus_info bus_gpio_tall_button_right_v2 = {
    .bus_name = "gpioG",
    .bus_addr = 1,
};

struct bus_info bus_gpio_emerg = {
    .bus_name = "gpioE",
    .bus_addr = 2,
};
struct bus_info bus_gpio_crash_left_2 = {
    .bus_name = "gpioA",
    .bus_addr = 1,
};
struct bus_info bus_gpio_crash_right_3 = {
    .bus_name = "gpioC",
    .bus_addr = 4,
};

//未使用
struct bus_info bus_dvt_switch = {
    .bus_name = "gpioA",
    .bus_addr = 10,
};

struct bus_info bus_gpio_sewage_water_empty = {
    .bus_name = "gpioE",
    .bus_addr = 9,
};

struct bus_info bus_gpio_sewage_water_check = {
    .bus_name = "gpioE",
    .bus_addr = 3,
};

struct bus_info bus_gpio_sewage_water_groove = {
    //污水槽
    .bus_name = "gpioF",
    .bus_addr = 14,
};

struct bus_info bus_gpio_carpet1 = {
    .bus_name = "gpioB",
    .bus_addr = 7,
};

struct bus_info bus_gpio_carpet2 = {
    .bus_name = "gpioB",
    .bus_addr = 6,
};

struct bus_info bus_gpio_carpet3 = {
    .bus_name = "gpioB",
    .bus_addr = 8,
};

struct bus_info bus_gpio_reset_emerg = {
    .bus_name = "gpioG",
    .bus_addr = 2,
};

struct bus_info bus_gpio_virtual_wall_magnetic = {
    .bus_name = "gpioE",
    .bus_addr = 4,
};

struct bus_info bus_gpio_dirt_box = {
    .bus_name = "gpioF",
    .bus_addr = 11,
};

struct bus_info bus_gpio_3V3 = {
    .bus_name = "gpioG",
    .bus_addr = 10,
};
struct bus_info bus_gpio_5V = {
    .bus_name = "gpioD",
    .bus_addr = 11,
};
struct bus_info bus_gpio_12V = {
    .bus_name = "gpioD",
    .bus_addr = 7,
};
struct bus_info bus_gpio_led_r = {
    .bus_name = "gpioD",
    .bus_addr = 15,
};
struct bus_info bus_gpio_led_g = {
    .bus_name = "gpioD",
    .bus_addr = 14,
};
struct bus_info NULL_BUS = {
    .bus_name = "gpioA",
    .bus_addr = 15,
};

struct bus_info bus_gpio_clean_water_dectect_enable = {
    .bus_name = "gpioG",
    .bus_addr = 4,
};

struct bus_info shell_uart = {
    .bus_name = "uart4",
};

struct bus_info bus_gpio_dig_usound = {
    .bus_name = "gpioC",
    .bus_addr = 9,
};
struct bus_info bus_tim_dig_usound = {
    .bus_name = "tim8",
    .bus_addr = 2,
};

struct bus_info bus_can_sytron = {
    .bus_name = "can1",
};

struct bus_info bus_tim_pwm_cliff_oml = {
    .bus_name = "tim5",
    .bus_addr = 0,
};

struct bus_info bus_tim_oc_cliff_oml = {
    .bus_name = "tim5",
    .bus_addr = 1,
};

struct bus_info bus_tim_ir_rx1 = {
    .bus_name = "tim7",
    .bus_addr = 0,
};

struct bus_info bus_tim_ir_rx2 = {
    .bus_name = "tim14",
    .bus_addr = 0,
};
struct bus_info bus_tim_ir_tx1 = {
    .bus_name = "tim10",
    .bus_addr = 0,
};
struct bus_info bus_charge_en = {
    .bus_name = "gpioD",
    .bus_addr = 0,
};

struct bus_info bus_gpio_virtul_wall = {
    .bus_name = "gpioE",
    .bus_addr = 4,
};

// 是否有集水槽满检测 版本识别
struct bus_info bus_sewage_tank_version_det = {
    .bus_name = "gpioC",
    .bus_addr = 9,
};

struct bus_info bus_gpio_version1 = {
    .bus_name = "gpioA",
    .bus_addr = 10,
};

struct bus_info bus_gpio_version2 = {
    .bus_name = "gpioE",
    .bus_addr = 0,
};

struct bus_switch_group bus_switch_group1[] = {{.gpio_ctrl[0] =
                                                    {
                                                        .bus_name = "gpioG",
                                                        .bus_addr = 5,
                                                    },
                                                .gpio_ctrl[1] =
                                                    {
                                                        .bus_name = "gpioG",
                                                        .bus_addr = 6,
                                                    },
                                                .gpio_ctrl[2] =
                                                    {
                                                        .bus_name = "",  //若是两路切换开关，则第三路留空
                                                        .bus_addr = 0,
                                                    }}

};
// oc1
/*
00-右边刷电机电流
01-净水泵电机电流
10-中扫电机电流
11-升降电机电流
*/
struct bus_switch_group bus_switch_group2[] = {{.gpio_ctrl[0] =
                                                    {
                                                        .bus_name = "gpioF",
                                                        .bus_addr = 3,
                                                    },
                                                .gpio_ctrl[1] =
                                                    {
                                                        .bus_name = "gpioF",
                                                        .bus_addr = 2,
                                                    },
                                                .gpio_ctrl[2] =
                                                    {
                                                        .bus_name = "",  //若是两路切换开关，则第三路留空
                                                        .bus_addr = 0,
                                                    },
                                                .gpio_ctrl[3] =
                                                    {
                                                        .bus_name = "",  //若是两路切换开关，则第三路留空
                                                        .bus_addr = 0,
                                                    }}

};

/*红外避障切换开关*/
struct bus_switch_group bus_switch_group4[] = {{.gpio_ctrl[0] =
                                                    {
                                                        .bus_name = "gpioG",
                                                        .bus_addr = 6,
                                                    },
                                                .gpio_ctrl[1] =
                                                    {
                                                        .bus_name = "gpioG",
                                                        .bus_addr = 5,
                                                    },
                                                .gpio_ctrl[2] =
                                                    {
                                                        .bus_name = "gpioG",  //若是两路切换开关，则第三路留空
                                                        .bus_addr = 44,
                                                    },
                                                .gpio_ctrl[3] = {
                                                    .bus_name = "gpioG",  //若是两路切换开关，则第三路留空
                                                    .bus_addr = 3,
                                                }}};

// oc2
/*
00-左边刷电机电流
01-滚筒电机电流
10-污水泵电机电流
11-xx
*/
struct bus_switch_group bus_switch_group3[] = {{.gpio_ctrl[0] =
                                                    {
                                                        .bus_name = "gpioF",
                                                        .bus_addr = 7,
                                                    },
                                                .gpio_ctrl[1] =
                                                    {
                                                        .bus_name = "gpioF",
                                                        .bus_addr = 6,
                                                    },
                                                .gpio_ctrl[2] =
                                                    {
                                                        .bus_name = "",  //若是两路切换开关，则第三路留空
                                                        .bus_addr = 0,
                                                    },
                                                .gpio_ctrl[3] =
                                                    {
                                                        .bus_name = "",  //若是两路切换开关，则第三路留空
                                                        .bus_addr = 0,
                                                    }}

};

struct bus_info bus_en_gpio_cwp = {
    .bus_name = "gpioF",
    .bus_addr = 15,
};
//驱动轮
struct bus_info bus_en_gpio_wheel = {
    .bus_name = "gpioG",
    .bus_addr = 13,
};
PID_ATTR         motor_wheel_pid    = {.kd = 0.000000, .ki = 0.00020, .kp = 0.00000};
MOTOR_BLDCM_BUS  motor_wheel_bus[2] = {{
                                          .pwm.bus_name         = "tim11",  // left
                                          .pwm.bus_addr         = 0,
                                          .fg_encoding.bus_name = "tim2",
                                          .fg_encoding.bus_addr = 1,
                                          .gpio_cw_ccw.bus_name = "gpioG",
                                          .gpio_cw_ccw.bus_addr = 12,
                                          // 驱动轮电源常开，避免驱动闲时关闭电源，先屏蔽
                                          /*
                                          .gpio_vcc.bus_name    = "gpioG",
                                          .gpio_vcc.bus_addr    = 13,
                                          */
                                          .adc.bus_name = "adc3",
                                          .adc.bus_addr = 15,
                                      },
                                      {
                                          .pwm.bus_name         = "tim4",  // right
                                          .pwm.bus_addr         = 0,
                                          .fg_encoding.bus_name = "tim3",
                                          .fg_encoding.bus_addr = 1,
                                          .gpio_cw_ccw.bus_name = "gpioG",
                                          .gpio_cw_ccw.bus_addr = 11,
                                          // 驱动轮电源常开，避免驱动闲时关闭电源，先屏蔽
                                          /*
                                          .gpio_vcc.bus_name    = "gpioG",
                                          .gpio_vcc.bus_addr    = 13,
                                          */
                                          .adc.bus_name = "adc3",
                                          .adc.bus_addr = 14,
                                      }};
MOTOR_BLDCM_ATTR motor_wheel_attr   = {.bldcm_nums         = 2,
                                     .gpio_cw_ccw_status = 1,  // 1正转 0反转    上层speed给下正值为正转  负值为反转
                                     .bldcm_bus          = motor_wheel_bus,
                                     .pid_parm           = &motor_wheel_pid,
                                     .open_state         = 1,
                                     .brake_enable_state = 1};
//风机
MOTOR_BLDCM_BUS motor_fan_bus = {.pwm.bus_name            = "tim8",
                                 .pwm.bus_addr            = 0,
                                 .fg_ic.bus_name          = "tim8",
                                 .fg_ic.bus_addr          = 1,
                                 .gpio_cw_ccw.bus_name    = "gpioG",
                                 .gpio_cw_ccw.bus_addr    = 7,
                                 .gpio_brake.bus_name     = "gpioG",
                                 .gpio_brake.bus_addr     = 8,
                                 .adc.bus_name            = "adc1",
                                 .adc.bus_addr            = 6,
                                 .gpio_error_det.bus_name = "gpioF",
                                 .gpio_error_det.bus_addr = 1,
                                 .gpio_vcc.bus_name       = "gpioA",
                                 .gpio_vcc.bus_addr       = 9};

MOTOR_BLDCM_ATTR motor_fan_attr =
    {.bldcm_nums = 1, .gpio_cw_ccw_status = 1, .bldcm_bus = &motor_fan_bus, .pid_parm = NULL, .brake_enable_state = 0, .open_state = 1};
//滚筒
struct bus_info bus_en_gpio_roller_tube = {
    .bus_name = "gpioE",
    .bus_addr = 15,
};
MOTOR_BLDCM_BUS  motor_roller_tube_bus  = {.pwm.bus_name         = "tim9",
                                         .pwm.bus_addr         = 0,
                                         .gpio_cw_ccw.bus_name = "gpioE",
                                         .gpio_cw_ccw.bus_addr = 1,
                                         .gpio_vcc.bus_name    = "gpioE",
                                         .gpio_vcc.bus_addr    = 12,
                                         .adc.bus_name         = "adc3",
                                         .adc.bus_addr         = 10,
                                         .fg_ic.bus_name       = "tim9",
                                         .fg_ic.bus_addr       = 1};
MOTOR_BLDCM_ATTR motor_roller_tube_attr = {.bldcm_nums         = 1,
                                           .gpio_cw_ccw_status = 1,
                                           .bldcm_bus          = &motor_roller_tube_bus,
                                           .pid_parm           = NULL,
                                           .open_state         = 1};

//边刷
struct bus_info bus_en_gpio_side_brush = {
    .bus_name = "gpioE",
    .bus_addr = 12,
};
MOTOR_BLDCM_BUS  motor_side_brush_bus[2] = {{.pwm.bus_name         = "tim12",  // left
                                            .pwm.bus_addr         = 1,
                                            .gpio_cw_ccw.bus_name = "gpioD",
                                            .gpio_cw_ccw.bus_addr = 10,
                                            .gpio_vcc.bus_name    = "gpioE",
                                            .gpio_vcc.bus_addr    = 12,
                                            .adc.bus_name         = "adc3",
                                            .adc.bus_addr         = 10,
                                            .fg_ic.bus_name       = "tim12",
                                            .fg_ic.bus_addr       = 0},
                                           {.pwm.bus_name         = "tim1",  // right
                                            .pwm.bus_addr         = 3,
                                            .gpio_cw_ccw.bus_name = "gpioE",
                                            .gpio_cw_ccw.bus_addr = 15,
                                            .gpio_vcc.bus_name    = "gpioF",
                                            .gpio_vcc.bus_addr    = 15,
                                            .adc.bus_name         = "adc1",
                                            .adc.bus_addr         = 11,
                                            .fg_ic.bus_name       = "tim4",
                                            .fg_ic.bus_addr       = 1}};
MOTOR_BLDCM_ATTR motor_side_brush_attr   = {.bldcm_nums         = 2,
                                          .gpio_cw_ccw_status = 1,
                                          .bldcm_bus          = motor_side_brush_bus,
                                          .pid_parm           = NULL,
                                          .open_state         = 1};

//滚刷
struct bus_info bus_en_gpio_roller_brush = {
    .bus_name = "gpioE",
    .bus_addr = 12,
};
MOTOR_BLDCM_BUS  motor_roller_brush_bus  = {.pwm.bus_name         = "tim1",
                                          .pwm.bus_addr         = 2,
                                          .gpio_cw_ccw.bus_name = "gpioE",
                                          .gpio_cw_ccw.bus_addr = 10,
                                          .gpio_vcc.bus_name    = "gpioF",
                                          .gpio_vcc.bus_addr    = 15,
                                          .adc.bus_name         = "adc1",
                                          .adc.bus_addr         = 11,
                                          .fg_ic.bus_name       = "tim1",
                                          .fg_ic.bus_addr       = 1};
MOTOR_BLDCM_ATTR motor_roller_brush_attr = {.bldcm_nums         = 1,
                                            .gpio_cw_ccw_status = 1,
                                            .bldcm_bus          = &motor_roller_brush_bus,
                                            .pid_parm           = NULL,
                                            .open_state         = 1};

struct bus_info bus_button = {
    .bus_name = "gpioA",
};

//清水箱水位
struct bus_info bus_clean_water = {
    .bus_name = "uart3",
};
//污水泵控制io
// struct bus_info bus_gpio_motor_swp = {.bus_name = "gpioE", .bus_addr = 0};
MOTOR_IO_BUS bus_gpio_motor_swp = {
    .gpio_vcc_H.bus_name = "gpioA",
    .gpio_vcc_H.bus_addr = 8,
    .adc.bus_name        = "adc3",
    .adc.bus_addr        = 10,
    .fg_ic.bus_name      = "tim8",
    .fg_ic.bus_addr      = 3,
};

//清水泵控制io
// struct bus_info bus_gpio_motor_cwp = {.bus_name = "gpioF", .bus_addr = 14};
MOTOR_IO_BUS bus_gpio_motor_cwp = {
    .gpio_vcc_H.bus_name = "gpioF",
    .gpio_vcc_H.bus_addr = 10,
    .adc.bus_name        = "adc1",
    .adc.bus_addr        = 11,
};
//升降电机升
// struct bus_info bus_gpio_motor_eu = {.bus_name = "gpioF", .bus_addr = 13};
MOTOR_IO_BUS bus_gpio_motor_eu = {
    .gpio_vcc_H.bus_name = "gpioF",
    .gpio_vcc_H.bus_addr = 12,
    .adc.bus_name        = "adc1",
    .adc.bus_addr        = 11,
};
//升降电机降
// struct bus_info bus_gpio_motor_ed = {.bus_name = "gpioF", .bus_addr = 12};
MOTOR_IO_BUS bus_gpio_motor_ed = {
    .gpio_vcc_H.bus_name = "gpioF",
    .gpio_vcc_H.bus_addr = 13,
    .adc.bus_name        = "adc1",
    .adc.bus_addr        = 11,
};
//排污阀
MOTOR_IO_BUS bus_gpio_sewage_valve = {
    .gpio_vcc_H.bus_name = "gpioE",
    .gpio_vcc_H.bus_addr = 7,
};
//水槽污水满检测
struct bus_info bus_ir_sewagefull = {
    .bus_name = "adc1",
    .bus_addr = 5,
};

// 充电输入电压检测
struct bus_info bus_battery_voltage = {
    .bus_name = "adc1",
    .bus_addr = 5,
};
// 充电输入电压检测通道切换
struct bus_info bus_switch_charge = {
    .bus_name = "gpioG",
    .bus_addr = 3,
};

struct bus_info bus_adc_version = {
    .bus_name = "adc1",
    .bus_addr = 10,
};

struct bus_info bus_adc_mcu_refint = {
    .bus_name = "adc1",
    .bus_addr = 17,
};

struct bus_info bus_adc_light_det = {
    .bus_name = "adc1",
    .bus_addr = 7,
};

MOTOR_IO_ATTR gpio_motor_swp_attr = {.open_state = 1, .io_bus = &bus_gpio_motor_swp};
MOTOR_IO_ATTR gpio_motor_cwp_attr = {.open_state = 1, .io_bus = &bus_gpio_motor_cwp};
MOTOR_IO_ATTR gpio_motor_cwv_attr = {.open_state = 1};
MOTOR_IO_ATTR gpio_motor_swv_attr = {.open_state = 1, .io_bus = &bus_gpio_sewage_valve};
MOTOR_IO_ATTR gpio_motor_eu_attr  = {.open_state = 1, .io_bus = &bus_gpio_motor_eu};
MOTOR_IO_ATTR gpio_motor_ed_attr  = {.open_state = 1, .io_bus = &bus_gpio_motor_ed};

SIM_UART_ATTR ir_rx2_attr = {.gpio_info.bus_addr = 13, .gpio_info.bus_name = "gpioC", .gpio_port = GPIOC, .tim = &htim14};
SIM_UART_ATTR ir_rx1_attr = {.gpio_info.bus_addr = 14, .gpio_info.bus_name = "gpioC", .gpio_port = GPIOC, .tim = &htim7};
SIM_UART_ATTR ir_tx1_attr = {.gpio_info.bus_addr = 0,
                             .gpio_port          = IR_TX1_GPIO_Port,
                             .tim                = &htim10,
                             .tx_pwm_tim         = &htim13,
                             .tx_pwm_channel     = TIM_CHANNEL_1,
                             .tx_pwm_pulse       = 768};

BREATHE_LED_ATTR_T ledg_attr =
    {.gpio_tim.bus_name = "tim4", .gpio_tim.bus_addr = 2, .pwm_pulse = 0, .duty_cycle_delay = 20, .pwm_pulse_max = 1.0f, .breathe_enable = 0};
BREATHE_LED_ATTR_T ledr_attr =
    {.gpio_tim.bus_name = "tim4", .gpio_tim.bus_addr = 3, .pwm_pulse = 0, .duty_cycle_delay = 20, .pwm_pulse_max = 1.0f, .breathe_enable = 0};

sensor_obj_st sensor_objs[] = {
    {.bus_com = &NULL_BUS, .dev_name = "letter_shell", .handle = &shell_handle},
    {.bus_com = &NULL_BUS, .dev_name = "log", .handle = &log_handle},
    {.bus_com = &bus_tim_ir_rx1, .dev_name = "ir_rx1", .handle = &ir_rx1_handle, .args = (void *) &ir_rx1_attr},
    {.bus_com = &bus_tim_ir_rx2, .dev_name = "ir_rx2", .handle = &ir_rx2_handle, .args = (void *) &ir_rx2_attr},
    {.bus_com = (struct bus_info *) &rs4851, .dev_name = "bms_dwg01", .handle = &battery_handle},
    {
        .bus_com  = &bus_tim_ir_tx1,
        .dev_name = "ir_tx1",
        .handle   = &ir_tx1_handle,
        .args     = &ir_tx1_attr,
    },
    {
        .bus_com  = &bus_charge_en,
        .dev_name = "gpio_ctl_charge_en",
        .handle   = &charge_en_handle,
    },
    {.bus_com = &NULL_BUS, .dev_name = "breathe_led", .handle = &breathe_led_handle},
    {.bus_com = &bus_gpio_3V3, .dev_name = "gpio_ctl_3V3", .handle = &power_3V3_handle},
    {.bus_com = &bus_gpio_5V, .dev_name = "gpio_ctl_5V", .handle = &power_5V_handle},
    {.bus_com = &bus_gpio_12V, .dev_name = "gpio_ctl_12V", .handle = &power_12V_handle},
    {.bus_com = &bus_dvt_switch, .dev_name = "gpio_ctl_dvt_switch", .handle = &dvt_switch_handle},
    {.bus_com = &bus_gpio_reset_emerg, .dev_name = "gpio_ctl_reset_emerg", .handle = &reset_emerg_handle},
    {.bus_com = &bus_gpio_sewage_water_empty, .dev_name = "gpio_ctl_sewage_empty", .handle = &get_sewage_empty_handle},
//{.bus_com = &bus_tim_dig_usound, .dev_name = "du_S12", .handle = &dig_usound_S12_handle, .args = (void *) &bus_gpio_dig_usound},
#ifdef SYTRON_DRIVER
    {
        .bus_com  = &bus_can_sytron,
        .dev_name = "sytron",
        .handle   = &wheel_handle,
    },
#endif
    {
        .bus_com  = &bus_tim_oc_cliff_oml,
        .dev_name = "cliff_oml1",
        .handle   = &cliff_oml_handle1,
        .args     = (void *) &bus_tim_pwm_cliff_oml,
    },
    {
        .bus_com  = &NULL_BUS,
        .dev_name = "psd_cliff",
        .handle   = &cliff_psd_handle1,
    },

    // {.bus_com = &NULL_BUS, .dev_name = "switch_group1", .handle = &switch_usound_handle, .args = (void *) bus_switch_group1},
    {.bus_com = &NULL_BUS, .dev_name = "switch_group2", .handle = &switch_oc1_handle, .args = (void *) bus_switch_group2},
    {.bus_com = &NULL_BUS, .dev_name = "switch_group3", .handle = &switch_oc2_handle, .args = (void *) bus_switch_group3},
#ifdef BLDCM_DRIVER
    {.bus_com = &NULL_BUS, .dev_name = "bldcm_wheel", .handle = &wheel_handle, .args = (void *) &motor_wheel_attr},
#endif
    {.bus_com = &NULL_BUS, .dev_name = "bldcm_fan", .handle = &fan_motor_handle, .args = (void *) &motor_fan_attr},
    {.bus_com = &NULL_BUS, .dev_name = "bldcm_rt", .handle = &roller_tube_handle, .args = (void *) &motor_roller_tube_attr},
    {.bus_com = &NULL_BUS, .dev_name = "bldcm_sb", .handle = &side_brush_handle, .args = (void *) &motor_side_brush_attr},
    {.bus_com = &NULL_BUS, .dev_name = "bldcm_rb", .handle = &roller_brush_handle, .args = (void *) &motor_roller_brush_attr},

    {
        .bus_com  = &bus_button,
        .dev_name = "button",
        .handle   = &button_handle,
    },
    // {
    // 	.bus_com = &bus_gpio_motor,
    // 	.dev_name = "gpio_motor",
    // 	.handle = &gpio_motor_handle,
    // },
    {.bus_com = &NULL_BUS, .dev_name = "gpio_motor_cwp", .handle = &clean_water_pump_handle, .args = &gpio_motor_cwp_attr},
    {.bus_com = &bus_clean_water, .dev_name = "water_scw", .handle = &clean_water_handle},
    {.bus_com = &NULL_BUS, .dev_name = "gpio_motor_swp", .handle = &sewage_water_pump_handle, .args = &gpio_motor_swp_attr},
    {.bus_com = &NULL_BUS, .dev_name = "gpio_motor_eu", .handle = &up_elevator_handle, .args = &gpio_motor_eu_attr},
    {.bus_com = &NULL_BUS, .dev_name = "gpio_motor_ed", .handle = &down_elevator_handle, .args = &gpio_motor_ed_attr},
    {.bus_com = &NULL_BUS, .dev_name = "gpio_motor_swv", .handle = &sewage_valve_handle, .args = &gpio_motor_swv_attr},
    {.bus_com = &bus_ir_sewagefull, .dev_name = "adc_reference_sewagefull", .handle = &detect_sewage_full_handle},
    {.bus_com  = &bus_sewage_tank_version_det,
     .dev_name = "gpio_ctl_sewage_version",
     .handle   = &sewageTank_full_detect_ver_handle,
     .args     = NULL},
    {.bus_com = &bus_battery_voltage, .dev_name = "adc_reference_charge_voltage", .handle = &charge_voltage_handle},
    {
        .bus_com  = &bus_switch_charge,
        .dev_name = "gpio_ctl_det_charge",
        .handle   = &switch_charge_handle,
    },
    {.bus_com = &bus_adc_version, .dev_name = "adc_reference_version", .handle = &adc_version_handle},
    {.bus_com = &bus_adc_mcu_refint, .dev_name = "adc_reference_refint", .handle = &mcu_refint_handle},
    {.bus_com = &bus_adc_light_det, .dev_name = "adc_reference_light_det", .handle = &adc_light_det_handle},
    {.bus_com = &bus_gpio_version2, .dev_name = "gpio_ctl_ver2", .handle = &gpio_version2_handle, .args = NULL},
    {.bus_com = &bus_gpio_version1, .dev_name = "gpio_ctl_ver1", .handle = &gpio_version1_handle, .args = NULL},
    {.bus_com = &bus_gpio_clean_water_dectect_enable, .dev_name = "gpio_ctl_cwde", .handle = &gpio_ctl_cwde_handle},

};
//断崖adc
struct bus_info bus_adc_cliff_oml01 = {
    .bus_name = "adc1",
    .bus_addr = 12,
};
struct bus_info bus_adc_cliff_oml02 = {
    .bus_name = "adc1",
    .bus_addr = 13,
};
struct bus_info bus_adc_cliff_oml03 = {
    .bus_name = "adc1",
    .bus_addr = 3,
};
struct bus_info bus_adc_cliff_oml04 = {
    .bus_name = "adc1",
    .bus_addr = 9,
};
struct bus_info bus_adc_cliff_oml05 = {
    .bus_name = "adc1",
    .bus_addr = 8,
};
struct bus_info bus_adc_cliff_oml06 = {
    .bus_name = "adc1",
    .bus_addr = 4,
};
//不用
struct bus_info bus_ir_obstacle = {
    .bus_name = "adc1",
    .bus_addr = 0,
};
struct bus_info bus_adc_crash_left_1 = {
    .bus_name = "adc1",
    .bus_addr = 2,
};
struct bus_info bus_adc_crash_right_4 = {
    .bus_name = "adc1",
    .bus_addr = 15,
};

adc_sensor_obj_st cliff_sensor_objs[] = {
    {
        .bus_com       = &bus_ir_obstacle,
        .dev_name      = "ir_obstacle",
        .dirction_flag = PWM_ADC_DIRETION_FRONT_REDUCE_AFTER,
        .filter_type   = LIMIT_ADC_TRANS_THRESHOLD,
    },
    {
        .bus_com             = &bus_adc_cliff_oml01,
        .dev_name            = "cliff_oml1",
        .adc_value_top_thres = 600,
        .adc_value_low_thres = 500,
        .dirction_flag       = PWM_ADC_DIRETION_FRONT_REDUCE_AFTER,
        .filter_type         = LIMIT_ADC_TRANS_SCHMIDT,
        .adc_vol_thres       = PSD_DEFAULT_CLIFF_VOL,
    },
    {
        .bus_com             = &bus_adc_cliff_oml02,
        .dev_name            = "cliff_oml2",
        .adc_value_top_thres = 600,
        .adc_value_low_thres = 500,
        .debounce_num        = 20,
        .dirction_flag       = PWM_ADC_DIRETION_FRONT_REDUCE_AFTER,
        .filter_type         = LIMIT_ADC_TRANS_SCHMIDT,
        .adc_vol_thres       = PSD_DEFAULT_CLIFF_VOL,
    },
    {
        .bus_com             = &bus_adc_cliff_oml03,
        .dev_name            = "cliff_oml3",
        .adc_value_top_thres = 600,
        .adc_value_low_thres = 500,
        .dirction_flag       = PWM_ADC_DIRETION_FRONT_REDUCE_AFTER,
        .filter_type         = LIMIT_ADC_TRANS_SCHMIDT,
        .adc_vol_thres       = PSD_DEFAULT_CLIFF_VOL,
    },
    {
        .bus_com             = &bus_adc_cliff_oml04,
        .dev_name            = "cliff_oml4",
        .adc_value_top_thres = 600,
        .adc_value_low_thres = 500,
        .dirction_flag       = PWM_ADC_DIRETION_FRONT_REDUCE_AFTER,
        .filter_type         = LIMIT_ADC_TRANS_SCHMIDT,
        .adc_vol_thres       = PSD_DEFAULT_CLIFF_VOL,
    },
    {
        .bus_com             = &bus_adc_cliff_oml05,
        .dev_name            = "cliff_oml5",
        .adc_value_top_thres = 600,
        .adc_value_low_thres = 500,
        .debounce_num        = 20,
        .dirction_flag       = PWM_ADC_DIRETION_FRONT_REDUCE_AFTER,
        .filter_type         = LIMIT_ADC_TRANS_SCHMIDT,
        .adc_vol_thres       = PSD_DEFAULT_CLIFF_VOL,
    },
    {
        .bus_com             = &bus_adc_cliff_oml06,
        .dev_name            = "cliff_oml6",
        .adc_value_top_thres = 600,
        .adc_value_low_thres = 500,
        .dirction_flag       = PWM_ADC_DIRETION_FRONT_REDUCE_AFTER,
        .filter_type         = LIMIT_ADC_TRANS_SCHMIDT,
        .adc_vol_thres       = PSD_DEFAULT_CLIFF_VOL,
    },
    {
        .bus_com              = &bus_adc_crash_left_1,
        .dev_name             = "crash_left",
        .adc_value_thres      = 600,
        .adc_value_top_thres  = 1500,
        .adc_value_low_thres  = 700,
        .dirction_flag        = PWM_ADC_DIRETION_FRONT_REDUCE_AFTER,
        .filter_type          = LIMIT_ADC_TRANS_THRESHOLD,
        .adc_value_delt_thres = 500,
        .adc_value_delt_ms    = 100,
    },
    {
        .bus_com              = &bus_adc_crash_right_4,
        .dev_name             = "crash_right",
        .adc_value_thres      = 600,
        .adc_value_top_thres  = 1500,
        .adc_value_low_thres  = 700,
        .dirction_flag        = PWM_ADC_DIRETION_FRONT_REDUCE_AFTER,
        .filter_type          = LIMIT_ADC_TRANS_THRESHOLD,
        .adc_value_delt_thres = 500,
        .adc_value_delt_ms    = 100,
    },
};

/*****************************************************************
 * 外部变量声明（如果全局变量没有在其它的H文件声明，引用时需在此处声明，
 *如果已在其它H文件声明，则只需包含此H文件即可）
 ******************************************************************/

/*****************************************************************
 * 静态变量定义
 ******************************************************************/

/*****************************************************************
 * 函数原型声明
 ******************************************************************/

/*****************************************************************
 * 函数定义
 *注意，编写函数需首先定义所有的局部变量等，不允许在
 *函数的中间出现新的变量的定义。
 ******************************************************************/

/*****************************************************************/
/**
 * Function:       hal_init
 * Description:    初始化HAL层
 * Calls:
 * Called By:
 * @param[in]
 * @param[out]
 * @return
 *  - -1 表示打开文件失败
 *  - 0  表示打开文件成功
 *
 * @author:
 * @date
 *
 * @par Modification History
 * @par Author:
 * @par Date:
 * @par Description:
 *
 * @see
 *
 */
/******************************************************************/
int hal_init(void) {
    int index = 0;

    for (index = 0; index < ITEM_NUM(sensor_objs); index++) {
        device_register(sensor_objs[index].bus_com, sensor_objs[index].dev_name, sensor_objs[index].args);
        *(sensor_objs[index].handle) = device_open(sensor_objs[index].dev_name, 0);
    }

    return 0;
}

/*****************************************************************/
/**
 * Function:       hal_deinit
 * Description:    释放HAL层资源
 * Calls:
 * Called By:
 * @param[in]
 * @param[out]
 * @return
 *  - -1 表示打开文件失败
 *  - 0  表示打开文件成功
 *
 * @author:
 * @date
 *
 * @par Modification History
 * @par Author:
 * @par Date:
 * @par Description:
 *
 * @see
 *
 */
/******************************************************************/
int hal_deInit(void) {
    /*释放资源*/
    devices_deinit();
    return 0;
}

#ifdef __cplusplus
}
#endif

/* @} Robot_HAL */
/* @} Robot-NAV_407 */

/**************************** CMSIS ********************************/
/**
 * @addtogroup Robot_HAL
 * @{
 */

/**
 * @defgroup H_CMSIS 微控制器软件接口标准 - CMSIS
 *
 * @brief (CMSIS) 是 Cortex-M 处理器系列的与供应商无关的硬件抽象层。\n
 * CMSIS
 * 可实现与处理器和外设之间的一致且简单的软件接口，从而简化软件的重用，缩短微控制器开发人员新手的学习过程，并缩短新设备的上市时间。\n
 * \n
 * @{
 */

/* @} H_CMSIS */
/* @} Robot_HAL */

/**************************** Driver ********************************/
/**
 * @addtogroup Robot_HAL
 * @{
 */

/**
 * @defgroup H_DRIVER 外围驱动接口API - DRIVER
 *
 * @brief 包含处理器的外围驱动接口API。\n
 * \n
 * @{
 */

/* @} H_DRIVER */
/* @} Robot_HAL */
