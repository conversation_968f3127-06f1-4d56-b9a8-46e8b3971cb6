﻿/*************************************************
  ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
  ** File name:      hal.h
  ** Author:
  ** Version:
  ** Date:
  ** Description:
  ** Others:
  ** Function List:
  ** History:

  ** <time>   <author>    <version >   <desc>
  **
*************************************************/
#ifndef _HAL_H
#define _HAL_H

/*****************************************************************
 * 包含头文件(如非特殊需要，H文件不建议包含其它H文件)
 ******************************************************************/
#include "devices.h"

/**
 * @ingroup Robot_HAL 硬件适配层
 *
 * @brief \n
 * \n
 * @{
 */

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 宏定义
 ******************************************************************/
#define DEVICE_MAX 32

#define ITEM_NUM(items) sizeof(items) / sizeof(items[0])

/*****************************************************************
 * 结构定义
 ******************************************************************/
typedef struct {
    struct bus_info *bus_com;
    uint8_t          dev_name[32];
    int *            handle;
    void *           args;
} sensor_obj_st;

typedef struct {
    struct bus_info *bus_com;
    uint8_t          dev_name[32];
    int *            handle;
    void *           args;
    uint16_t         adc_value_thres;
    uint16_t         adc_value_top_thres;
    uint16_t         adc_value_low_thres;
    uint8_t          dirction_flag;  // 1:后采样点减前采样点， 0:前采样点减后采样点
    uint16_t         debounce_num;   // 消抖次数
    uint8_t          filter_type;
    uint16_t         adc_value_delt_thres;
    uint16_t         adc_value_delt_ms;
    float            adc_vol_thres;
} adc_sensor_obj_st;

/*****************************************************************
 * 全局变量声明
 ******************************************************************/

/*****************************************************************
 * 函数原型声明
 ******************************************************************/

/****************************************************
    功能：初始化FAL层
    返回值:等于0表示成功，其它值表示失败原因
 ***************************************************/
int hal_init(void);

/****************************************************
    功能：释放FAL层资源
    返回值： 等于0表示成功，其它值表示失败原因
***************************************************/
int hal_deInit(void);

/*****************************************************************
 * 函数说明
 ******************************************************************/

#ifdef __cplusplus
}
#endif

/* @} Robot_HAL */

#endif
