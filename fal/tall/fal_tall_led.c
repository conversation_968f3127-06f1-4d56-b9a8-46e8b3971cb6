#define LOG_TAG "fal_tall_led"
#include "stm32f4xx.h"
#include "aw9523.h"
#include <stdio.h>
#include "soft_i2c.h"
#include "log.h"
#include "fal_button.h"
#include "cmsis_os.h"
#include "shell.h"
#include "define_button.h"
#include "devices.h"
#include "string.h"
#include "pubsub.h"
#include "fal_charge.h"
#include "fal_tall_led.h"

extern char work_status[32];
extern char area[32];

// c3增高版相关应用代码
const osThreadAttr_t led_tall_attributes = {.name = "led_tall", .priority = (osPriority_t) osPriorityNormal, .stack_size = 256 * 4};
osThreadId_t         led_tall_thread;

// 香薰控制接口
void aromatherapy_control(device_status_t state) {
    if (state == CLOSE) {
        AW9523_GpioWrite(AW9523_P1, AW9523_PIN_5, AW9523_GPIO_RESET);
    } else if (state == OPEN) {
        AW9523_GpioWrite(AW9523_P1, AW9523_PIN_5, AW9523_GPIO_SET);
    } else {
        // no apply
    }
}

// 示廓灯控制接口
void position_light_control(device_status_t state) {
    if (state == CLOSE) {
        AW9523_GpioWrite(AW9523_P0, AW9523_PIN_1, AW9523_GPIO_RESET);
    } else if (state == OPEN) {
        AW9523_GpioWrite(AW9523_P0, AW9523_PIN_1, AW9523_GPIO_SET);
    } else {
        // no apply
    }
}

void led_tall_run() {
    int timeCount = 0;
    while (1) {
        osDelay(1000);

#if 0        
        uint8_t reg = AW9523_GetID();
        log_i("io拓展芯片ID:%d", reg);
        uint8_t KEY2 = AW9523_GpioRead(AW9523_P1, AW9523_PIN_2);
        uint8_t KEY3 = AW9523_GpioRead(AW9523_P1, AW9523_PIN_3);
        log_i("增高版按键状态:  key2:%d   key3:%d  ", KEY2, KEY3);
        log_i("work_status:%s           area:%s", work_status, area);
#endif

//若工作状态为导航且不在乘梯
//香熏改成在应用层控制
#if 0
        if (!strcmp(work_status, "task_navigating") && strcmp(area, "elevator") && aromatherapyEnble_) {
            if (timeCount < 3000) {
                AW9523_GpioWrite(AW9523_P1, AW9523_PIN_5, AW9523_GPIO_SET);
                timeCount += 1000;
            } else if (timeCount >= 3000 && timeCount <= 13000 + 5000) {
                AW9523_GpioWrite(AW9523_P1, AW9523_PIN_5, AW9523_GPIO_RESET);
                timeCount += 1000;
            } else {
                timeCount = 0;
            }
        } else {
            AW9523_GpioWrite(AW9523_P1, AW9523_PIN_5, AW9523_GPIO_RESET);
        }
#endif
        //若在桩外则开红灯 在桩上把红灯关了
        if (get_is_tall_version()) {
            if (get_charge_feedback() == PUSH_CHARGE_ELEC_PRESS || get_charge_feedback() == AUTO_CHARGE_ELEC_PRESS) {
                // 左红
                AW9523_GpioWrite(AW9523_P0, AW9523_PIN_4, AW9523_GPIO_RESET);
                // 右红
                AW9523_GpioWrite(AW9523_P0, AW9523_PIN_3, AW9523_GPIO_RESET);

                // AW9523_GpioWrite(AW9523_P0, AW9523_PIN_1, AW9523_GPIO_RESET);
            } else {
                // 左红
                AW9523_GpioWrite(AW9523_P0, AW9523_PIN_4, AW9523_GPIO_SET);
                // 右红
                AW9523_GpioWrite(AW9523_P0, AW9523_PIN_3, AW9523_GPIO_SET);

                // AW9523_GpioWrite(AW9523_P0, AW9523_PIN_1, AW9523_GPIO_SET);
            }

            // 右蓝常闭
            AW9523_GpioWrite(AW9523_P0, AW9523_PIN_6, AW9523_GPIO_RESET);
            // 右绿常闭
            AW9523_GpioWrite(AW9523_P0, AW9523_PIN_2, AW9523_GPIO_RESET);

            // 左蓝常闭
            AW9523_GpioWrite(AW9523_P0, AW9523_PIN_7, AW9523_GPIO_RESET);
            // 左绿常闭
            AW9523_GpioWrite(AW9523_P1, AW9523_PIN_4, AW9523_GPIO_RESET);
        }
    }
}

//香熏改成在应用层控制
#if 0
void SetAromatherapyStatus(bool a) {
    aromatherapyEnble_ = a;
}
#endif

int fal_tall_led_init(void) {
    AW9523_Init();
    // AW9523_GpioWrite(AW9523_P1, AW9523_PIN_0, AW9523_GPIO_SET);  //不能放延时后！！！！
    // osDelay(1000);
    //前圆弧灯供电
    //默认关闭
    AW9523_GpioWrite(AW9523_P0, AW9523_PIN_1, AW9523_GPIO_RESET);
    //右绿
    // AW9523_GpioWrite(AW9523_P0, AW9523_PIN_2, AW9523_GPIO_SET);
    //右红
    //默认关闭
    AW9523_GpioWrite(AW9523_P0, AW9523_PIN_3, AW9523_GPIO_RESET);
    //右蓝
    // AW9523_GpioWrite(AW9523_P0, AW9523_PIN_6, AW9523_GPIO_SET);
    // AW9523_GpioWrite(AW9523_P0, AW9523_PIN_2, AW9523_GPIO_RESET);
    //左红
    //默认关闭
    AW9523_GpioWrite(AW9523_P0, AW9523_PIN_4, AW9523_GPIO_RESET);
    //左蓝
    // AW9523_GpioWrite(AW9523_P0, AW9523_PIN_7, AW9523_GPIO_SET);
    //左绿
    // AW9523_GpioWrite(AW9523_P1, AW9523_PIN_4, AW9523_GPIO_SET);
    // 升级/hub切换 切到升级口
    // AW9523_GpioWrite(AW9523_P1, AW9523_PIN_0, AW9523_GPIO_SET);
    //相机供电使能
    AW9523_GpioWrite(AW9523_P1, AW9523_PIN_1, AW9523_GPIO_SET);
    //线激光使能
    AW9523_GpioWrite(AW9523_P1, AW9523_PIN_7, AW9523_GPIO_SET);
    //香熏泵关闭
    AW9523_GpioWrite(AW9523_P1, AW9523_PIN_5, AW9523_GPIO_RESET);
    //消杀泵
    AW9523_GpioWrite(AW9523_P1, AW9523_PIN_6, AW9523_GPIO_SET);
    led_tall_thread = osThreadNew(led_tall_run, NULL, &led_tall_attributes);
    return 0;
}
FAL_MODULE_INIT(fal_tall_led_init);