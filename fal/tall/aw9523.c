/*
 * @brief  Read  device register (platform dependent)
 *
 * @param  handle    customizable argument. In this examples is used in
 *                   order to select the correct sensor bus handler.
 * @param  reg       register to read
 * @param  bufp      pointer to buffer that store the data read
 * @param  len       number of consecutive register to read
 *
 */

#include "soft_i2c.h"
#include "stm32f4xx.h"
#include "aw9523.h"
#include "devices.h"
#include <stdio.h>
#include "bldcm.h"
#include "devices.h"
#include "define_motor.h"
#include "tim_core.h"
#include "gpio_core.h"
#include "adc_core.h"
#include "mem_pool.h"
#include <stddef.h>
#include <string.h>
#include <stdlib.h>
#include "delay.h"
#include "log.h"
#include <math.h>
#include "pid.h"
#include "shell.h"
#ifdef USING_SEGGER
#include "SEGGER_RTT.h"
#endif
#include "bldcm_wheel.h"
#include "bldcm_core.h"
#include "tim.h"
const osMutexAttr_t mutex_aw9523_attr = {
    "aw9523_mutex",                         // human readable mutex name
    osMutexRecursive | osMutexPrioInherit,  // attr_bits
    NULL,                                   // memory for control block
    0U                                      // size for control block
};
osMutexId_t aw9523_mutex;
int32_t     platform_read(void *handle, uint8_t reg, uint8_t *bufp, uint16_t len) {
    uint16_t i;

    /* 检查I2C总线是否繁忙 */
    if (i2c_GetSta() != 1) {
        log_e("soft i2c busy!");

        goto cmd_fail;
    }

    /* 第1步：发起I2C总线启动信号 */
    i2c_Start();
    /* 第2步：发送控制字节，高7bit是地址，bit0是读写控制位，0表示写，1表示读 */
    i2c_SendByte(AW9523_I2C_ADD | BSP_I2C_WR); /* 写指令 */
    /* 第3步：等待ACK */
    if (i2c_WaitAck() != 0) {
        goto cmd_fail;
    }
    /* 第4步: 发送SUB */
    i2c_SendByte(reg);
    /* 第5步: 等待ACK */
    if (i2c_WaitAck() != 0) {
        goto cmd_fail;
    }
    /* 第6步: 发送SR (repeated START) */
    i2c_Start();
    /* 第7步: 发送控制字节 */
    i2c_SendByte(AW9523_I2C_ADD | BSP_I2C_RD); /* 读指令 */
                                               /* 第8步: 发送ACK */
    if (i2c_WaitAck() != 0) {
        goto cmd_fail;
    }
    /* 第9步: 循环读取数据 */
    for (i = 0; i < len; i++) {
        bufp[i] = i2c_ReadByte(); /* 读1个字节 */

        /* 每读完1个字节后,需要主机发送ACK,最后一个字节发送NACK */
        if (i != len - 1) {
            i2c_Ack(); /* 中间字节读完后，CPU产生ACK信号(驱动SDA = 0) */
        } else {
            i2c_NAck(); /* 最后1个字节读完后，CPU产生NACK信号(驱动SDA = 1) */
        }
    }
    /* 第10步：发送停止信号 */
    i2c_Stop();
    return 0;
cmd_fail:
    i2c_Stop();
    return 1;
}

int32_t platform_read_try(void *handle, uint8_t reg, uint8_t *bufp, uint16_t len) {
    uint8_t err_cnt;

    for (err_cnt = 0; err_cnt < 10; err_cnt++) {
        if (platform_read(handle, reg, bufp, len) == 0) {
            return 0;
        }

        osDelay(1);
    }

    return 1;
}

/*
 * @brief  Write generic device register (platform dependent)
 *
 * @param  handle    customizable argument. In this examples is used in
 *                   order to select the correct sensor bus handler.
 * @param  reg       register to write
 * @param  bufp      pointer to buffer that store the data read
 * @param  len       number of consecutive register to read
 *
 */
int32_t platform_write(void *handle, uint8_t reg, const uint8_t *bufp, uint16_t len) {
    uint16_t i;

    /* 检查I2C总线是否繁忙 */
    if (i2c_GetSta() != 1) {
        log_e("soft i2c busy!");

        goto cmd_fail;
    }

    /* 第0步: 发送停止信号 */
    i2c_Stop();
    /* 第1步: 发起I2C总线启动信号 */
    i2c_Start();
    /* 第2步: 发送控制字节 */
    i2c_SendByte(AW9523_I2C_ADD | BSP_I2C_WR);
    /* 第3步: 等待ACK */
    if (i2c_WaitAck() != 0) {
        goto cmd_fail;
    }
    /* 第4步: 发送SUB */
    i2c_SendByte(reg);
    /* 第5步: 等待ACK */
    if (i2c_WaitAck() != 0) {
        goto cmd_fail;
    }
    /* 第6步: 循环发送DATA */
    for (i = 0; i < len; i++) {
        i2c_SendByte(bufp[i]); /* 发一个数据 */
        i2c_Ack();             /*发完一个数据后等待ACK*/
    }
    /* 第7步: 发送停止信号 */
    i2c_Stop();
    return 0;
cmd_fail:
    i2c_Stop();
    return 1;
}

int32_t platform_write_try(void *handle, uint8_t reg, const uint8_t *bufp, uint16_t len) {
    uint8_t err_cnt;

    for (err_cnt = 0; err_cnt < 10; err_cnt++) {
        if (platform_write(handle, reg, bufp, len) == 0) {
            return 0;
        }

        osDelay(1);
    }

    return 1;
}

uint8_t AW9523_GCR(void) {
    if (osMutexAcquire(aw9523_mutex, 0) == osOK) {
        uint8_t regval = 0;
        regval         = 0x10;
        if (platform_write_try(NULL, AW9523_ADDR_GCR, &regval, 1)) {
            osMutexRelease(aw9523_mutex);
            return AW9523_NK;
        }
        osMutexRelease(aw9523_mutex);
        return AW9523_OK;
    }
    return AW9523_NK;
}

uint8_t AW9523_GetID(void) {
    if (osMutexAcquire(aw9523_mutex, 0) == osOK) {
        uint8_t regval = 0;
        if (platform_read_try(NULL, AW9523_ADDR_GETID, &regval, 1)) {
            osMutexRelease(aw9523_mutex);
            return AW9523_NK;
        }
        osMutexRelease(aw9523_mutex);
        return regval;
    }
    return AW9523_NK;
}

//将P0或P1某个IO设置成INPUT模式
uint8_t AW9523_ConfigInput(int P, int pin) {
    if (osMutexAcquire(aw9523_mutex, 0) == osOK) {
        uint8_t regval = 0;
        if (P) {
            if (platform_read_try(NULL, AW9523_ADDR_P1CONFIG, &regval, 1)) {
                osMutexRelease(aw9523_mutex);
                return AW9523_NK;
            }
            regval = regval | (1 << pin);
            if (platform_write_try(NULL, AW9523_ADDR_P1CONFIG, &regval, 1)) {
                osMutexRelease(aw9523_mutex);
                return AW9523_NK;
            }
        } else {
            if (platform_read_try(NULL, AW9523_ADDR_P0CONFIG, &regval, 1)) {
                osMutexRelease(aw9523_mutex);
                return AW9523_NK;
            }
            regval = regval | (1 << pin);
            if (platform_write_try(NULL, AW9523_ADDR_P0CONFIG, &regval, 1)) {
                osMutexRelease(aw9523_mutex);
                return AW9523_NK;
            }
        }
        osMutexRelease(aw9523_mutex);
        return AW9523_OK;
    }
    return AW9523_NK;
}

void AW9523_Init(void) {
    aw9523_mutex = osMutexNew(&mutex_aw9523_attr);
    i2c_CfgGpio();
    AW9523_GCR();
    AW9523_ConfigInput(AW9523_P1, AW9523_PIN_2);
    AW9523_ConfigInput(AW9523_P1, AW9523_PIN_3);
    AW9523_All_rst(AW9523_P0);
    AW9523_All_rst(AW9523_P1);
}

//获取P0或者P1上的所有IO电平状态
uint8_t AW9523_get(int P) {
    uint8_t regval = 0;
    if (P) {
        if (platform_read_try(NULL, AW9523_ADDR_P1INPUT, &regval, 1)) {
            return AW9523_NK;
        }
    } else {
        if (platform_read_try(NULL, AW9523_ADDR_P0INPUT, &regval, 1)) {
            return AW9523_NK;
        }
    }
    return regval;
}

//将P0或P1上某个io设置成高电平
uint8_t AW9523_set(int P, int pin) {
    uint8_t regval = AW9523_get(P);
    regval         = regval | (1 << pin);
    if (P) {
        if (platform_write_try(NULL, AW9523_ADDR_P1OUTPUT, &regval, 1)) {
            return AW9523_NK;
        }
    } else {
        if (platform_write_try(NULL, AW9523_ADDR_P0OUTPUT, &regval, 1)) {
            return AW9523_NK;
        }
    }
    return AW9523_OK;
}

//将P0或P1上某个io设置成低电平
uint8_t AW9523_rst(int P, int pin) {
    uint8_t regval = AW9523_get(P);
    regval         = regval & ~(1 << pin);
    if (P) {
        if (platform_write_try(NULL, AW9523_ADDR_P1OUTPUT, &regval, 1)) {
            return AW9523_NK;
        }
    } else {
        if (platform_write_try(NULL, AW9523_ADDR_P0OUTPUT, &regval, 1)) {
            return AW9523_NK;
        }
    }
    return AW9523_OK;
}

//将P0或P1上所有io设置成低电平
uint8_t AW9523_All_rst(int P) {
    uint8_t regval = 0;
    if (P) {
        if (platform_write_try(NULL, AW9523_ADDR_P1OUTPUT, &regval, 1)) {
            return AW9523_NK;
        }
    } else {
        if (platform_write_try(NULL, AW9523_ADDR_P0OUTPUT, &regval, 1)) {
            return AW9523_NK;
        }
    }
    return AW9523_OK;
}

#include "shell.h"

uint8_t AW9523_GpioWrite(int P, int pin, int level) {
    if (osMutexAcquire(aw9523_mutex, 10) == osOK) {
        uint8_t res = level ? AW9523_set(P, pin) : AW9523_rst(P, pin);
        osMutexRelease(aw9523_mutex);
        return res;
    } else {
        log_e("AW9523_GpioWrite get mutex err!");
    }

    return AW9523_NK;
}

SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), aw9523_GpioWrite, AW9523_GpioWrite, aw9523_GpioWrite);

uint8_t AW9523_GpioToggle(int P, int pin) {
    uint8_t res = AW9523_GpioRead(P, pin);
    if (res) {
        res = AW9523_GpioWrite(P, pin, 0);
    } else {
        res = AW9523_GpioWrite(P, pin, 1);
    }
    return res;
}

uint8_t AW9523_GpioRead(uint8_t P, uint8_t pin) {
    if (osMutexAcquire(aw9523_mutex, 10) == osOK) {
        uint8_t regval = AW9523_get(P);
        regval         = regval & (1 << pin);
        osMutexRelease(aw9523_mutex);
        return regval ? AW9523_GPIO_SET : AW9523_GPIO_RESET;
    } else {
        log_e("AW9523_GpioRead get mutex err!");
    }

    return AW9523_NK;
}
