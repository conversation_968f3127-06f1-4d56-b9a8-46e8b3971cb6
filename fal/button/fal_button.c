#define LOG_TAG "fal_button"
#include "log.h"
#include "fal_button.h"
#include "cmsis_os.h"
#include "shell.h"
#include "define_button.h"
#include "devices.h"
#include "string.h"
#include "pubsub.h"
#include "pal_task_control.h"
#include "fal_charge.h"
#include "pal_clean.h"
#include "aw9523.h"
#include "fal_tall_led.h"
#include "button_type.h"

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 私有宏定义
 ******************************************************************/

/*****************************************************************
 * 私有结构体/共用体/枚举定义
 ******************************************************************/
typedef enum {
    CHILD_HOOK_UNLOCKED = 1,
    CHILD_HOOK_LOCKED,
    CHILD_HOOK_UNLOCKING,
} CHILD_HOOK_STATE_E;

static BUTTON_ATTACH_ATTR_T phy_button_attach_attr[2];
static BUTTON_ATTR_T        phy_button_attr[2];
BUTTON_STATUS               phyButtonStatus_;
SEWAGE_SWITCH_ATTR_T        sewageSwitch_;
const osThreadAttr_t        longPressButtonsAttributes = {
    .name       = "longPressButtons",
    .priority   = (osPriority_t) osPriorityNormal,
    .stack_size = 256 * 4,
};
/*****************************************************************
 * 全局变量定义
 ******************************************************************/

/*****************************************************************
 * 私有全局变量定义
 ******************************************************************/
static CHILD_HOOK_STATE_E child_hook         = CHILD_HOOK_UNLOCKED;
static bool               is_tall_version    = false;
int                       button_cfg_get_ret = -1;
bool                      is_button_cfg_get  = false;
/*****************************************************************
 * 外部变量声明（如果全局变量没有在其它的H文件声明，引用时需在此处声明，
 *如果已在其它H文件声明，则只需包含此H文件即可）
 ******************************************************************/
extern struct bus_info bus_gpio_button_right;
extern struct bus_info bus_gpio_button_left;
extern struct bus_info bus_gpio_tall_button_right;
extern struct bus_info bus_gpio_tall_button_left;
extern struct bus_info bus_gpio_tall_button_right_v2;
extern struct bus_info bus_gpio_tall_button_left_v2;

extern button_type_e button_type;
extern int           button_handle;
/*****************************************************************
 * 私有函数原型声明
 ******************************************************************/
static void sync_button_type_cfg(void);
/*****************************************************************
 * 函数定义
 *注意，编写函数需首先定义所有的局部变量等，不允许在
 *函数的中间出现新的变量的定义。
 ******************************************************************/
int button_right_callback(uint8_t index, BUTTON_STATUS_TYPE type) {
    log_i("right button type:%d", type);
    switch (type) {
        case BUTTON_SINGLE_CLICK:
            key_state_pub("charge", "single_click");
            SetSewageStatus(SEWAGE_SWITCH_CLOSE);
            break;
        case BUTTON_DOUBLE_CLICK:
            key_state_pub("charge", "double_click");
            SetSewageStatus(SEWAGE_SWITCH_CLOSE);
            break;
        case BUTTON_LONG_PRESS_START:
            key_state_pub("charge", "long_press");
            SET_BIT(phyButtonStatus_, RIGHT_LONG_PRESS_STATUS);
            break;
        case BUTTON_PRESS_UP:
            CLEAR_BIT(phyButtonStatus_, RIGHT_LONG_PRESS_STATUS);
            break;
        case BUTTON_PRESS_DOWN:
            break;
        default:
            break;
    }
    return 0;
}

int button_left_callback(uint8_t index, BUTTON_STATUS_TYPE type) {
    log_i("left button type:%d", type);
    switch (type) {
        case BUTTON_SINGLE_CLICK:
            key_state_pub("task", "single_click");
            SetSewageStatus(SEWAGE_SWITCH_CLOSE);
            break;
        case BUTTON_DOUBLE_CLICK:
            key_state_pub("task", "double_click");
            SetSewageStatus(SEWAGE_SWITCH_CLOSE);
            break;
        case BUTTON_LONG_PRESS_START:
            key_state_pub("task", "long_press");
            SET_BIT(phyButtonStatus_, LEFT_LONG_PRESS_STATUS);
            break;
        case BUTTON_PRESS_UP:
            CLEAR_BIT(phyButtonStatus_, LEFT_LONG_PRESS_STATUS);
            break;
        case BUTTON_PRESS_DOWN:
            break;
        default:
            break;
    }
    return 0;
}

//排污机构手动开启条件为：左右按键长按2秒
//排污机构关闭条件为，在手动开启情况下：
// 1.任意按键触发
// 2.机器压桩/出桩信号
// 3.机器收到任务请求 进入等待导航/导航状态
// 4.超时
static void longPressOpenSewageRun(void *argument) {
    uint8_t aw9523_id   = 0;
    uint8_t read_id_cnt = 0;

    while (read_id_cnt <= 5) {
        aw9523_id = AW9523_GetID();
        log_i("aw9523_id:0x%2x", aw9523_id);
        read_id_cnt++;
        if (aw9523_id == AW9525_CHIP_ID) {
            log_i("it's fall version");
            is_tall_version = true;
            break;
        }
        osDelay(1000);
    }

    sewageSwitch_.status = SEWAGE_SWITCH_IDLE;

    memset(&phy_button_attr[0], 0, sizeof(BUTTON_ATTR_T));
    memset(&phy_button_attach_attr[0], 0, sizeof(BUTTON_ATTACH_ATTR_T));

    phy_button_attr[0].index                = 0;
    phy_button_attr[0].trigger_condition    = GPIO_TRIGGER_LOW;
    phy_button_attr[0].prevent_shake_num    = 15;    //防抖动次数
    phy_button_attr[0].period               = 2000;  //长按触发时间
    phy_button_attr[0].long_press_hold_type = BUTTON_LONG_PRESS_HOLD_SINGLE_TRIGGER;
    phy_button_attr[0].gpio_info            = &bus_gpio_button_right;

    phy_button_attach_attr[0].gpio_info                      = &bus_gpio_button_right;
    phy_button_attach_attr[0].attach.button_single_click     = 1;
    phy_button_attach_attr[0].attach.button_double_click     = 1;
    phy_button_attach_attr[0].attach.button_long_press_start = 1;
    phy_button_attach_attr[0].attach.button_press_down       = 1;
    phy_button_attach_attr[0].attach.button_press_up         = 1;
    phy_button_attach_attr[0].button_callback                = button_right_callback;

    // device_ioctl(button_handle, BUTTON_CMD_SET_ADD_BUTTON, (void *) &phy_button_attr[0]);
    // device_ioctl(button_handle, BUTTON_CMD_SET_ADD_ATTACH, (void *) &phy_button_attach_attr[0]);

    memset(&phy_button_attr[1], 0, sizeof(BUTTON_ATTR_T));
    memset(&phy_button_attach_attr[1], 0, sizeof(BUTTON_ATTACH_ATTR_T));

    phy_button_attr[1].gpio_info            = &bus_gpio_button_left;
    phy_button_attr[1].index                = 0;
    phy_button_attr[1].trigger_condition    = GPIO_TRIGGER_LOW;
    phy_button_attr[1].prevent_shake_num    = 15;    //防抖动次数
    phy_button_attr[1].period               = 2000;  //长按触发时间
    phy_button_attr[1].long_press_hold_type = BUTTON_LONG_PRESS_HOLD_SINGLE_TRIGGER;

    phy_button_attach_attr[1].gpio_info                      = &bus_gpio_button_left;
    phy_button_attach_attr[1].attach.button_single_click     = 1;
    phy_button_attach_attr[1].attach.button_double_click     = 1;
    phy_button_attach_attr[1].attach.button_long_press_start = 1;
    phy_button_attach_attr[1].attach.button_press_down       = 1;
    phy_button_attach_attr[1].attach.button_press_up         = 1;
    phy_button_attach_attr[1].button_callback                = button_left_callback;

    // device_ioctl(button_handle, BUTTON_CMD_SET_ADD_BUTTON, (void *) &phy_button_attr[1]);
    // device_ioctl(button_handle, BUTTON_CMD_SET_ADD_ATTACH, (void *) &phy_button_attach_attr[1]);

    while (1) {
        // 获取设置的按键检测类型
        sync_button_type_cfg();
        //判断left和right是否同时长按
        if (READ_BIT(phyButtonStatus_, LEFT_LONG_PRESS_STATUS) && READ_BIT(phyButtonStatus_, RIGHT_LONG_PRESS_STATUS)) {
            CLEAR_BIT(phyButtonStatus_, LEFT_LONG_PRESS_STATUS);
            CLEAR_BIT(phyButtonStatus_, RIGHT_LONG_PRESS_STATUS);
            SetSewageStatus(SEWAGE_SWITCH_OPEN);
        }

        //排污机构状态机
        switch (sewageSwitch_.status) {
            case SEWAGE_SWITCH_IDLE:
                break;
            case SEWAGE_SWITCH_OPEN:
                ctrl_elevator_sewage();
                SetSewageTime(SEWAGE_TIMEOUT);
                SetSewageStatus(SEWAGE_SWITCH_RUNNING);
                //播放语音
                robot_event_publish("sewage_switch_open");
                break;
            case SEWAGE_SWITCH_RUNNING:
                SetSewageTime(sewageSwitch_.timeout - BUTTON_TICK);
                if (sewageSwitch_.timeout <= 0) {
                    SetSewageStatus(SEWAGE_SWITCH_CLOSE);
                }
                break;
            case SEWAGE_SWITCH_CLOSE:
                ctrl_elevator_up();
                SetSewageTime(0);
                SetSewageStatus(SEWAGE_SWITCH_IDLE);
                //播放语音
                robot_event_publish("sewage_switch_close");
                break;
        }

        osDelay(BUTTON_TICK);
    }

    return;
}

void sync_button_type_cfg(void) {
    static button_type_e previous_button_type     = BUTTON_TYPE_UNKNOWN;
    char                 temp_button_type_str[32] = {0};
    static uint32_t      last_check_time          = 0;
    uint32_t             current_time             = osKernelGetTickCount();

    // 每10秒检查一次按键类型配置
    if (current_time - last_check_time < 10000) {
        return;
    }

    last_check_time = current_time;

    log_d("periodic check button type cfg");

    button_cfg_get_ret = ask_cfg("hardware.key.interface", temp_button_type_str);

    button_type_e temp_button_type;
    if (button_cfg_get_ret == 0) {
        log_i("get button type cfg success, type:%s", temp_button_type_str);
        temp_button_type = button_type_str_to_enum(temp_button_type_str);
    } else if (button_cfg_get_ret == -2) {
        log_i("no button type cfg, use default value");
        temp_button_type = is_tall_version ? BUTTON_TYPE_IIC : BUTTON_TYPE_DIRECT;
    } else {
        log_w("get button type cfg failed, ret:%d", button_cfg_get_ret);
        temp_button_type = is_tall_version ? BUTTON_TYPE_IIC : BUTTON_TYPE_DIRECT;
    }

    if (button_cfg_get_ret != 0 && button_cfg_get_ret != -2) {
        log_e("get button type cfg failed, using default value");
        temp_button_type = is_tall_version ? BUTTON_TYPE_IIC : BUTTON_TYPE_DIRECT;
    }

    if (temp_button_type == BUTTON_TYPE_AUTO) {
        temp_button_type = is_tall_version ? BUTTON_TYPE_IIC : BUTTON_TYPE_DIRECT;
    }

    if (previous_button_type == temp_button_type) {
        log_d("button type unchanged: %s", button_type_enum_to_str(temp_button_type));
        return;
    }

    log_w("button type changed from '%s' to '%s', reinitializing", button_type_enum_to_str(previous_button_type),
          button_type_enum_to_str(temp_button_type));

    button_type          = temp_button_type;
    previous_button_type = temp_button_type;

    switch (button_type) {
        case BUTTON_TYPE_IIC:
            log_i("button type is iic");
            phy_button_attr[0].gpio_info         = &bus_gpio_tall_button_right;
            phy_button_attr[0].button_level_read = AW9523_GpioRead;
            phy_button_attach_attr[0].gpio_info  = &bus_gpio_tall_button_right;

            phy_button_attr[1].gpio_info         = &bus_gpio_tall_button_left;
            phy_button_attr[1].button_level_read = AW9523_GpioRead;
            phy_button_attach_attr[1].gpio_info  = &bus_gpio_tall_button_left;
            break;

        case BUTTON_TYPE_DIRECT:
            log_i("button type is direct");
            phy_button_attr[0].gpio_info        = &bus_gpio_button_right;
            phy_button_attach_attr[0].gpio_info = &bus_gpio_button_right;

            phy_button_attr[1].gpio_info        = &bus_gpio_button_left;
            phy_button_attach_attr[1].gpio_info = &bus_gpio_button_left;
            break;

        case BUTTON_TYPE_DIRECT2:
            log_i("button type is direct2");
            phy_button_attr[0].gpio_info        = &bus_gpio_tall_button_right_v2;
            phy_button_attach_attr[0].gpio_info = &bus_gpio_tall_button_right_v2;

            phy_button_attr[1].gpio_info        = &bus_gpio_tall_button_left_v2;
            phy_button_attach_attr[1].gpio_info = &bus_gpio_tall_button_left_v2;
            break;

        default:
            log_e("unknown button type: %s", button_type_enum_to_str(button_type));
            return;
    }

    device_ioctl(button_handle, BUTTON_CMD_SET_ADD_BUTTON, (void *) &phy_button_attr[0]);
    device_ioctl(button_handle, BUTTON_CMD_SET_ADD_ATTACH, (void *) &phy_button_attach_attr[0]);
    device_ioctl(button_handle, BUTTON_CMD_SET_ADD_BUTTON, (void *) &phy_button_attr[1]);
    device_ioctl(button_handle, BUTTON_CMD_SET_ADD_ATTACH, (void *) &phy_button_attach_attr[1]);
}

void SetSewageStatus(SEWAGE_SWITCH_STATUS status) {
    if (status == SEWAGE_SWITCH_CLOSE) {
        if (sewageSwitch_.status == SEWAGE_SWITCH_OPEN || sewageSwitch_.status == SEWAGE_SWITCH_RUNNING) {
            sewageSwitch_.status = status;
        } else {
            //手动清洗污水箱模式未打开 不做关闭处理
        }
    } else {
        sewageSwitch_.status = status;
    }
}

void SetSewageTime(int32_t time) {
    sewageSwitch_.timeout = time;
}

bool get_is_tall_version(void) {
    return is_tall_version;
}

int phy_button_attr_init(void) {
    osThreadId_t longPressButtons = osThreadNew(longPressOpenSewageRun, NULL, &longPressButtonsAttributes);
    return 0;
}
FAL_MODULE_INIT(phy_button_attr_init);

#ifdef __cplusplus
}
#endif