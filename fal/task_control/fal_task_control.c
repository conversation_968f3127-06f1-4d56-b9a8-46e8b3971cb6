#define LOG_TAG "fal_taskcontrol"
#include "log.h"
#include "fal_task_control.h"
#include "fal_security.h"
#include "fal_charge.h"
#include "cmsis_os.h"
#include "shell.h"
#include "devices.h"
#include "string.h"
#include "pubsub.h"
#include "mem_pool.h"
#include "pal_task_control.h"
#include "define_cliff.h"
#include "pal_cliff.h"
#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 私有宏定义
 ******************************************************************/
#define CLIFF_STOP_TIME 2000
/*****************************************************************
 * 私有结构体/共用体/枚举定义
 ******************************************************************/

/*****************************************************************
 * 全局变量定义
 ******************************************************************/
const osThreadAttr_t task_control_attributes = {.name = "task_control", .priority = (osPriority_t) osPriorityNormal, .stack_size = 256 * 4};

char work_status[32];
char map_status[32];
char navigation_state[32];
/*****************************************************************
 * 私有全局变量定义
 ******************************************************************/
/*****************************************************************
 * 外部变量声明（如果全局变量没有在其它的H文件声明，引用时需在此处声明，
 *如果已在其它H文件声明，则只需包含此H文件即可）
 ******************************************************************/
extern int side_brush_handle;
/*****************************************************************
 * 私有函数原型声明
 ******************************************************************/
static void task_control_run(void *argument);
/*****************************************************************
 * 函数定义
 *注意，编写函数需首先定义所有的局部变量等，不允许在
 *函数的中间出现新的变量的定义。
 ******************************************************************/
bool is_automove;
void move_and_sbrun_callback(const MOTOR_DATA *data, uint32_t size) {
    if ((NULL == data) || (size < 2 * sizeof(MOTOR_DATA))) {
        return;
    }
    log_d("work_status : %s", work_status);
    log_d("map_status  : %s", map_status);
    log_d("is_automove : %d", is_automove);
    //行走时边刷保持最低转速转动
    int min_rpm = 100;
    //停止速度
    int stop_rpm = 0;
    //下发的速度
    int rpm = (int) data->rpm;
    //若工作状态不为idle且不是暂停 或者 处于自动构图，机器处于自动行走，开启断崖和边刷
    if (((strcmp(work_status, "idle") && strcmp(navigation_state, "pausing")) || (!strcmp(map_status, "auto_mapping"))) &&
        ((get_charge_feedback() == CHARGE_ELEC_UP) || (get_charge_feedback() == CHARGE_ELEC_UNKNOWN))) {
        if (rpm == 0) {
            //若下发速度为0 则以最低速运行
            // device_ioctl(side_brush_handle, MOTOR_CMD_SET_RPM, (void *) &min_rpm);
        } else {
            //若下发速度为非0 则以该速运行
            // device_ioctl(side_brush_handle, MOTOR_CMD_SET_RPM, (void *) &rpm);
        }
        //机器自动行走
        is_automove = true;
    } else {
        //若上一次机器处于自动行走
        if (is_automove) {
            // device_ioctl(side_brush_handle, MOTOR_CMD_SET_RPM, (void *) &stop_rpm);
            //机器不处于自动行走
            is_automove = false;
        } else {
            //置为实际下发速度
            // device_ioctl(side_brush_handle, MOTOR_CMD_SET_RPM, (void *) &rpm);
        }
    }
}

void move_and_sbrun_init() {
    //注册回调函数到边刷驱动中
    MOTOR_DATA_CB_ARG *cb = (MOTOR_DATA_CB_ARG *) mem_block_alloc(sizeof(MOTOR_DATA_CB_ARG));
    cb->fn_callback       = move_and_sbrun_callback;
    cb->period            = 1000;
    device_ioctl(side_brush_handle, MOTOR_CMD_SET_DATA_CB, (void *) cb);
}

int fal_task_control_init(void) {
    osThreadNew(task_control_run, NULL, &task_control_attributes);
    move_and_sbrun_init();
    strcpy(work_status, "idle");
    strcpy(map_status, "idle");
    return 0;
}
FAL_MODULE_INIT(fal_task_control_init);

extern char emerg_type_str[32];

void task_control_run(void *argument) {
    ps_subscriber_t *s   = ps_new_subscriber(5, PS_STRLIST("work_status", "navigation_state", "map_status"));
    ps_msg_t *       msg = NULL;

    int cfg_get_ret = -1;

    uint8_t is_emerg_cfg_get = 0;

    osDelay(3000);

    while (1) {
        msg = ps_get(s, 10);
        if (msg != NULL) {
            log_d("%s recv:%s", msg->topic, msg->str_val);
            if (strcmp(msg->topic, "work_status") == 0) {
                if (PS_IS_STR(msg)) {
                    strcpy(work_status, msg->str_val);
                    log_i("change work_status to %s", work_status);
                }
            } else if (strcmp(msg->topic, "map_status") == 0) {
                if (PS_IS_STR(msg)) {
                    strcpy(map_status, msg->str_val);
                    log_i("change map_status to %s", map_status);
                }
            } else if (strcmp(msg->topic, "navigation_state") == 0) {
                if (PS_IS_STR(msg)) {
                    strcpy(navigation_state, msg->str_val);
                    log_i("change navigation_state to %s", navigation_state);
                }
            }
            ps_unref_msg(msg);
        }

        if (!is_emerg_cfg_get) {
            static uint32_t last_read_time = 0;
            uint32_t        current_time   = osKernelGetTickCount();

            // Only try reading every 3 seconds
            if (current_time - last_read_time >= 3000) {
                last_read_time = current_time;

                log_i("try read emerg_type config.");

                cfg_get_ret = ask_cfg("system.trigger_emerg_type", emerg_type_str);

                //读取成功不再主动读取，changed 接口可读取更新值
                if (cfg_get_ret == 0) {
                    is_emerg_cfg_get = 1;
                }

                //读取返回空值说明没有该配置项，不再读取，直接使用本地默认值。changed 接口可读取更新值
                if (cfg_get_ret == -2) {
                    is_emerg_cfg_get = 1;
                }
            }
        }
        // Read cliff sensor calibration values
        extern adc_sensor_obj_st cliff_sensor_objs[];
        extern int               cliff_handle;
        extern int               cliff_psd_handle1;
        static uint8_t           is_cliff_cfg_get = 0;
        // 如果断崖传感器类型为PSD，则读取PSD断崖传感器的校准值
        if (!is_cliff_cfg_get && cliff_handle == cliff_psd_handle1) {
            char            cliff_key[32];
            char            cliff_val[32];
            int             ret;
            uint8_t         all_success    = 1;
            static uint32_t last_read_time = 0;
            uint32_t        current_time   = osKernelGetTickCount();

            // Only try reading every 3 seconds
            if (current_time - last_read_time >= 3000) {
                last_read_time = current_time;

                log_i("try read psd_cali config.");

                // Read calibration values for all 6 cliff sensors
                for (int i = 1; i <= 6; i++) {
                    snprintf(cliff_key, sizeof(cliff_key), "sensors.psd_cali%d", i);
                    ret = ask_cfg(cliff_key, cliff_val);

                    if (ret == 0) {
                        // Find matching cliff sensor object
                        char cliff_name[32];
                        snprintf(cliff_name, sizeof(cliff_name), "cliff_oml%d", i);

                        for (int j = 0; j < CLIFF_MAX; j++) {
                            if (strcmp((const char *) cliff_sensor_objs[j].dev_name, cliff_name) == 0) {
                                cliff_sensor_objs[j].adc_vol_thres = atof(cliff_val);
                                device_ioctl(cliff_handle, CLIFF_CMD_SET_VOL_THRES, (void *) &cliff_sensor_objs[j]);
                                log_i("Init %s vol threshold to %.2f", cliff_name, cliff_sensor_objs[j].adc_vol_thres);
                                break;
                            }
                        }
                    } else if (ret == -2) {
                        log_i("No %s config, use default value", cliff_key);
                        // 没有此psd的配置项，不再读取
                        continue;
                    } else {
                        all_success = 0;
                    }
                }

                if (all_success) {
                    is_cliff_cfg_get = 1;
                }
            }
        }
    }
}

extern bool is_debug_always_pub_cliff;

void open_cliff_debug_mode(uint8_t debug) {
    if (debug == 0) {
        security_stop(CLIFF_BIT);
        is_debug_always_pub_cliff = false;
    } else {
        security_start(CLIFF_BIT);
        is_debug_always_pub_cliff = true;
    }
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC),open_cliff_debug_mode, open_cliff_debug_mode, open_cliff_debug_mode for default parameter);

#ifdef __cplusplus
}
#endif