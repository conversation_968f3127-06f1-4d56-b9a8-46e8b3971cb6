#define LOG_TAG "fal_workstation"
#include "log.h"
#include "fal_workstation.h"
#include "pal_workstation.h"
#include "fal.h"
#include "fal_motor.h"
#include "fal_led.h"
#include "fal_clean.h"
#include "fal_security.h"
#include "water_position_scw.h"
#include "cmsis_os.h"
#include "shell.h"
#include "utils_tick.h"
#include "mem_pool.h"
#include "pal_log.h"
#include "define.h"
#include <math.h>

extern int   roller_tube_handle;
extern int   clean_water_handle;
extern int   clean_water_pump_handle;
extern int   sewage_water_pump_handle;
extern int   fan_motor_handle;
extern int   ruller_tube_rpm;
extern float module_table[MOTOR_MAX][MOTOR_INFO_TYPE_MAX];

extern WATER_STATE_E sewage_box_state;
extern int           sewage_check_state;
extern volatile bool water_state_pub_flag;
extern volatile bool dust_state_pub_flag;
extern DUST_STATE_E  dust_curr_state;

extern bool sewage_water_status_info_pub(WATER_STATE_E water_state);
extern bool clean_water_status_info_pub(WATER_STATE_E water_state);

WATER_STATE_E get_clean_water_state(void);
WATER_STATE_E get_sewage_water_state(void);

cycle_led_t tasking_led;
/*macro*************************************************************/
#define SEWAGE_BOX_AD_LIMIT             250
#define TICK_CNT                        4
#define CLEAN_WATER_STATE_PREVENT_SHAKE (20)  //清水水位检测消抖次数（CLEAN_WATER_STATE_PREVENT_SHAKE * 100ms）
#define HAVE_WATER                      0     //清水位检测点有水
#define NO_WATER                        1     //清水位检测点无水

#define SEWAGE_WATER_TIMEOUT       (2.5 * 60)  //排污超时时间
#define SEWAGE_WATER_DELAY_SECONDS (3)         //工作站空抽时间
#define SEWAGE_WATER_EMPTY_TIME    (10)        //判断污水空时间

#define SELF_CLEAN_TIMEOUT         (5 * 60)  //自清洁超时时间
#define SELF_CLEAN_PUMB_STOP_TIME  (25)      //自清洁清水泵停止时间
#define SELF_CLEAN_PUMB_CYCLE_TIME (40)      //自清洁清水泵循环时间
#define SEWAGE_ERROR_JUDGE_START   (6)       //抽污异常开始判断时间
#define SEWAGE_ERROR_JUDGE_TIME    (105)     //抽污异常结束判断时间
#define SEWAGE_ERROR_TICK          (12)      //抽污异常时间

#define DRYING_TIMEOUT   (2 * 60 * 60)  //烘干超时时间
#define DRYING_TURN_TIME (20 * 60)      //烘干翻面时间

#define PITCH_THREADHOLD (12.0)
#define ROLL_THREADHOLD  (12.0)

#define WATER_SENSOR_NORMAL_PERIOD         (60000)            //默认水位发布周期
#define WATER_SENSOR_FAST_PERIOD           (1000)             //空或满水位发布周期
#define WATER_SENSOR_EMPTY_LOG_SEND_PERIOD (4.5 * 60 * 1000)  //清水箱空log发布周期
#define WATER_SENSOR_REPEAT_SEND_TIMES     10                 // 水量更新后重复发布次数

typedef struct {
    uint32_t run_tick_second;
    uint32_t run_start;
} WorkStation_Tick;

typedef struct {
    WC_ERROR_CODE       watercontrol_error_code;
    uint8_t             pile_heartbeat;
    WATER_STATE_E       c_water_state;
    WATER_STATE_E       s_water_state;
    uint32_t            c_water_tick;
    uint32_t            s_water_tick;
    uint32_t            c_water_empty_tick;
    WATER_SENSOR_DATA_T clean_water_state;
    WATER_SENSOR_DATA_T last_clean_water_state;
    WATER_STATE_E       last_sewage_water_state;
    WorkStation_Tick    add_clean_water;
    WorkStation_Tick    sewage_water;
    WorkStation_Tick    rolltube_selfclean;
    WorkStation_Tick    drying;
} WorkStation_ST;

WorkStation_ST workstation_control;

const osThreadAttr_t wrokstation_attributes = {.name = "workstation", .priority = (osPriority_t) osPriorityNormal, .stack_size = 384 * 4};
const osThreadAttr_t motor_ad_attributes = {.name = "sewage_motor_ad_run", .priority = (osPriority_t) osPriorityNormal, .stack_size = 384 * 4};

void update_clean_water_state(void);
void workstation_control_process(void);

void task_workstation_run(void *argument);
void update_sewage_water_full_run(void *argument);
void update_dust_state_run(void);

int fal_workstation_init(void) {
    osThreadNew(task_workstation_run, NULL, &wrokstation_attributes);
    osThreadNew(update_sewage_water_full_run, NULL, &motor_ad_attributes);
    return 0;
}
FAL_MODULE_INIT(fal_workstation_init);

void task_workstation_run(void *argument) {
    uint32_t process_time                          = 0;
    workstation_control.c_water_state              = WATER_POSITION_PER66;
    workstation_control.s_water_state              = WATER_POSITION_EMPTY;
    workstation_control.last_sewage_water_state    = WATER_POSITION_EMPTY;
    workstation_control.last_clean_water_state.ch1 = NO_WATER;
    workstation_control.last_clean_water_state.ch2 = HAVE_WATER;
    workstation_control.last_clean_water_state.ch3 = HAVE_WATER;
    workstation_control.last_clean_water_state.ch4 = HAVE_WATER;

    while (1) {
        osDelay(100);
        update_clean_water_state();
        update_dust_state_run();
        if (is_timeout(process_time, 1000)) {
            workstation_control_process();
            process_time = osKernelGetTickCount();
        }

        if (water_state_pub_flag) {
            water_state_pub_flag = false;

            clean_water_status_info_pub(get_clean_water_state());
            osDelay(10);
            sewage_water_status_info_pub(get_sewage_water_state());
            osDelay(10);
            clean_water_sensor_status_info_pub();
        }

        if (dust_state_pub_flag) {
            dust_state_pub_flag = false;
            send_dust_state(&dust_curr_state);
        }
    }
}

void sewage_water_proces(void) {
    static int speed_0 = 0;
    static int speed_1 = 1;

    static bool last_run_start = false;
    if (last_run_start != workstation_control.sewage_water.run_start) {
        last_run_start = workstation_control.sewage_water.run_start;
        if (!workstation_control.sewage_water.run_start) {
            log_i("sewage_water end, ctrl_elevator_up and sewage_water_pump work");
            ctrl_elevator_up();
        } else {
            log_i("sewage_water start");
        }
    }

    if (!workstation_control.sewage_water.run_start) {
        workstation_control.sewage_water.run_tick_second = 0;
        return;
    }

    if (workstation_control.sewage_water.run_tick_second == SEWAGE_WATER_DELAY_SECONDS) {
        log_i("sewage_water delay %d ctrl_elevator_sewage", workstation_control.sewage_water.run_tick_second);
        ctrl_elevator_sewage();
    }

    if (workstation_control.sewage_water.run_tick_second >= SEWAGE_WATER_EMPTY_TIME && sewage_box_state == WATER_POSITION_EMPTY) {
        log_i("sewage water is empty!!");
        workstation_control.s_water_state = WATER_POSITION_EMPTY;
    }

    if (workstation_control.sewage_water.run_tick_second++ >= SEWAGE_WATER_TIMEOUT) {
        workstation_control.sewage_water.run_start = false;
    }
}

void stop_rolltube_selfclean_motor(void) {
    int speed_0 = 0;
    device_ioctl(roller_tube_handle, MOTOR_CMD_SET_RPM, (void *) &speed_0);
    device_ioctl(clean_water_pump_handle, MOTOR_CMD_SET_RPM, (void *) &speed_0);
    device_ioctl(sewage_water_pump_handle, MOTOR_CMD_SET_RPM, (void *) &speed_0);
}

void sewage_error_handle(uint8_t *sewage_error_cnt) {
    int  speed_0             = 0;
    bool is_sewage_tank_full = Is_have_sewage_tank_detect() ? get_sewage_tank_full_state() : false;
    bool error_condition     = false;

    // 判断是否满足错误条件
    if (!Is_have_sewage_tank_detect()) {
        if ((workstation_control.rolltube_selfclean.run_tick_second % SELF_CLEAN_PUMB_CYCLE_TIME < SELF_CLEAN_PUMB_STOP_TIME) &&
            (workstation_control.rolltube_selfclean.run_tick_second >= SEWAGE_ERROR_JUDGE_START) &&
            (workstation_control.rolltube_selfclean.run_tick_second <= SEWAGE_ERROR_JUDGE_TIME) &&
            (workstation_control.c_water_state > WATER_POSITION_PER15) && (sewage_check_state == 0)) {
            error_condition = true;
        }
    } else if (is_sewage_tank_full) {
        error_condition = true;
    }

    // 处理错误条件
    if (error_condition) {
        if ((*sewage_error_cnt)++ > SEWAGE_ERROR_TICK) {
            log_w("sewage water suck up error !");
            pal_log_pub("error", "hardware/sewage_check", "抽污超时异常");
            workstation_error_pub("rolltube_selfclean");
            stop_rolltube_selfclean_motor();
            workstation_control.rolltube_selfclean.run_start = false;
        }
    } else {
        *sewage_error_cnt = 0;
    }
}

void control_clean_water_pump_cycle(int speed_0, int speed_1) {
    bool     sewage_tank_full = Is_have_sewage_tank_detect() ? get_sewage_tank_full_state() : false;
    uint32_t tick             = workstation_control.rolltube_selfclean.run_tick_second % SELF_CLEAN_PUMB_CYCLE_TIME;

    if (sewage_tank_full) {
        device_ioctl(clean_water_pump_handle, MOTOR_CMD_SET_RPM, &speed_0);
    }

    if (tick == SELF_CLEAN_PUMB_STOP_TIME) {
        log_i("rolltube_selfclean clean_water_pump stop");
        device_ioctl(clean_water_pump_handle, MOTOR_CMD_SET_RPM, &speed_0);
    } else if (tick == 0 && !sewage_tank_full) {
        log_i("rolltube_selfclean clean_water_pump restart");
        device_ioctl(clean_water_pump_handle, MOTOR_CMD_SET_RPM, &speed_1);
    }
}

void rolltube_selfclean_proces(void) {
    static int     sewage_water_pump_work_second = 0;
    static uint8_t sewage_error_cnt              = 0;
    static bool    last_run_start                = false;

    int ruller_tube_rpm = 50;
    int speed_0         = 0;
    int speed_1         = 1;

    bool is_start            = workstation_control.rolltube_selfclean.run_start;
    bool is_sewage_tank_full = Is_have_sewage_tank_detect() ? get_sewage_tank_full_state() : false;

    // 检测启动状态变化
    if (last_run_start != is_start) {
        last_run_start = is_start;
        log_i(is_start ? "rolltube_selfclean start" : "rolltube_selfclean end");
        if (is_start) {
            sewage_error_cnt = 0;
            ctrl_elevator_up();
            device_ioctl(roller_tube_handle, MOTOR_CMD_SET_RPM, &ruller_tube_rpm);
            device_ioctl(sewage_water_pump_handle, MOTOR_CMD_SET_RPM, &speed_1);
            device_ioctl(clean_water_pump_handle, MOTOR_CMD_SET_RPM, is_sewage_tank_full ? &speed_0 : &speed_1);
        } else {
            stop_rolltube_selfclean_motor();
        }
    }

    if (!is_start) {
        workstation_control.rolltube_selfclean.run_tick_second = 0;
        return;
    }

    // 抽污异常检测及处理
    sewage_error_handle(&sewage_error_cnt);

    // 控制清水泵周期性启停
    control_clean_water_pump_cycle(speed_0, speed_1);

    if (workstation_control.rolltube_selfclean.run_tick_second++ >= SELF_CLEAN_TIMEOUT) {
        workstation_control.rolltube_selfclean.run_start = false;
    }
}

void drying_proces(void) {
    if (!workstation_control.drying.run_start) {
        workstation_control.drying.run_tick_second = 0;
        return;
    }

    if (workstation_control.drying.run_tick_second == 0) {
        log_i("drying start , ruller_tube turn 360");
        ruller_tube_rpm = -10;
        ruller_tube_run(ruller_tube_rpm, ROLLER_TUBE_1RUN_360ANGLE_TIME);
    }

    if (workstation_control.drying.run_tick_second % DRYING_TURN_TIME == 0) {
        log_i("drying , ruller_tube turn 90");
        ruller_tube_rpm = -10;
        ruller_tube_run(ruller_tube_rpm, ROLLER_TUBE_1RUN_90ANGLE_TIME);
    }

    if (workstation_control.drying.run_tick_second++ >= DRYING_TIMEOUT) {
        workstation_control.drying.run_start = false;
    }
}

void workstation_control_process(void) {
    sewage_water_proces();
    rolltube_selfclean_proces();
    drying_proces();
}

//污水满检测
void update_sewage_water_full_run(void *argument) {
    uint16_t           warn_cnt                           = 0;
    uint16_t           nomal_cnt                          = 0;
    uint8_t            last_open_flag                     = 0;
    static uint8_t     repeat_send_cnt                    = 0;
    MOTOR_CURRENT_INFO motor_common_current_info          = {0};
    float              motor_common_current_filterd_value = 0.0;
    motor_common_current_info.filtered_current_value      = &motor_common_current_filterd_value;
    while (1) {
        motor_current_info_clear(&motor_common_current_info);
        device_ioctl(sewage_water_pump_handle, MOTOR_CMD_GET_CURRENT_FILTERED_VALUE, &motor_common_current_info);
        if (motor_common_current_info.open_flag == 1) {
            float cur_sewage = *motor_common_current_info.filtered_current_value *
                               module_table[MOTOR_SECURITY_SEWAGE_WATER_PUMP][MOTOR_INFO_REAL_CURRENT_RATIO];
            float cur_gain_sewage = cur_sewage * 1000;
            log_d("cur_sewage %.2f cur_gain_sewage %.2f", cur_sewage, cur_gain_sewage);
            if (cur_gain_sewage > SEWAGE_BOX_AD_LIMIT) {
                if (last_open_flag != 0) {
                    warn_cnt++;
                }
                if (warn_cnt >= TICK_CNT) {
                    nomal_cnt                         = 0;
                    warn_cnt                          = 0;
                    workstation_control.s_water_state = WATER_POSITION_FULL;
                    log_d("sewage_water state is full!");
                }
            } else {
                nomal_cnt++;
                if (nomal_cnt >= TICK_CNT) {
                    nomal_cnt                         = 0;
                    warn_cnt                          = 0;
                    workstation_control.s_water_state = WATER_POSITION_NORMAL;
                    log_d("sewage_water state is nomal!");
                }
            }
        }
        last_open_flag = motor_common_current_info.open_flag;
        if (workstation_control.last_sewage_water_state != workstation_control.s_water_state) {
            repeat_send_cnt = WATER_SENSOR_REPEAT_SEND_TIMES;
            if (send_sewage_water_state(&workstation_control.s_water_state)) {
                if (get_sewage_water_state() == WATER_POSITION_FULL) {
                    pal_log_pub("warn", "software/sewage_water_full", "污水箱满");
                }
                workstation_control.last_sewage_water_state = workstation_control.s_water_state;
            }
        }
        if (repeat_send_cnt) {
            if (is_timeout(workstation_control.s_water_tick, WATER_SENSOR_FAST_PERIOD)) {
                send_sewage_water_state(&workstation_control.s_water_state);
                workstation_control.s_water_tick = osKernelGetTickCount();
                --repeat_send_cnt;
            }
        }

        if (is_timeout(workstation_control.s_water_tick, WATER_SENSOR_NORMAL_PERIOD)) {
            send_sewage_water_state(&workstation_control.s_water_state);
            workstation_control.s_water_tick = osKernelGetTickCount();
        }

        osDelay(500);
    }
}

WATER_STATE_E get_clean_water_state(void) {
    return workstation_control.c_water_state;
}

WATER_STATE_E get_sewage_water_state(void) {
    return workstation_control.s_water_state;
}

void update_dust_state_run(void) {
    static DUST_STATE_E last_state = DUST_POSITION_EMPTY;
    uint8_t             open_state = 0;
    uint8_t             motor_num  = 0;
    uint8_t             tick       = 3;

    open_state = device_ioctl(fan_motor_handle, MOTOR_CMD_GET_OPEN_STATE, &motor_num);

    if (open_state == 1 && (dust_curr_state == DUST_POSITION_EMPTY)) {
        dust_curr_state = DUST_POSITION_NORMAL;
        log_i("dust_box_status:Have Dust");
        pal_log_pub("info", "software/dust_state", "尘盒当前状态:有尘");
        for (int i = 0; i < tick; i++) {  // 发送多次防止丢包
            send_dust_state(&dust_curr_state);
            osDelay(100);
        }
    } else if (open_state == 0) {
        if (dust_curr_state != last_state && (dust_curr_state == DUST_POSITION_EMPTY)) {
            pal_log_pub("info", "software/dust_state", "尘盒当前状态:空");
            log_i("dust_box_status:No Dust");
        }
    } else {
        //
    }

    last_state = dust_curr_state;
}

void clean_data_handler(void) {
    uint8_t        update_flag       = 0;  // pub更新水位信息标志
    static uint8_t repeat_send_cnt   = 0;  // 水位更新后重复发送次数
    static uint8_t have_water_cnt[4] = {0, 0, 0, 0};
    static uint8_t no_water_cnt[4]   = {0, 0, 0, 0};

    if (HAVE_WATER == workstation_control.clean_water_state.ch4) {
        no_water_cnt[0] = 0;

        if (++have_water_cnt[0] >= CLEAN_WATER_STATE_PREVENT_SHAKE) {
            if (workstation_control.c_water_state < WATER_POSITION_PER15) {
                update_flag                       = 1;
                workstation_control.c_water_state = WATER_POSITION_PER15;
                log_i("clean water position percent = 15! workstation_control.water_state = %d", workstation_control.c_water_state);
            }
            have_water_cnt[0] = 0;
            // workstation_control.last_clean_water_state.ch4 = workstation_control.clean_water_state.ch4;
        }

    } else {
        have_water_cnt[0] = 0;
        if (++no_water_cnt[0] >= CLEAN_WATER_STATE_PREVENT_SHAKE) {
            if (workstation_control.c_water_state > WATER_POSITION_EMPTY) {
                update_flag                       = 1;
                workstation_control.c_water_state = WATER_POSITION_EMPTY;
                log_i("clean water position percent = 0!  workstation_control.water_state = %d", workstation_control.c_water_state);
            }
            no_water_cnt[0] = 0;
            // workstation_control.last_clean_water_state.ch4 = workstation_control.clean_water_state.ch4;
        }
    }

    if (HAVE_WATER == workstation_control.clean_water_state.ch3) {
        no_water_cnt[1] = 0;
        if (++have_water_cnt[1] >= CLEAN_WATER_STATE_PREVENT_SHAKE) {
            if (workstation_control.c_water_state < WATER_POSITION_PER33) {
                update_flag                       = 1;
                workstation_control.c_water_state = WATER_POSITION_PER33;
                log_i("clean water position percent = 33! workstation_control.water_state = %d", workstation_control.c_water_state);
            }
            have_water_cnt[1] = 0;
            // workstation_control.last_clean_water_state.ch3 = workstation_control.clean_water_state.ch3;
        }
    } else {
        have_water_cnt[1] = 0;
        if (++no_water_cnt[1] >= CLEAN_WATER_STATE_PREVENT_SHAKE) {
            if (workstation_control.c_water_state > WATER_POSITION_PER15) {
                update_flag                       = 1;
                workstation_control.c_water_state = WATER_POSITION_PER15;
                log_i("clean water position percent = 15!   workstation_control.water_state = %d", workstation_control.c_water_state);
            }
            no_water_cnt[1] = 0;
            // workstation_control.last_clean_water_state.ch3 = workstation_control.clean_water_state.ch3;
        }
    }

    if (HAVE_WATER == workstation_control.clean_water_state.ch2) {
        no_water_cnt[2] = 0;
        if (++have_water_cnt[2] >= CLEAN_WATER_STATE_PREVENT_SHAKE) {
            if (workstation_control.c_water_state < WATER_POSITION_PER66) {
                update_flag                       = 1;
                workstation_control.c_water_state = WATER_POSITION_PER66;
                log_i("clean water position percent = 66!  workstation_control.water_state = %d", workstation_control.c_water_state);
            }
            have_water_cnt[2] = 0;
            // workstation_control.last_clean_water_state.ch2 = workstation_control.clean_water_state.ch2;
        }
    } else {
        have_water_cnt[2] = 0;
        if (++no_water_cnt[2] >= CLEAN_WATER_STATE_PREVENT_SHAKE) {
            if (workstation_control.c_water_state > WATER_POSITION_PER33) {
                update_flag                       = 1;
                workstation_control.c_water_state = WATER_POSITION_PER33;
                log_i("clean water position percent = 33!   workstation_control.water_state = %d", workstation_control.c_water_state);
            }
            no_water_cnt[2] = 0;
            // workstation_control.last_clean_water_state.ch2 = workstation_control.clean_water_state.ch2;
        }
    }

    if (HAVE_WATER == workstation_control.clean_water_state.ch1) {
        no_water_cnt[3] = 0;
        if (++have_water_cnt[3] >= CLEAN_WATER_STATE_PREVENT_SHAKE) {
            if (workstation_control.c_water_state < WATER_POSITION_FULL) {
                update_flag                       = 1;
                workstation_control.c_water_state = WATER_POSITION_FULL;
                log_i("clean water position percent = 100! workstation_control.water_state = %d", workstation_control.c_water_state);
            }
            have_water_cnt[3] = 0;
            // workstation_control.last_clean_water_state.ch1 = workstation_control.clean_water_state.ch1;
        }
    } else {
        have_water_cnt[3] = 0;
        if (++no_water_cnt[3] >= CLEAN_WATER_STATE_PREVENT_SHAKE) {
            if (workstation_control.c_water_state > WATER_POSITION_PER66) {
                update_flag                       = 1;
                workstation_control.c_water_state = WATER_POSITION_PER66;
                log_i("clean water position percent = 66!   workstation_control.water_state = %d ", workstation_control.c_water_state);
            }
            no_water_cnt[3] = 0;
            // workstation_control.last_clean_water_state.ch1 = workstation_control.clean_water_state.ch1;
        }
    }

    // pub水位信息
    if (update_flag) {
        repeat_send_cnt = WATER_SENSOR_REPEAT_SEND_TIMES;
        send_clean_water_state(&workstation_control.c_water_state);
        if (get_clean_water_state() == WATER_POSITION_EMPTY &&
            is_timeout(workstation_control.c_water_empty_tick, WATER_SENSOR_EMPTY_LOG_SEND_PERIOD)) {
            pal_log_pub("warn", "software/clean_water_empty", "清水箱空");
            workstation_control.c_water_empty_tick = osKernelGetTickCount();
        }
    }

    if (repeat_send_cnt) {
        if (is_timeout(workstation_control.c_water_tick, WATER_SENSOR_FAST_PERIOD)) {
            send_clean_water_state(&workstation_control.c_water_state);
            workstation_control.c_water_tick = osKernelGetTickCount();
            --repeat_send_cnt;
        }
    }

    if (is_timeout(workstation_control.c_water_tick, WATER_SENSOR_NORMAL_PERIOD)) {
        send_clean_water_state(&workstation_control.c_water_state);
        workstation_control.c_water_tick = osKernelGetTickCount();
    }
}

void update_clean_water_state(void) {
    static uint8_t data = 0;

    euler_angles_t euler_angles = {0};

    // 获取车身姿态角
    int pose_get_ret = get_pose_euler_angle(&euler_angles);

    device_ioctl(clean_water_handle, WATER_POSITION_IOCTL_GET_DATA, (void *) &workstation_control.clean_water_state);
    log_d("workstation_control.clean_water_state = %d,   full ch1 %d, %d,    ch2 %d, %d,  ch3 %d, %d,   empty ch4 %d, %d, state = %d",
          workstation_control.clean_water_state, workstation_control.clean_water_state.ch1, workstation_control.clean_water_state.ch1_reverse,
          workstation_control.clean_water_state.ch2, workstation_control.clean_water_state.ch2_reverse,
          workstation_control.clean_water_state.ch3, workstation_control.clean_water_state.ch3_reverse,
          workstation_control.clean_water_state.ch4, workstation_control.clean_water_state.ch4_reverse, workstation_control.c_water_state);
    memcpy(&data, &workstation_control.clean_water_state, sizeof(uint8_t));

    // 车身倾斜角度超過閾值時，舍弃清水量值为0的数据
    if (data == CLEAN_WATER_SENSOR_EMPTY_DATA && pose_get_ret == 0 &&
        (fabs(euler_angles.pitch) >= PITCH_THREADHOLD || fabs(euler_angles.roll) >= ROLL_THREADHOLD)) {
        log_w("Pose abnormal, pitch[%.2f], roll[%.2f].", euler_angles.pitch, euler_angles.roll);

        return;
    }

    if (data != CLEAN_WATER_SENSOR_EXCEPTION_DATA) {
        clean_data_handler();
    }
}

void set_pile_heartbeat(uint8_t i) {
    workstation_control.pile_heartbeat = i;
}

static bool is_lose_heartbeat(uint8_t over_times) {
    static uint8_t last_pile_hearbeat;
    static uint8_t cur_pile_heartbeat;
    static uint8_t overtime = 0;
    last_pile_hearbeat      = cur_pile_heartbeat;
    cur_pile_heartbeat      = workstation_control.pile_heartbeat;
    if (last_pile_hearbeat == cur_pile_heartbeat) {
        overtime++;
    } else {
        overtime = 0;
    }
    if (overtime > over_times) {
        return true;
    }
    return false;
}

bool get_sewage_water_control(void) {
    if (workstation_control.sewage_water.run_start) {
        return true;
    }
    return false;
}

WC_ERROR_CODE get_water_control_error_code(void) {
    return workstation_control.watercontrol_error_code;
}

void add_clean_water_control(bool is_open) {
    workstation_control.add_clean_water.run_start = is_open;
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), add_clean_water_control, add_clean_water_control,
                 add_clean_water_control);

void sewage_water_control(bool is_open) {
    workstation_control.sewage_water.run_start = is_open;
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), sewage_water_control, sewage_water_control,
                 sewage_water_control);

void rolltube_selfclean_control(bool is_open) {
    workstation_control.rolltube_selfclean.run_start = is_open;
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), rolltube_selfclean_control, rolltube_selfclean_control,
                 rolltube_selfclean_control);

void drying_control(bool is_open) {
    workstation_control.drying.run_start = is_open;
    log_w("dry control recept: %d", is_open);
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), drying_control, drying_control, drying_control);