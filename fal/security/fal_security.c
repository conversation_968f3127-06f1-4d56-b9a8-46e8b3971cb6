/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:
 ** Author:         曾曼云
 ** Version:        V0.0.1
 ** Date:           2021-3-25
 ** Description:		microROS安全管理
 ** Others:
 ** Function List:
 ** History:        2021-11 曾曼云 创建
 ** <time>          <author>    <version >    <desc>
 ** 2021-3-25       曾曼云						1.0         创建文件
 ******************************************************************/

/*****************************************************************
 * 包含头文件
 ******************************************************************/
#define LOG_TAG "fal_security"
#include "log.h"
#include "pal_security.h"
#include "fal.h"
#include "devices.h"
#include "define.h"
#include <stdarg.h>
#include <stdio.h>
#include <string.h>
#include "define_button.h"
#include "std_msgs/msg/bool.h"
#include "pal_zbus.h"
#include "define_motor.h"
#include "fal_security.h"
#include "mem_pool.h"
#include "pal_log.h"
#include "shell.h"
#include "fal_charge.h"
#include "fal_led.h"
#include "fal_button.h"
#include "utils_tick.h"
#include "cliff_oml.h"
#include "hal.h"
#include "pal_cliff.h"
#include "define_cliff.h"
#include "math.h"
#include "string.h"
#include "pubsub.h"
#include "fal_tall_led.h"
/**
 * @addtogroup Robot_PAL 协议适配层 - PAL
 * @{
 */

/**
 * @defgroup Robot_PAL_UROS microROS接口处理
 *
 * @brief
 * \n
 * @{
 */

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 私有宏定义
 ******************************************************************/

/*****************************************************************
 * 私有结构体/共用体/枚举定义
 ******************************************************************/
typedef struct security_button_callback_node {
    struct list_struct node;
    SECURITY_CB_T      security_cb_attr;
} SECUTIRT_CB_NODE_T, *SECUTIRT_CB_NODE_P;  //回调函数链表节点

typedef struct security_handle_attr {
    SECURITY_TYPE_E         security_type;
    security_happen_handle  happen_handle;
    security_release_handle release_handle;
} SECUTIRT_HANDLE_ATTR_T, *SECUTIRT_HANDLE_ATTR_P;  //回调函数链表节点

typedef enum {
    LEFT_SIDE_BRUSH,
    RIGHT_SIDE_BRUSH,
    ROLLER_BRUSH,
    ROLLER_TUBE,
    CLEAN_WATER_PUMP,
    SEWAGE_WATER_PUMP,
    LEFT_DRIVER_WHEEL,
    RIGHT_DRIVER_WHEEL,
} MOTOR_TYPE_E;
/*****************************************************************
 * 全局变量定义
 ******************************************************************/
const osThreadAttr_t security_attributes = {
    .name       = "security",
    .priority   = (osPriority_t) osPriorityNormal,
    .stack_size = 384 * 4,
};

struct list_struct security_pub_callback_list;
struct list_struct security_handle_list;
static uint32_t    wheel_timeout;
static MOTOR_SPEED wheel_speed;
static MOTOR_SPEED wheel_speed_cliff;
static MOTOR_SPEED wheel_speed_mcu;
static MOTOR_SPEED speed_slow              = {0};
static uint32_t    crash_release_last_time = 0;  //最后一次碰撞解除的时间
static uint32_t    crash_happen_flag       = 0;  //碰撞传感器触发标志

security_t           wheel;
BRAKE_ENABLE_STATE_E wheel_brake_enable_flag;

static uint32_t stop_start_time;
static uint32_t stop_time;
static uint32_t emerg_reset_cnt;
static uint32_t emerg_reset_mode = GPIO_MODE_OUTPUT_PUSH_PULL;
static uint32_t slow_start_time;
static uint32_t slow_time;

int                   cliff_handle          = -1;
static euler_angles_t euler_angle           = {0};
static uint32_t       euler_angle_update_ts = 0;
uint8_t               pub_raw_cliff_state[CLIFF_MAX];
// 屏蔽姿态获取超时设置默认断崖传感器类型的逻辑
// static uint32_t       cliff_type_det_close_ts = 0;

#define MAX_CLIFF_NUM 15
/*****************************************************************
 * 私有全局变量定义
 ******************************************************************/
extern struct bus_info   bus_gpio_emerg;
extern struct bus_info   bus_gpio_crash_left_2;
extern struct bus_info   bus_gpio_crash_right_3;
extern int               cliff_oml_handle1;
extern adc_sensor_obj_st cliff_sensor_objs[];
extern uint8_t           cliff_state[CLIFF_MAX];
extern float             cliff_vol[CLIFF_MAX];

extern struct bus_info bus_gpio_sewage_water_groove;
extern struct bus_info bus_gpio_dirt_box;
extern struct bus_info bus_gpio_virtul_wall;
extern struct bus_info bus_gpio_reset_emerg;

static SECURITY_BUTTON_ATTR_T security_button_info[SECURITY_BUTTON_MAX];
static BUTTON_ATTACH_ATTR_T   button_attr[SECURITY_BUTTON_MAX];
static SECUTIRT_HANDLE_ATTR_T security_handle[SECURITY_TYPE_MAX];
CLIFF_TYPE_E                  cliff_type = CLIFF_TYPE_UNKOWN;

CRASH_ADC_ATTR_T crash_adc_info[CRASH_ADC_NUMBER] = {0};

/*****************************************************************
 * 外部变量声明（如果全局变量没有在其它的H文件声明，引用时需在此处声明，
 *如果已在其它H文件声明，则只需包含此H文件即可）
 ******************************************************************/
extern int button_handle;
extern int wheel_handle;
extern int side_brush_handle;
extern int roller_brush_handle;
extern int roller_tube_handle;
extern int clean_water_pump_handle;
extern int sewage_water_pump_handle;
extern int fan_motor_handle;
extern int cliff_oml_handle1;
extern int cliff_psd_handle1;
extern int reset_emerg_handle;
/*****************************************************************
 * 私有函数原型声明
 ******************************************************************/
void check_cliff_type(void);

static float float_max(float a, float b) {
    if (a > b)
        return a;
    else
        return b;
}

void enable_wheel_brake(void) {
    //外部有急停置位逻辑 若急停按下 则置位
    //若电极压下 则置位
    if ((get_charge_feedback() == AUTO_CHARGE_ELEC_PRESS) || (get_charge_feedback() == PUSH_CHARGE_ELEC_PRESS)) {
        SET_BIT(wheel_brake_enable_flag, CHARGE_ELEC_PRESS_CMD);
    } else {
        CLEAR_BIT(wheel_brake_enable_flag, CHARGE_ELEC_PRESS_CMD);
    }
    //当标志位为0时 说明此时既没有电极压下 急停也没有摁下 要使能报死功能
    if (!wheel_brake_enable_flag)
        device_ioctl(wheel_handle, MOTOR_CMD_BRAKE_ENABLE, NULL);
}
void disable_all_motor(void) {
    int64_t rpm   = 0;
    uint8_t index = 0;

    // device_ioctl(wheel_handle, MOTOR_CMD_DISABLE, NULL);
    device_ioctl(side_brush_handle, MOTOR_CMD_DISABLE, NULL);
    device_ioctl(roller_brush_handle, MOTOR_CMD_DISABLE, NULL);
    device_ioctl(roller_tube_handle, MOTOR_CMD_DISABLE, NULL);
    device_ioctl(clean_water_pump_handle, MOTOR_CMD_DISABLE, NULL);
    device_ioctl(sewage_water_pump_handle, MOTOR_CMD_DISABLE, NULL);
    device_ioctl(fan_motor_handle, MOTOR_CMD_DISABLE, NULL);
    //香熏改成在应用层控制
    // SetAromatherapyStatus(false);

    return;
}

void enable_all_motor(void) {
    uint8_t index = 0;

    device_ioctl(side_brush_handle, MOTOR_CMD_ENABLE, NULL);
    device_ioctl(roller_brush_handle, MOTOR_CMD_ENABLE, NULL);
    device_ioctl(roller_tube_handle, MOTOR_CMD_ENABLE, NULL);
    device_ioctl(clean_water_pump_handle, MOTOR_CMD_ENABLE, NULL);
    device_ioctl(sewage_water_pump_handle, MOTOR_CMD_ENABLE, NULL);
    device_ioctl(fan_motor_handle, MOTOR_CMD_ENABLE, NULL);

    //香熏改成在应用层控制
    // SetAromatherapyStatus(true);

    return;
}

void set_motor_pwr(MOTOR_TYPE_E type, uint8_t state) {
    int32_t motor_index;
    switch (type) {
        case LEFT_SIDE_BRUSH:
            motor_index = 0;
            if (state) {
                device_ioctl(side_brush_handle, MOTOR_CMD_START, &motor_index);
            } else {
                device_ioctl(side_brush_handle, MOTOR_CMD_STOP, &motor_index);
            }
            break;
        case RIGHT_SIDE_BRUSH:
            motor_index = 1;
            if (state) {
                device_ioctl(side_brush_handle, MOTOR_CMD_START, &motor_index);
            } else {
                device_ioctl(side_brush_handle, MOTOR_CMD_STOP, &motor_index);
            }
            break;
        case ROLLER_BRUSH:
            motor_index = 0;
            if (state) {
                device_ioctl(roller_brush_handle, MOTOR_CMD_START, &motor_index);
            } else {
                device_ioctl(roller_brush_handle, MOTOR_CMD_STOP, &motor_index);
            }
            break;
        case ROLLER_TUBE:
            motor_index = 0;
            if (state) {
                device_ioctl(roller_tube_handle, MOTOR_CMD_START, &motor_index);
            } else {
                device_ioctl(roller_tube_handle, MOTOR_CMD_STOP, &motor_index);
            }
            break;
        case CLEAN_WATER_PUMP:
            if (state) {
                device_ioctl(clean_water_pump_handle, MOTOR_CMD_START, NULL);
            } else {
                device_ioctl(clean_water_pump_handle, MOTOR_CMD_STOP, NULL);
            }
            break;
        case SEWAGE_WATER_PUMP:
            if (state) {
                device_ioctl(sewage_water_pump_handle, MOTOR_CMD_START, NULL);
            } else {
                device_ioctl(sewage_water_pump_handle, MOTOR_CMD_STOP, NULL);
            }
            break;
        case LEFT_DRIVER_WHEEL:
            motor_index = 0;
            if (state) {
                device_ioctl(wheel_handle, MOTOR_CMD_START, &motor_index);
            } else {
                device_ioctl(wheel_handle, MOTOR_CMD_STOP, &motor_index);
            }
            break;
        case RIGHT_DRIVER_WHEEL:
            motor_index = 1;
            if (state) {
                device_ioctl(wheel_handle, MOTOR_CMD_START, &motor_index);
            } else {
                device_ioctl(wheel_handle, MOTOR_CMD_STOP, &motor_index);
            }
            break;
        default:
            break;
    }

    return;
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), set_motor_pwr, set_motor_pwr, set_motor_pwr);

static int security_button_callback(uint8_t index, BUTTON_STATUS_TYPE state) {
    security_button_info[index].state = state;
    log_d("security button callback index = %d state = %d", index, state);

    return 0;
}

static void emerg_exit_callback(const struct ca_device *dev, uint16_t GPIO_Pin) {
    static uint32_t last_count = 0;

    if ((!last_count) || (is_timeout(last_count, 100))) {
        last_count = osKernelGetTickCount();
        return;
    }

    if (is_timeout(last_count, 5)) {
        last_count = 0;
    }

    return;
}

void set_wheel_emerg_io_mode(uint32_t mode) {
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    if (GPIO_MODE_INPUT_NO_PP == mode) {
        GPIO_InitStruct.Pin  = WHEEL_EMERG_RESET_Pin;
        GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
        GPIO_InitStruct.Pull = GPIO_NOPULL;
        HAL_GPIO_Init(WHEEL_EMERG_RESET_GPIO_Port, &GPIO_InitStruct);
    } else if (GPIO_MODE_OUTPUT_PUSH_PULL == mode) {
        GPIO_InitStruct.Pin   = WHEEL_EMERG_RESET_Pin;
        GPIO_InitStruct.Mode  = GPIO_MODE_OUTPUT_PP;
        GPIO_InitStruct.Pull  = GPIO_NOPULL;
        GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
        HAL_GPIO_Init(WHEEL_EMERG_RESET_GPIO_Port, &GPIO_InitStruct);
    }
}

void happen_emerg_handle(void *args) {
    security_button_info[BUTTON_SECURITY_EMERG].trigger_flag = security_button_info[BUTTON_SECURITY_EMERG].trigger_flag ? 0 : 1;
    log_w("emerg change !!!");

    if (security_button_info[BUTTON_SECURITY_EMERG].trigger_flag) {
        SET_BIT(wheel.state, EMERG_CMD);
        SET_BIT(wheel_brake_enable_flag, EMERG_PRESS_CMD);
        device_ioctl(wheel_handle, MOTOR_CMD_BRAKE_DISABLE, NULL);
        disable_all_motor();

        //上报给pal_log
        pal_log_pub("warn", "hardware/emerg_press", "机器人急停");
        log_i("emerg happen !");
    } else {
        // 重置检测时间
        reset_all_motor_det_ts();
        CLEAR_BIT(wheel.state, EMERG_CMD);
        CLEAR_BIT(wheel_brake_enable_flag, EMERG_PRESS_CMD);
        enable_wheel_brake();
        enable_all_motor();

        //上报给pal_log
        pal_log_pub("warn", "hardware/emerg_press", "机器人急停解除");
        log_i("emerg release !");
    }

    SetSewageStatus(SEWAGE_SWITCH_CLOSE);
    return;
}

void release_emerg_handle(void *args) {
    log_w("emerg up!!!");
    if (!security_button_info[BUTTON_SECURITY_EMERG].trigger_flag) {
        // 重置检测时间
        reset_all_motor_det_ts();
        CLEAR_BIT(wheel.state, EMERG_CMD);
        CLEAR_BIT(wheel_brake_enable_flag, EMERG_PRESS_CMD);
        enable_wheel_brake();
        enable_all_motor();

        //上报给pal_log
        pal_log_pub("warn", "hardware/emerg_press", "机器人急停解除");
        log_i("emerg release !");
    }
    SetSewageStatus(SEWAGE_SWITCH_CLOSE);

    return;
}

void net_release_emerg_handle(void) {
    // 压桩信号会在中断中触发，所以需要使用中断临界区
    // 先保存中断状态
    uint32_t primask = __get_PRIMASK();
    __disable_irq();

    // 改变急停触发状态
    security_button_info[BUTTON_SECURITY_EMERG].trigger_flag = 0;
    // 改变按键状态
    security_button_callback(BUTTON_SECURITY_EMERG, BUTTON_PRESS_UP);

    // 恢复中断状态
    __set_PRIMASK(primask);

    // 需要通知 twist_mux, 急停按键远程释放
    // 按键状态改变后，fal 层会触发发布逻辑，不需要在这里调用
    // key_state_pub("emerg", "press_up");

    release_emerg_handle(NULL);
}

void happen_crash_handle(void *args) {
    security_stop_ms(1000);

    // priority_control_set(&led_control, WARN_LVL, led_red, NULL, "crash");
    if (is_timeout(crash_release_last_time, 1000)) {
        pal_log_pub("warn", "software/touge_edge", "安全触边");
        crash_happen_flag = 1;
        log_i("pub crash stop happen log!");
    }
    log_i("crash stop happen !");

    return;
}

void release_crash_handle(void *args) {
    if (is_timeout(crash_release_last_time, 1000)) {
        if (crash_happen_flag) {
            pal_log_pub("info", "software/touge_edge", "安全触边解除");
            crash_release_last_time = osKernelGetTickCount();
            crash_happen_flag       = 0;
            log_i("pub crash stop release log!");
        }
    }
    log_i("stop release !");

    return;
}

void happen_virtual_wall_handle(void *args) {
    log_i("happen_virtual_wall!");
    security_stop_ms(2000);
    return;
}
void release_virtual_wall_handle(void *args) {
    log_i("release_virtual_wall!");
    return;
}

double cal_rotate_radius(double v, double w) {
    return fabs(w) < 1e-4 ? 0 : v / w;
}

void cal_cmd_vel(double speed, double radius, double max_wheel_speed, double *output_v_speed, double *output_w_speed) {
    double cmd_vel_v_temp = 0.0;
    double cmd_vel_w_temp = 0.0;

    if (fabs(radius) < 1e-4) {
        cmd_vel_v_temp = speed;
        cmd_vel_w_temp = 0;
    } else {
        const double wheel_dis = 0.375;
        double       w_car     = speed / radius;
        double       v_r       = w_car * (radius + wheel_dis / 2);
        double       v_l       = w_car * (radius - wheel_dis / 2);

        double scale   = float_max(fabs(v_r / max_wheel_speed), 1.0);
        scale          = float_max(fabs(v_l / max_wheel_speed), scale);
        cmd_vel_w_temp = w_car / scale;
        cmd_vel_v_temp = cmd_vel_w_temp * radius;
    }

    *output_v_speed = cmd_vel_v_temp;
    *output_w_speed = cmd_vel_w_temp;
}

void cal_cmd_reduce_speed(MOTOR_SPEED *speed, float percent) {
    double v_speed_raw        = (double) speed->speed_v_t / 1000;
    double w_speed_raw        = (double) speed->speed_w_t / 1000;
    double max_speed_limit    = (double) wheel_speed.speed_v_t / 1000;
    double output_v_speed_raw = 0.0;
    double output_w_speed_raw = 0.0;
    double want_v_speed       = v_speed_raw * percent;
    double radius             = cal_rotate_radius(v_speed_raw, w_speed_raw);
    cal_cmd_vel(want_v_speed, radius, max_speed_limit, &output_v_speed_raw, &output_w_speed_raw);

    speed->speed_v_t = output_v_speed_raw * 1000;
    speed->speed_w_t = output_w_speed_raw * 1000;
}

/*****************************************************************
 * 函数定义
 ******************************************************************/
void fal_security_button(void) {
#ifdef EMERG

    memset(&security_button_info[BUTTON_SECURITY_EMERG], 0, sizeof(BUTTON_ATTR_T));
    memset(&button_attr[BUTTON_SECURITY_EMERG], 0, sizeof(BUTTON_ATTACH_ATTR_T));

    security_button_info[BUTTON_SECURITY_EMERG].button_attr.gpio_info            = &bus_gpio_emerg;
    security_button_info[BUTTON_SECURITY_EMERG].button_attr.trigger_condition    = GPIO_TRIGGER_LOW;
    security_button_info[BUTTON_SECURITY_EMERG].button_attr.index                = BUTTON_SECURITY_EMERG;
    security_button_info[BUTTON_SECURITY_EMERG].button_attr.period               = 2000;
    security_button_info[BUTTON_SECURITY_EMERG].button_attr.long_press_hold_type = BUTTON_LONG_PRESS_HOLD_SINGLE_TRIGGER;

    security_button_info[BUTTON_SECURITY_EMERG].security_type = SECURITY_EMERG;
    security_button_info[BUTTON_SECURITY_EMERG].last_state    = BUTTON_PRESS_UP;
    security_button_info[BUTTON_SECURITY_EMERG].state         = BUTTON_PRESS_UP;

    button_attr[BUTTON_SECURITY_EMERG].gpio_info                      = &bus_gpio_emerg;
    button_attr[BUTTON_SECURITY_EMERG].attach.button_press_down       = 1;
    button_attr[BUTTON_SECURITY_EMERG].attach.button_press_up         = 1;
    button_attr[BUTTON_SECURITY_EMERG].attach.button_single_click     = 1;
    button_attr[BUTTON_SECURITY_EMERG].attach.button_long_press_start = 1;
    button_attr[BUTTON_SECURITY_EMERG].button_callback                = security_button_callback;
    button_attr[BUTTON_SECURITY_EMERG].gpio_exti_callback             = emerg_exit_callback;

    security_handle[SECURITY_EMERG].happen_handle = happen_emerg_handle;
    // security_handle[SECURITY_EMERG].release_handle = release_emerg_handle;
    security_handle[SECURITY_EMERG].release_handle = NULL;

#endif

#ifdef CRASH_LEFT

    memset(&security_button_info[BUTTON_SECURITY_CRASH_1], 0, sizeof(BUTTON_ATTR_T));
    memset(&button_attr[BUTTON_SECURITY_CRASH_1], 0, sizeof(BUTTON_ATTACH_ATTR_T));

    security_button_info[BUTTON_SECURITY_CRASH_1].security_type = SECURITY_CRASH_LEFT2;
    security_button_info[BUTTON_SECURITY_CRASH_1].timeout       = 1000;
    security_button_info[BUTTON_SECURITY_CRASH_1].last_state    = BUTTON_PRESS_UP;
    security_button_info[BUTTON_SECURITY_CRASH_1].state         = BUTTON_PRESS_UP;

    security_handle[SECURITY_CRASH_LEFT2].happen_handle  = happen_crash_handle;
    security_handle[SECURITY_CRASH_LEFT2].release_handle = release_crash_handle;

    memset(&security_button_info[BUTTON_SECURITY_CRASH_2], 0, sizeof(BUTTON_ATTR_T));
    memset(&button_attr[BUTTON_SECURITY_CRASH_2], 0, sizeof(BUTTON_ATTACH_ATTR_T));

    security_button_info[BUTTON_SECURITY_CRASH_2].button_attr.gpio_info         = &bus_gpio_crash_left_2;
    security_button_info[BUTTON_SECURITY_CRASH_2].button_attr.trigger_condition = GPIO_TRIGGER_LOW;
    security_button_info[BUTTON_SECURITY_CRASH_2].button_attr.index             = BUTTON_SECURITY_CRASH_2;
    security_button_info[BUTTON_SECURITY_CRASH_2].timeout                       = 1000;
    security_button_info[BUTTON_SECURITY_CRASH_2].security_type                 = SECURITY_CRASH_LEFT;
    security_button_info[BUTTON_SECURITY_CRASH_2].last_state                    = BUTTON_PRESS_UP;
    security_button_info[BUTTON_SECURITY_CRASH_2].state                         = BUTTON_PRESS_UP;

    button_attr[BUTTON_SECURITY_CRASH_2].gpio_info                = &bus_gpio_crash_left_2;
    button_attr[BUTTON_SECURITY_CRASH_2].attach.button_press_down = 1;
    button_attr[BUTTON_SECURITY_CRASH_2].attach.button_press_up   = 1;
    button_attr[BUTTON_SECURITY_CRASH_2].attach.button_exit       = 1;
    button_attr[BUTTON_SECURITY_CRASH_2].button_callback          = security_button_callback;
    button_attr[BUTTON_SECURITY_CRASH_2].gpio_exti_callback       = emerg_exit_callback;

    security_handle[SECURITY_CRASH_LEFT].happen_handle  = happen_crash_handle;
    security_handle[SECURITY_CRASH_LEFT].release_handle = release_crash_handle;

#endif

#ifdef CRASH_RIGHT

    memset(&security_button_info[BUTTON_SECURITY_CRASH_3], 0, sizeof(BUTTON_ATTR_T));
    memset(&button_attr[BUTTON_SECURITY_CRASH_3], 0, sizeof(BUTTON_ATTACH_ATTR_T));

    security_button_info[BUTTON_SECURITY_CRASH_3].button_attr.gpio_info         = &bus_gpio_crash_right_3;
    security_button_info[BUTTON_SECURITY_CRASH_3].button_attr.trigger_condition = GPIO_TRIGGER_LOW;
    security_button_info[BUTTON_SECURITY_CRASH_3].button_attr.index             = BUTTON_SECURITY_CRASH_3;
    security_button_info[BUTTON_SECURITY_CRASH_3].timeout                       = 1000;
    security_button_info[BUTTON_SECURITY_CRASH_3].security_type                 = SECURITY_CRASH_RIGHT;
    security_button_info[BUTTON_SECURITY_CRASH_3].last_state                    = BUTTON_PRESS_UP;
    security_button_info[BUTTON_SECURITY_CRASH_3].state                         = BUTTON_PRESS_UP;

    button_attr[BUTTON_SECURITY_CRASH_3].gpio_info                = &bus_gpio_crash_right_3;
    button_attr[BUTTON_SECURITY_CRASH_3].attach.button_press_down = 1;
    button_attr[BUTTON_SECURITY_CRASH_3].attach.button_press_up   = 1;
    button_attr[BUTTON_SECURITY_CRASH_3].attach.button_exit       = 1;
    button_attr[BUTTON_SECURITY_CRASH_3].button_callback          = security_button_callback;
    button_attr[BUTTON_SECURITY_CRASH_3].gpio_exti_callback       = emerg_exit_callback;

    security_handle[SECURITY_CRASH_RIGHT].happen_handle  = happen_crash_handle;
    security_handle[SECURITY_CRASH_RIGHT].release_handle = release_crash_handle;

    memset(&security_button_info[BUTTON_SECURITY_CRASH_4], 0, sizeof(BUTTON_ATTR_T));
    memset(&button_attr[BUTTON_SECURITY_CRASH_4], 0, sizeof(BUTTON_ATTACH_ATTR_T));

    security_button_info[BUTTON_SECURITY_CRASH_4].security_type = SECURITY_CRASH_RIGHT2;
    security_button_info[BUTTON_SECURITY_CRASH_4].timeout       = 1000;
    security_button_info[BUTTON_SECURITY_CRASH_4].last_state    = BUTTON_PRESS_UP;
    security_button_info[BUTTON_SECURITY_CRASH_4].state         = BUTTON_PRESS_UP;

    security_handle[SECURITY_CRASH_RIGHT2].happen_handle  = happen_crash_handle;
    security_handle[SECURITY_CRASH_RIGHT2].release_handle = release_crash_handle;

#endif

    memset(&security_button_info[BUTTON_VIRTUL_WALL], 0, sizeof(BUTTON_ATTR_T));
    memset(&button_attr[BUTTON_VIRTUL_WALL], 0, sizeof(BUTTON_ATTACH_ATTR_T));

    security_button_info[BUTTON_VIRTUL_WALL].button_attr.gpio_info         = &bus_gpio_virtul_wall;
    security_button_info[BUTTON_VIRTUL_WALL].button_attr.trigger_condition = GPIO_TRIGGER_LOW;
    security_button_info[BUTTON_VIRTUL_WALL].button_attr.index             = BUTTON_VIRTUL_WALL;
    security_button_info[BUTTON_VIRTUL_WALL].security_type                 = SECURITY_VIRTUAL_WALL;
    security_button_info[BUTTON_VIRTUL_WALL].last_state                    = BUTTON_PRESS_UP;
    security_button_info[BUTTON_VIRTUL_WALL].state                         = BUTTON_PRESS_UP;

    button_attr[BUTTON_VIRTUL_WALL].gpio_info                = &bus_gpio_virtul_wall;
    button_attr[BUTTON_VIRTUL_WALL].attach.button_press_down = 1;
    button_attr[BUTTON_VIRTUL_WALL].attach.button_press_up   = 1;
    button_attr[BUTTON_VIRTUL_WALL].attach.button_exit       = 1;
    button_attr[BUTTON_VIRTUL_WALL].button_callback          = security_button_callback;
    button_attr[BUTTON_VIRTUL_WALL].gpio_exti_callback       = emerg_exit_callback;

    security_handle[SECURITY_VIRTUAL_WALL].happen_handle  = happen_virtual_wall_handle;
    security_handle[SECURITY_VIRTUAL_WALL].release_handle = release_virtual_wall_handle;

    return;
}

int8_t find_pub_callback(SECURITY_TYPE_E security_type, security_cb *p_callback) {
    struct list_struct *p_list = NULL;
    SECUTIRT_CB_NODE_P  p_node = NULL;

    p_list = &security_pub_callback_list;
    while (list_is_last(p_list, &security_pub_callback_list) != 1) {
        p_node = (SECUTIRT_CB_NODE_P) p_list->next;

        if (security_type == p_node->security_cb_attr.security_type) {
            *p_callback = p_node->security_cb_attr.callback;
            return 0;
        }
        p_list = p_list->next;
    }

    return -1;
}

void security_pub_data(SECURITY_TYPE_E security_type, void *args) {
    // MOTOR_CB_ARGS_T button_args;
    security_cb p_callback = NULL;
    // memcpy(&button_args, args, sizeof(MOTOR_CB_ARGS_T));
    find_pub_callback(security_type, &p_callback);
    if (NULL == p_callback) {
        return;
    }
    p_callback(args);

    return;
}

extern char emerg_type_str[32];

void security_button_handle(void) {
    uint8_t          index = 0;
    BUTTON_CB_ARGS_T button_args;

    for (index = 0; index < SECURITY_BUTTON_MAX; index++) {
        // if (security_button_info[index].pub_flag) {
        //     security_button_info[index].tick_count++;
        // }

        //触发状态发生改变或按键状态发送改变或者到达超时时间做相应处理
        // if ((security_button_info[index].state != security_button_info[index].last_state) ||
        //     ((security_button_info[index].tick_count >= security_button_info[index].timeout / TICK) &&
        //      (0 != security_button_info[index].timeout))) {
        //更新last_state
        // if (  (security_button_info[index].tick_count >= security_button_info[index].timeout / TICK) &&
        //     (0 != security_button_info[index].timeout)) {
        //     security_button_info[index].last_state = BUTTON_PRESS_UP;
        //     security_button_info[index].state      = BUTTON_PRESS_UP;
        // } else {
        if ((security_button_info[index].state != security_button_info[index].last_state) ||
            (security_button_info[index].trigger_flag != security_button_info[index].last_trigger_flag)) {
            security_button_info[index].last_state = security_button_info[index].state;

            switch (security_button_info[index].last_state) {
                case BUTTON_PRESS_DOWN:
                    // security_button_info[index].pub_flag = 1;

                    log_i("security button index[%d] happen ! PD", index);

                    if (index == BUTTON_SECURITY_EMERG) {
                        key_state_pub("emerg", "press_down");

                        //急停的事件处理只在按键的单击和长按，按下时只发布按键状态
                        break;
                    }

                    if (security_handle[security_button_info[index].security_type].happen_handle) {
                        security_handle[security_button_info[index].security_type].happen_handle(NULL);
                    }

                    break;

                case BUTTON_PRESS_UP:
                    // security_button_info[index].pub_flag = 0;

                    log_i("security button index[%d][%d][%d] release! PU", index, security_button_info[index].trigger_flag,
                          security_button_info[index].last_trigger_flag);

                    if (index == BUTTON_SECURITY_EMERG) {
                        key_state_pub("emerg", "press_up");

                        //急停的事件处理只在按键的单击和长按，弹起时只发布按键状态
                        break;
                    }

                    if (security_handle[security_button_info[index].security_type].release_handle) {
                        security_handle[security_button_info[index].security_type].release_handle(NULL);
                    }

                    break;

                case BUTTON_SINGLE_CLICK:

                    log_i("security button index[%d] happen! SC", index);

                    if (index == BUTTON_SECURITY_EMERG) {
                        if (strcmp("longpress", emerg_type_str) == 0) {
                            if (!security_button_info[index].trigger_flag) {
                                break;
                            } else {
                            }
                        }
                    }

                    security_handle[security_button_info[index].security_type].happen_handle(NULL);

                    break;

                case BUTTON_LONG_PRESS_START:

                    log_i("security button index[%d] happen! LP", index);

                    if (index == BUTTON_SECURITY_EMERG) {
                        if (strcmp("longpress", emerg_type_str) != 0) {
                            break;
                        }
                    }

                    security_handle[security_button_info[index].security_type].happen_handle(NULL);

                    break;

                default:
                    break;
            }

            if ((security_button_info[index].trigger_flag == security_button_info[index].last_trigger_flag) &&
                (index == BUTTON_SECURITY_EMERG)) {
                continue;
            }

            button_args.index                             = index;
            button_args.state                             = security_button_info[index].last_state;
            button_args.trigger_state                     = security_button_info[index].trigger_flag;
            security_button_info[index].last_trigger_flag = security_button_info[index].trigger_flag;
            security_pub_data(security_button_info[index].security_type, (void *) &button_args);
            security_button_info[index].tick_count = 0;
        }
    }
    return;
}

void motor_speed_update(MOTOR_SPEED *speed) {
    wheel_speed.speed_v_t = speed->speed_v_t;
    wheel_speed.speed_w_t = speed->speed_w_t;

    wheel_timeout = osKernelGetTickCount();
}

int security_cliff_handle() {
    static bool    pub_cliff_state[2]  = {0};
    static uint8_t cliff_part_state[2] = {0};
    static int8_t  dire_state          = 0;
    static int8_t  last_dire_state     = 0;
    static int32_t last_cliff_count    = 0;
    static int8_t  pub_cliff_count     = 0;
    float          vel_factor          = 0.0;

    static int32_t cliff_count = 0;

    int32_t value_temp = 0;

    for (uint8_t index = 0; index < CLIFF_MAX - 3; index++) {
        if (cliff_handle != -1) {
            cliff_state[index] = device_ioctl(cliff_handle, CLIFF_CMD_GET_STATE, (void *) &cliff_sensor_objs[index + 1]);
        } else {
            cliff_state[index] = device_ioctl(cliff_oml_handle1, CLIFF_CMD_GET_STATE, (void *) &cliff_sensor_objs[index + 1]);
        }

        if (cliff_handle == cliff_psd_handle1) {
            value_temp       = device_ioctl(cliff_psd_handle1, CLIFF_CMD_GET_VALUE, (void *) &cliff_sensor_objs[index + 1]);
            cliff_vol[index] = value_temp * 3.3 / 4096;
        }
    }

    if (cliff_state[2] == CLIFF_EVEVT_CMD || cliff_state[3] == CLIFF_EVEVT_CMD) {
        cliff_part_state[CLIFF_REAR] = CLIFF_EVEVT_CMD;
    } else {
        cliff_part_state[CLIFF_REAR] = CLIFF_RELEASE_CMD;
    }
    if (cliff_state[0] == CLIFF_EVEVT_CMD || cliff_state[1] == CLIFF_EVEVT_CMD || cliff_state[4] == CLIFF_EVEVT_CMD ||
        cliff_state[5] == CLIFF_EVEVT_CMD) {
        cliff_part_state[CLIFF_FRONT] = CLIFF_EVEVT_CMD;
    } else {
        cliff_part_state[CLIFF_FRONT] = CLIFF_RELEASE_CMD;
    }

    //断崖触发急停，不上报透穿信息
    if (READ_BIT(security_off_ctl_bit(), CLIFF_CMD)) {
        memset(cliff_state, 0, CLIFF_MAX);
        memset(pub_raw_cliff_state, 0, CLIFF_MAX);
        pub_cliff_state[CLIFF_FRONT] = false;
        pub_cliff_state[CLIFF_REAR]  = false;
        return 0;
    }

    // 断崖触发逻辑
    if (dire_state != RUN_DIRE_STOP) {
        last_dire_state = dire_state;
    }
    dire_state = (wheel_speed.speed_v_t > 0) ? RUN_DIRE_FORWARD : (wheel_speed.speed_v_t < 0) ? RUN_DIRE_BACKWARD : RUN_DIRE_STOP;

    if ((last_dire_state == RUN_DIRE_FORWARD && dire_state == RUN_DIRE_BACKWARD) ||
        (last_dire_state == RUN_DIRE_BACKWARD && dire_state == RUN_DIRE_FORWARD)) {
        cliff_count = 0;
    }

    last_cliff_count = cliff_count;
    // 断崖触发
    if ((cliff_part_state[CLIFF_FRONT] == CLIFF_EVEVT_CMD || cliff_part_state[CLIFF_REAR] == CLIFF_EVEVT_CMD)) {
        if (dire_state != RUN_DIRE_STOP) {
            SET_BIT(wheel.state, CLIFF_CMD);
            device_ioctl(wheel_handle, MOTOR_CMD_URGENT_BRAKE_ENABLE, NULL);
            if (++cliff_count >= MAX_CLIFF_NUM) {
                cliff_count = MAX_CLIFF_NUM;
            }

            if (++pub_cliff_count >= MAX_CLIFF_NUM) {
                pub_cliff_count = MAX_CLIFF_NUM;
            }
        }
    }

    if ((dire_state == RUN_DIRE_FORWARD && cliff_part_state[CLIFF_FRONT] == CLIFF_RELEASE_CMD) ||
        (dire_state == RUN_DIRE_BACKWARD && cliff_part_state[CLIFF_REAR] == CLIFF_RELEASE_CMD) ||
        (dire_state == RUN_DIRE_STOP && cliff_part_state[CLIFF_REAR] == CLIFF_RELEASE_CMD &&
         cliff_part_state[CLIFF_FRONT] == CLIFF_RELEASE_CMD)) {
        if (--cliff_count <= 0) {
            cliff_count = 0;
            CLEAR_BIT(wheel.state, CLIFF_CMD);
            device_ioctl(wheel_handle, MOTOR_CMD_URGENT_BRAKE_DISABLE, NULL);
        }

        if (--pub_cliff_count < 0) {
            pub_cliff_count = 0;
        }
    }

    // 降速曲线
    vel_factor = 1.0 - pow((float) cliff_count / MAX_CLIFF_NUM, 2);
    // vel_factor                  = 1.0 - (float) cliff_count / MAX_CLIFF_NUM;
    vel_factor                  = (vel_factor < 0) ? 0 : vel_factor;
    wheel_speed_cliff.speed_v_t = wheel_speed.speed_v_t;
    wheel_speed_cliff.speed_w_t = wheel_speed.speed_w_t;
    cal_cmd_reduce_speed(&wheel_speed_cliff, vel_factor);
    // 应算法要求, 这里不能对旋转速度做限制
    // 如果原角速度不为 0，线速度为 0，则按照原角速度运行
    // 因为这样算出来的减速后角速度是 0，机器会无法原地旋转
    if (wheel_speed.speed_w_t != 0 && wheel_speed.speed_v_t == 0) {
        wheel_speed_cliff.speed_w_t = wheel_speed.speed_w_t;
    }

    if ((wheel_speed_cliff.speed_v_t != wheel_speed.speed_v_t || wheel_speed_cliff.speed_w_t != wheel_speed.speed_w_t) &&
        last_cliff_count != cliff_count) {
        log_w("vel_factor:%.2f, cliff_count:%d,v_peed_cliff:%d,w_speed:_cliff%d,v_speed:%d,w_speed:%d,dire_state:%d", vel_factor, cliff_count,
              wheel_speed_cliff.speed_v_t, wheel_speed_cliff.speed_w_t, wheel_speed.speed_v_t, wheel_speed_cliff.speed_w_t, dire_state);
    }

    // 上报断崖
    // 前断崖触发
    if (pub_cliff_state[CLIFF_FRONT] == false) {
        if (cliff_part_state[CLIFF_FRONT] == CLIFF_EVEVT_CMD && pub_cliff_count >= MAX_CLIFF_NUM) {
            // 上报前断崖触发
            pub_cliff_state[CLIFF_FRONT] = true;
            log_e("CLIFF_FRONT target!");
            char cliff_log[50];
            sprintf(cliff_log, "前防跌落触发,1:%d,2:%d,5:%d,6:%d", cliff_state[0], cliff_state[1], cliff_state[4], cliff_state[5]);
            log_e("%s", cliff_log);
            pal_log_pub("warn", "software/front_cliff", cliff_log);
        }
    } else {
        if (cliff_part_state[CLIFF_FRONT] == CLIFF_RELEASE_CMD && !pub_cliff_count) {
            // 上报前断崖释放
            pub_cliff_state[CLIFF_FRONT] = false;
            log_e("CLIFF_FRONT realese!");
            pal_log_pub("warn", "software/front_cliff", "前防跌落解除");
        }
    }

    // 后断崖触发
    if (pub_cliff_state[CLIFF_REAR] == false) {
        if (cliff_part_state[CLIFF_REAR] == CLIFF_EVEVT_CMD && pub_cliff_count >= MAX_CLIFF_NUM) {
            // 上报后断崖触发
            pub_cliff_state[CLIFF_REAR] = true;
            log_e("CLIFF_REAR target!");
            char cliff_log[50];
            sprintf(cliff_log, "后防跌落触发,3:%d,4:%d", cliff_state[2], cliff_state[3]);
            log_e("%s", cliff_log);
            pal_log_pub("warn", "software/front_cliff", cliff_log);
        }
    } else {
        if (cliff_part_state[CLIFF_REAR] == CLIFF_RELEASE_CMD && !pub_cliff_count) {
            // 上报后断崖释放
            pub_cliff_state[CLIFF_REAR] = false;
            log_e("CLIFF_REAR realese!");
            pal_log_pub("warn", "software/front_cliff", "后防跌落解除");
        }
    }

    if (pub_cliff_state[CLIFF_FRONT] == true) {
        pub_raw_cliff_state[0] = cliff_state[0];
        pub_raw_cliff_state[1] = cliff_state[1];
        pub_raw_cliff_state[4] = cliff_state[4];
        pub_raw_cliff_state[5] = cliff_state[5];
    } else {
        pub_raw_cliff_state[0] = 0;
        pub_raw_cliff_state[1] = 0;
        pub_raw_cliff_state[4] = 0;
        pub_raw_cliff_state[5] = 0;
    }

    if (pub_cliff_state[CLIFF_REAR] == true) {
        pub_raw_cliff_state[2] = cliff_state[2];
        pub_raw_cliff_state[3] = cliff_state[3];
    } else {
        pub_raw_cliff_state[2] = 0;
        pub_raw_cliff_state[3] = 0;
    }

    return 0;
}

void mcu_motor_speed_vw(int16_t speed_v, int16_t speed_w) {
    speed_v = (speed_v > SPEED_MAX) ? (SPEED_MAX) : (speed_v);
    speed_v = (speed_v < SPEED_MIN) ? (SPEED_MIN) : (speed_v);

    speed_w = (speed_w > SPEED_MAX) ? (SPEED_MAX) : (speed_w);
    speed_w = (speed_w < SPEED_MIN) ? (SPEED_MIN) : (speed_w);

    wheel_speed_mcu.speed_v_t = speed_v;
    wheel_speed_mcu.speed_w_t = speed_w;
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), mcu_motor_speed_vw, mcu_motor_speed_vw, mm / s);

void security_stop(uint32_t stopbit) {
    wheel.security_off_ctl |= 1 << stopbit;
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), security_stop, security_stop, 1 crash; 3 cliff);

void security_start(uint32_t startbit) {
    wheel.security_off_ctl &= ~(1 << startbit);
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), security_start, security_start, 1 crash; 3 cliff);

uint32_t security_off_ctl_bit(void) {
    return wheel.security_off_ctl;
}

void security_stop_ms(uint32_t ms) {
    uint32_t resume_time_new;
    uint32_t resume_time_old;

    resume_time_old = stop_start_time + stop_time;
    resume_time_new = osKernelGetTickCount() + ms;

    //只有新的停机时间比原来的停机时间久，才更新停机时长，避免一些短的停机误恢复原来进行中的长停机保护。
    if (resume_time_new > resume_time_old) {
        SET_BIT(wheel.state, STOP_CMD);
        stop_start_time = osKernelGetTickCount();
        stop_time       = ms;
    }
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), security_stop_ms, security_stop_ms, security_stop_ms);

void security_slow_wheel(uint32_t percent, uint32_t ms) {
    float slow_percent = 0.0f;

    slow_time    = ms;
    slow_percent = (float) percent / 100;

    speed_slow.speed_v_t = wheel_speed.speed_v_t;
    speed_slow.speed_w_t = wheel_speed.speed_w_t;
    cal_cmd_reduce_speed(&speed_slow, slow_percent);
    // 如果原角速度不为 0，线速度为 0，则按照原角速度运行
    // 因为这样算出来的减速后角速度是 0，机器会无法原地旋转
    if (wheel_speed.speed_w_t != 0 && wheel_speed.speed_v_t == 0) {
        speed_slow.speed_w_t = wheel_speed.speed_w_t;
    }

    log_i("slow move, raw v:%d,w:%d,reduce:%.2f, out v:%d,w:%d", wheel_speed.speed_v_t, wheel_speed.speed_w_t, slow_percent,
          speed_slow.speed_v_t, speed_slow.speed_w_t);

    SET_BIT(wheel.state, SLOW_CMD);
    slow_start_time = osKernelGetTickCount();
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), security_slow_wheel, security_slow_wheel, security_slow_wheel);

uint8_t get_security_wheel_state_bit(SECUTIRT_STATE_E wheel_bit) {
    return READ_BIT(wheel.state, wheel_bit);
}

void adc_crash_detect(void) {
    static uint32_t crash_adc_delt_time = 0;
    static uint8_t  i                   = 0;

    BUTTON_STATUS_TYPE adc_button_state[CRASH_ADC_NUMBER];

    for (i = 0; i < CRASH_ADC_NUMBER; i++) {
        adc_button_state[i] = BUTTON_EVENT_MAX;

        crash_adc_info[i].value = device_ioctl(cliff_oml_handle1, CLIFF_CMD_GET_VALUE, crash_adc_info[i].adc_info);
        log_d("crash[%d]---valve = %d", i, crash_adc_info[i].value);
    }

    for (i = 0; i < CRASH_ADC_NUMBER; i++) {
        // crash_adc_info[i].value = device_ioctl(cliff_oml_handle1, CLIFF_CMD_GET_VALUE, crash_adc_info[i].adc_info);
        // log_d("crash[%d]---valve = %d", i, crash_adc_info[i].value);
        // 数据抖动过程只用差分检测，不要绝对阈值检测
        if (!is_timeout(crash_adc_info[i].adc_value_shake_ts, 500)) {
            continue;
        }

        if (crash_adc_info[i].value >= crash_adc_info[i].adc_info->adc_value_top_thres) {
            crash_adc_info[i].release_cnt = 0;
            if (crash_adc_info[i].adc_button_state != BUTTON_PRESS_DOWN) {
                if (++crash_adc_info[i].trigger_cnt >= CRASH_ADC_PREVENT_TIME) {
                    // security_button_callback(crash_adc_info[i].index, BUTTON_PRESS_DOWN);
                    adc_button_state[i]          = BUTTON_PRESS_DOWN;
                    crash_adc_info[i].last_value = crash_adc_info[i].value;
                    log_e("DOWN-----------------crash[%d] = %d", i, crash_adc_info[i].value);
                }
            }
        } else {
            crash_adc_info[i].trigger_cnt = 0;
        }

        if (crash_adc_info[i].value < crash_adc_info[i].adc_info->adc_value_low_thres) {
            crash_adc_info[i].trigger_cnt = 0;
            if (crash_adc_info[i].adc_button_state != BUTTON_PRESS_UP) {
                if (++crash_adc_info[i].release_cnt >= CRASH_ADC_PREVENT_TIME) {
                    // security_button_callback(crash_adc_info[i].index, BUTTON_PRESS_UP);
                    adc_button_state[i]          = BUTTON_PRESS_UP;
                    crash_adc_info[i].last_value = crash_adc_info[i].value;
                    log_e("UP-----------------crash[%d] = %d", i, crash_adc_info[i].value);
                }
            }
        } else {
            crash_adc_info[i].release_cnt = 0;
        }
    }

    for (i = 0; i < CRASH_ADC_NUMBER; i++) {
        crash_adc_info[i].adc_value_delt_cnt++;

        if (crash_adc_info[i].value > crash_adc_info[i].adc_value_delt_peak) {
            crash_adc_info[i].adc_value_delt_peak = crash_adc_info[i].value;
        }

        if (crash_adc_info[i].adc_value_delt_cnt == 1) {
            crash_adc_info[i].adc_value_delt_left = crash_adc_info[i].value;
        } else if (crash_adc_info[i].adc_value_delt_cnt == CRASH_ADC_PREVENT_TIME / 2) {
            crash_adc_info[i].adc_value_delt_right = crash_adc_info[i].value;
        }

        if (crash_adc_info[i].adc_value_delt_cnt >= CRASH_ADC_PREVENT_TIME / 2) {
            log_d("(%d)l[%d] r[%d] p[%d]", i, crash_adc_info[i].adc_value_delt_left, crash_adc_info[i].adc_value_delt_right,
                  crash_adc_info[i].adc_value_delt_peak);
            if ((crash_adc_info[i].adc_value_delt_peak - crash_adc_info[i].adc_value_delt_right) >=
                (crash_adc_info[i].adc_info->adc_value_delt_thres / 2)) {
                crash_adc_info[i].adc_value_shake_ts = osKernelGetTickCount();

                if (crash_adc_info[i].adc_button_state != BUTTON_PRESS_UP) {
                    adc_button_state[i] = BUTTON_PRESS_UP;
                    log_w("D-UP---------------crash[%d] = %d", i, crash_adc_info[i].value);
                }
            } else if ((crash_adc_info[i].adc_value_delt_peak - crash_adc_info[i].adc_value_delt_left) >=
                       crash_adc_info[i].adc_info->adc_value_delt_thres) {
                crash_adc_info[i].adc_value_shake_ts = osKernelGetTickCount();

                if (crash_adc_info[i].adc_button_state != BUTTON_PRESS_DOWN) {
                    adc_button_state[i] = BUTTON_PRESS_DOWN;
                    log_w("D-DOWN-------------crash[%d] = %d", i, crash_adc_info[i].value);
                }
            }

            crash_adc_info[i].adc_value_delt_peak = 0;
            crash_adc_info[i].adc_value_delt_cnt  = 0;
        }

        /*
        if (is_timeout(crash_adc_info[i].adc_value_delt_ts, crash_adc_info[i].adc_info->adc_value_delt_ms)) {
            crash_adc_info[i].adc_value_delt_value = crash_adc_info[i].value - crash_adc_info[i].adc_value_delt_buff;

            if (crash_adc_info[i].adc_value_delt_value > crash_adc_info[i].adc_info->adc_value_delt_thres &&
                crash_adc_info[i].adc_button_state != BUTTON_PRESS_DOWN) {
                adc_button_state[i] = BUTTON_PRESS_DOWN;
                log_w("D-DOWN---------------crash[%d] = %d", i, crash_adc_info[i].value);
            } else if (crash_adc_info[i].adc_value_delt_value < -1 * crash_adc_info[i].adc_info->adc_value_delt_thres &&
                       crash_adc_info[i].adc_button_state != BUTTON_PRESS_UP) {
                adc_button_state[i] = BUTTON_PRESS_UP;
                log_w("D-UP---------------crash[%d] = %d", i, crash_adc_info[i].value);
            }

            crash_adc_info[i].adc_value_delt_buff = crash_adc_info[i].value;
        }
        */
    }

    for (i = 0; i < CRASH_ADC_NUMBER; i++) {
        if (adc_button_state[i] != BUTTON_EVENT_MAX && crash_adc_info[i].adc_button_state != adc_button_state[i]) {
            crash_adc_info[i].adc_button_state = adc_button_state[i];
            security_button_callback(crash_adc_info[i].index, crash_adc_info[i].adc_button_state);
        }
    }
}

void cliff_det_handle() {
    euler_angles_t pose_angle      = {0};
    static uint8_t pose_steady_cnt = 0;
    if (cliff_type == CLIFF_TYPE_UNKOWN) {
        if (get_pose_euler_angle(&pose_angle) == -1) {
            // 屏蔽姿态获取超时设置默认断崖传感器类型的逻辑
            // if (is_timeout(cliff_type_det_close_ts, 2 * 60000))
            if (0) {
                log_e("get imu time out, set type is ir");
                cliff_type = CLIFF_TYPE_IR;
                device_ioctl(cliff_oml_handle1, CLIFF_CMD_ENABLE, NULL);
                cliff_handle = cliff_oml_handle1;
            }
        } else {
            if (fabs(pose_angle.pitch) <= DESCENT_PROTECT_PITCH_THREADHOLD) {
                pose_steady_cnt++;
            } else {
                log_w("wait pitch angle:%d, now:%.2f", DESCENT_PROTECT_PITCH_THREADHOLD, pose_angle.pitch);
                pose_steady_cnt = 0;
            }

            if (pose_steady_cnt >= 5) {
                pose_steady_cnt = 5;
                check_cliff_type();
            }
        }
    }
}

static void security_run(void *argument) {
    uint32_t       security_button_time  = 0;
    uint32_t       crash_adc_time        = 0;
    uint32_t       descent_protect_count = 0;    //用于控制斜坡保护的时间间隔
    euler_angles_t euler_angles          = {0};  //用于读取车身欧拉角 判断机器是否在坡上
    int            pose_get_ret          = 0;    //读取欧拉角的返回值
    uint32_t       cliff_det_time        = 0;
    // osDelay(1000);
    emerg_reset_mode = GPIO_MODE_INPUT_NO_PP;
    set_wheel_emerg_io_mode(GPIO_MODE_INPUT_NO_PP);
    cycle_led_t emerg_led_error;
    emerg_led_error.cycle      = 200;
    emerg_led_error.duty_cycle = 0.5;
    // 屏蔽姿态获取超时设置默认断崖传感器类型的逻辑
    // cliff_type_det_close_ts    = osKernelGetTickCount();

    while (1) {
        security_button_handle();
        if (is_timeout(security_button_time, SECURITY_BUTTON_TIME)) {
            if (security_button_info[BUTTON_SECURITY_EMERG].trigger_flag) {
                priority_control_set(&led_control, WARN_LVL + WARN_LVL, led_red_cycle, &emerg_led_error, "emerg");
            }
            security_button_time = osKernelGetTickCount();
        }

        if (is_timeout(cliff_det_time, SECURITY_BUTTON_TIME)) {
            cliff_det_handle();
            cliff_det_time = osKernelGetTickCount();
        }

        if (cliff_type != CLIFF_TYPE_UNKOWN) {
            security_cliff_handle();
        }

        if (is_timeout(crash_adc_time, CRASH_ADC_TICK)) {
            crash_adc_time = osKernelGetTickCount();
            adc_crash_detect();
        }

        if (is_timeout(wheel_timeout, WHEEL_TIMEOUT_TICK)) {
            SET_BIT(wheel.state, WHEEL_TIMEOUT);
        } else {
            CLEAR_BIT(wheel.state, WHEEL_TIMEOUT);
        }

        if (!READ_BIT(wheel.state, EMERG_CMD)) {
            static uint32_t stable_count = 0;
            // 滤波时间要大于300毫秒，否则会出现移动过程按下急停出现车子跳动的情况
            // 原因是 wheel.state 更新延迟导致，按键单击事件发生及处理的延迟时间较长
            const uint32_t stable_threshold       = 250;
            uint32_t       gpio_reset_emerg_state = 0;
            device_read(reset_emerg_handle, &gpio_reset_emerg_state, 0);
            if (gpio_reset_emerg_state == GPIO_PIN_SET) {
                stable_count++;
            } else {
                stable_count = 0;
            }
            if (stable_count >= stable_threshold) {
                log_i("gpio_reset_emerg find high, change to low");
                set_wheel_emerg_io_mode(GPIO_MODE_OUTPUT_PUSH_PULL);
                device_ioctl(reset_emerg_handle, GPIO_ACTIVE_LOW, NULL);
                emerg_reset_cnt = 100 / TICK;
            }
            if (emerg_reset_cnt) {
                emerg_reset_cnt--;
                if (emerg_reset_cnt == 0) {
                    log_i("gpio_reset_emerg return input");
                    set_wheel_emerg_io_mode(GPIO_MODE_INPUT_NO_PP);
                }
            }
        } else {
            // 每次检查到急停触发，都关闭所有电机一次，避免因为时序问题导致部分组件还在工作
            disable_all_motor();
        }

        if (wheel.security_off_ctl) {
            SECUTIRT_STATE_E wheel_compare_state;
            wheel_compare_state = wheel.state;
            CLEAR_BIT(wheel.state, wheel.security_off_ctl);
            if (wheel_compare_state != wheel.state) {
                log_w("security bit %x close", (wheel_compare_state | wheel.state));
            }
        }

        if (READ_BIT(wheel.state, STOP_CMD) && (is_timeout(stop_start_time, stop_time))) {
            CLEAR_BIT(wheel.state, STOP_CMD);
            enable_wheel_brake();
        }

        if (READ_BIT(wheel.state, SLOW_CMD) && (is_timeout(slow_start_time, slow_time))) {
            log_i("clear wheel slow cmd");
            CLEAR_BIT(wheel.state, SLOW_CMD);
        }

        if (READ_BIT(wheel.state, EMERG_CMD | WHEEL_TIMEOUT | STOP_CMD)) {
            MOTOR_SPEED speed_zero = {0};
            device_ioctl(wheel_handle, MOTOR_CMD_SET_SPEED, (void *) &speed_zero);
        } else if (READ_BIT(wheel.state, CLIFF_CMD)) {
            device_ioctl(wheel_handle, MOTOR_CMD_SET_SPEED, (void *) &wheel_speed_cliff);
        } else if (READ_BIT(wheel.state, SLOW_CMD)) {
            device_ioctl(wheel_handle, MOTOR_CMD_SET_SPEED, (void *) &speed_slow);
        } else {
            if (wheel_speed_mcu.speed_v_t != 0 || wheel_speed_mcu.speed_w_t != 0) {
                device_ioctl(wheel_handle, MOTOR_CMD_SET_SPEED, (void *) &wheel_speed_mcu);
            } else {
                device_ioctl(wheel_handle, MOTOR_CMD_SET_SPEED, (void *) &wheel_speed);
            }
        }
        descent_protect_count++;
        // 获取车身姿态角
        if (descent_protect_count >= DESCENT_PROTECT_TIMES) {
            pose_get_ret = get_pose_euler_angle(&euler_angles);
            if (pose_get_ret == 0 &&
                (fabs(euler_angles.pitch) >= DESCENT_PROTECT_PITCH_THREADHOLD || fabs(euler_angles.roll) >= DESCENT_PROTECT_ROLL_THREADHOLD)) {
                device_ioctl(wheel_handle, MOTOR_CMD_URGENT_BRAKE_ENABLE, NULL);
            } else {
                device_ioctl(wheel_handle, MOTOR_CMD_URGENT_BRAKE_DISABLE, NULL);
            }
            descent_protect_count = 0;
        }

        osDelay(TICK);
    }

    return;
}

void print_wheel_state(void) {
    log_w("wheel.state: %x", wheel.state);
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), print_wheel_state, print_wheel_state, print_wheel_state);

int8_t security_pub_register(void *attr) {
    if (NULL == attr) {
        return -1;
    }
    SECURITY_CB_P p_attr = (SECURITY_CB_P) attr;

    if ((p_attr->callback == NULL) || (p_attr->security_type >= SECURITY_TYPE_MAX)) {
        return -1;
    }

    SECUTIRT_CB_NODE_P p_node = NULL;

    p_node = (SECUTIRT_CB_NODE_P) mem_block_alloc(sizeof(SECUTIRT_CB_NODE_T));

    if (NULL == p_node) {
        return -1;
    }

    memset(p_node, 0, sizeof(SECUTIRT_CB_NODE_T));

    p_node->security_cb_attr = *p_attr;

    list_add_tail(&p_node->node, &security_pub_callback_list);

    return 0;
}

void quaternion_to_euler(quaternion_t *quat, euler_angles_t *euler) {
    double q0, q1, q2, q3;
    q0           = quat->w;
    q1           = quat->x;
    q2           = quat->y;
    q3           = quat->z;
    euler->pitch = (float) (asin(-2 * q1 * q3 + 2 * q0 * q2) * 57.3);
    euler->roll  = (float) (atan2(2 * q2 * q3 + 2 * q0 * q1, -2 * q1 * q1 - 2 * q2 * q2 + 1) * 57.3);
    euler->yaw   = (float) (atan2(2 * (q1 * q2 + q0 * q3), q0 * q0 + q1 * q1 - q2 * q2 - q3 * q3) * 57.3);
}

void pose_handle(quaternion_t pose_quater) {
    quaternion_to_euler(&pose_quater, &euler_angle);
    euler_angle_update_ts = osKernelGetTickCount();
}

int get_pose_euler_angle(euler_angles_t *e) {
    if (is_timeout(euler_angle_update_ts, 1000)) {
        return -1;
    } else {
        e->pitch = euler_angle.pitch;
        e->roll  = euler_angle.roll;
        e->yaw   = euler_angle.yaw;

        return 0;
    }
}

void list_init(void) {
    list_head_init(&security_pub_callback_list);
    list_head_init(&security_handle_list);

    return;
}

void fal_security_button_init(void) {
    int index = 0;

    fal_security_button();

    osThreadId_t security_thread = osThreadNew(security_run, NULL, &security_attributes);

    for (index = 0; index < SECURITY_BUTTON_MAX; index++) {
        device_ioctl(button_handle, BUTTON_CMD_SET_ADD_BUTTON, (void *) &security_button_info[index].button_attr);
        device_ioctl(button_handle, BUTTON_CMD_SET_ADD_ATTACH, (void *) &button_attr[index]);
    }

    return;
}

void crash_adc_init(void) {
    memset(crash_adc_info, 0, sizeof(crash_adc_info));

    crash_adc_info[0].adc_info         = &cliff_sensor_objs[7];
    crash_adc_info[0].index            = BUTTON_SECURITY_CRASH_1;
    crash_adc_info[0].adc_button_state = BUTTON_EVENT_MAX;

    crash_adc_info[1].adc_info         = &cliff_sensor_objs[8];
    crash_adc_info[1].index            = BUTTON_SECURITY_CRASH_4;
    crash_adc_info[1].adc_button_state = BUTTON_EVENT_MAX;
}

void print_cliff_type(void) {
    if (cliff_type == CLIFF_TYPE_PSD) {
        log_i("cliff type is psd");
    } else if (cliff_type == CLIFF_TYPE_IR) {
        log_i("cliff type is ir");
    } else {
        log_e("cliff type is unknown!");
    }
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), print_cliff_type, print_cliff_type, print_cliff_type);

void set_cliff_type(uint8_t type) {
    uint8_t type_temp = type;
    if (type == CLIFF_TYPE_PSD) {
        device_ioctl(cliff_oml_handle1, CLIFF_CMD_DISABLE, NULL);
        device_ioctl(cliff_psd_handle1, CLIFF_CMD_ENABLE, NULL);
        cliff_type   = CLIFF_TYPE_PSD;
        cliff_handle = cliff_psd_handle1;
    } else if (type_temp == CLIFF_TYPE_IR) {
        device_ioctl(cliff_psd_handle1, CLIFF_CMD_DISABLE, NULL);
        device_ioctl(cliff_oml_handle1, CLIFF_CMD_ENABLE, NULL);
        cliff_type   = CLIFF_TYPE_IR;
        cliff_handle = cliff_oml_handle1;
    } else {
        log_e("set cliff type error, psd:%d,ir:%d", CLIFF_TYPE_PSD, CLIFF_TYPE_IR);
    }
    print_cliff_type();
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), set_cliff_type, set_cliff_type, set_cliff_type);

void check_cliff_type(void) {
    device_ioctl(cliff_oml_handle1, CLIFF_CMD_DISABLE, NULL);
    device_ioctl(cliff_psd_handle1, CLIFF_CMD_DISABLE, NULL);

    if (device_ioctl(cliff_psd_handle1, CLIFF_CMD_IS_TYPE, NULL)) {
        cliff_type = CLIFF_TYPE_PSD;
        device_ioctl(cliff_psd_handle1, CLIFF_CMD_ENABLE, NULL);
        cliff_handle = cliff_psd_handle1;
    } else {
        cliff_type = CLIFF_TYPE_IR;
        device_ioctl(cliff_oml_handle1, CLIFF_CMD_ENABLE, NULL);
        cliff_handle = cliff_oml_handle1;
    }

    print_cliff_type();
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), check_cliff_type, check_cliff_type, check_cliff_type);

void cliff_init(void) {
    for (int i = 0; i < CLIFF_MAX; i++) {
        device_ioctl(cliff_oml_handle1, CLIFF_CMD_SET_DATA, (void *) &cliff_sensor_objs[i]);
        device_ioctl(cliff_psd_handle1, CLIFF_CMD_SET_DATA, (void *) &cliff_sensor_objs[i]);
    }

    device_ioctl(cliff_oml_handle1, CLIFF_CMD_DISABLE, NULL);
    device_ioctl(cliff_psd_handle1, CLIFF_CMD_DISABLE, NULL);
}

int fal_security_init(void) {
    cliff_init();
    list_init();
    crash_adc_init();
    fal_security_button_init();
    return 0;
}

FAL_MODULE_INIT(fal_security_init);

/*****************************************************************
 * 函数定义
 *注意，编写函数需首先定义所有的局部变量等，不允许在
 *函数的中间出现新的变量的定义。
 ******************************************************************/

#ifdef __cplusplus
}
#endif

/* @} Robot_PAL_UROS */
/* @} Robot_PAL */
