/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:
 ** Author:         畅一晨
 ** Version:        V0.0.1
 ** Date:           2022-9-4
 ** Description:		microROS安全管理
 ** Others:
 ** Function List:
 ** History:        2022-9 畅一晨 创建
 ** <time>          <author>    <version >    <desc>
 ** 2021-3-25       曾曼云						1.0         创建文件
 ******************************************************************/

/*****************************************************************
 * 包含头文件
 ******************************************************************/
#define LOG_TAG "fal_clean"
#include "log.h"
#include "pal_security.h"
#include "fal.h"
#include "devices.h"
#include "define.h"
#include <stdarg.h>
#include <stdio.h>
#include <string.h>
#include "define_button.h"
#include "pal_zbus.h"
#include "define_motor.h"
#include "fal_security.h"
#include "mem_pool.h"
#include "pal_log.h"
#include "shell.h"
#include "fal_charge.h"
#include "water_position_scw.h"
#include "fal_led.h"
#include "hal.h"
#include "fal_clean.h"
#include "fal_motor.h"
#include "utils_tick.h"
#include "define_carpet.h"
#include "pal_clean.h"
#include "adc_reference.h"
/**
 * @addtogroup Robot_PAL 协议适配层 - PAL
 * @{
 */

/**
 * @defgroup Robot_PAL_UROS microROS接口处理
 *
 * @brief
 * \n
 * @{
 */

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 私有宏定义
 ******************************************************************/
#define DRIVE_WHEEL_ERROR_TICK       (60 * 1000)
#define SEWAGE_BOX_STATE_FILTER_TIME 10  // Total_Time = 200ms * SEWAGE_BOX_STATE_FILTER_TIME

#define SEWAGE_TANK_FULL_JUDGE_TICKS  6
#define SEWAGE_TANK_FULL_LOWWER_LIMIT 2500
#define SEWAGE_TANK_FULL_UPPER_LIMIT  3000
/*****************************************************************
 * 私有结构体/共用体/枚举定义
 ******************************************************************/
float module_table[MOTOR_MAX][MOTOR_INFO_TYPE_MAX] = {
#ifdef DEFINE_UP_DOWN_PUSH_ROD
    {(3.3 / 4096 / 8.5 / 0.3), 8.5, 0.3, 60.0 / 331 / 6},  //推杆
#endif

#ifdef DEFINE_SIDE_BRUSH
    {(3.3 / 4096 / 8.5 / 0.3), 8.5, 0.3, 60.0 / 21 / 6},  //边刷
#endif

#ifdef DEFINE_ROLLER_BRUSH
    {(3.3 / 4096 / 8.5 / 0.15), 8.5, 0.15, 60.0 / 6.8 / 3},  //滚刷
#endif

#ifdef DEFINE_ROLLER_TUBE
    {(3.3 / 4096 / 8.5 / 0.15), 8.5, 0.15, 60.0 / 28 / 2},  //滚筒
#endif

#ifdef DEFINE_CLEAN_WATER_PUMP
    {(3.3 / 4096 / 8.5 / 0.51), 8.5, 0.51, 60},  //清水泵
#endif

#ifdef DEFINE_SEWAGE_WATER_PUMP
    {(3.3 / 4096 / 8.5 / 0.51), 8.5, 0.51, 60.0 / 3900},  //污水泵
#endif

#ifdef DEFINE_FAN_MOTOR
    {(3.3 / 4096 / 20 / 0.01), 20, 0.01, 60},  //风机
#endif

#ifdef DEFINE_DRIVER
    {(3.3 / 4096 / 6 / 0.15), 6, 0.15, 60.0},  //驱动轮
#endif
};

//正常（占位，改位暂时不用),堵转,反馈异常,短路,电机异常,电流采样异常,过流,不在位
static const PARAMS_ATTR_T default_params[] = {
    //升降电机, 特殊处理,不参与通用电机检测
    {
        .enable                 = {0, 0, 0, 0, 0, 0, 0, 0},
        .open_state             = {0, 0, 1, 0, 0, 0, 1, 0},
        .currert_state          = {0, 0, 0, 4, 0, 0, 1, 0},
        .rpm_state              = {0, 0, 1, 0, 0, 0, 0, 0},
        .small_abnormal_current = 0,
        .small_abnormal_cnt     = 0,
        .big_abnormal_current   = 300,
        .current_abnormal_cnt   = 2,
        .abnormal_rpm           = 0,
        .rpm_abnormal_cnt       = 0,
        .retry_cnt              = 0,
        .delay_det_ms           = 3000,
        .delay_retry_ms         = 1000,
    },
    //边刷
    {
        .enable                 = {0, 1, 1, 1, 0, 0, 1, 0},
        .open_state             = {0, 1, 1, 0, 1, 1, 1, 1},
        .currert_state          = {0, 1, 0, 4, 2, 2, 1, 3},
        .rpm_state              = {0, 1, 1, 0, 1, 0, 0, 0},
        .small_current          = 0,
        .reign_current          = 0,
        .small_abnormal_current = 200,
        .small_abnormal_cnt     = 5,
        .big_abnormal_current   = 320,
        .current_abnormal_cnt   = 6,
        .short_circuit_current  = 800,
        .abnormal_rpm           = 90,
        .big_abnormal_rpm       = 170,
        .rpm_abnormal_cnt       = 3,
        .rpm_lower_limit_gear   = 90,
        .retry_cnt              = 2,
        .delay_det_ms           = 15000,
        .delay_retry_ms         = 20000,
    },
    //中扫
    {
        .enable                 = {0, 1, 1, 1, 1, 0, 1, 0},
        .open_state             = {0, 1, 1, 0, 1, 1, 1, 1},
        .currert_state          = {0, 1, 0, 4, 2, 2, 1, 3},
        .rpm_state              = {0, 1, 1, 0, 1, 0, 0, 0},
        .small_current          = 60,
        .small_current_cnt      = 3,
        .reign_current          = 0,
        .small_abnormal_current = 400,
        .small_abnormal_cnt     = 20,  // 出现20次认为是小过流, 出现3次小过流则上报过流, 20*3 = 60次
        .big_abnormal_current   = 1500,
        .current_abnormal_cnt   = 3,
        .short_circuit_current  = 500,
        .abnormal_rpm           = 200,
        .big_abnormal_rpm       = 1700,
        .rpm_abnormal_cnt       = 3,
        .rpm_lower_limit_gear   = 30,
        .retry_cnt              = 0,
        .delay_det_ms           = 5000,
        .delay_retry_ms         = 1000,
    },
    //滚筒
    {
        .enable                 = {0, 1, 1, 1, 1, 0, 1, 0},
        .open_state             = {0, 1, 1, 0, 1, 1, 1, 1},
        .currert_state          = {0, 1, 0, 4, 2, 2, 1, 3},
        .rpm_state              = {0, 1, 1, 0, 1, 0, 0, 0},
        .small_current          = 50,
        .small_current_cnt      = 3,
        .reign_current          = 150,
        .reign_current_cnt      = 3,
        .reign_gear             = 5,
        .small_abnormal_current = 1400,
        .small_abnormal_cnt     = 4,
        .big_abnormal_current   = 1700,
        .current_abnormal_cnt   = 1,
        .short_circuit_current  = 1000,
        .abnormal_rpm           = 50,
        .big_abnormal_rpm       = 1000,
        .rpm_abnormal_cnt       = 2,
        .rpm_lower_limit_gear   = 50,
        .retry_cnt              = 3,
        .delay_det_ms           = 1000,
        .delay_retry_ms         = 8000,
    },
    //清水泵
    {
        .enable                 = {0, 0, 0, 0, 1, 0, 1, 0},
        .open_state             = {0, 1, 1, 0, 1, 1, 1, 1},
        .currert_state          = {0, 1, 0, 4, 2, 2, 1, 3},
        .rpm_state              = {0, 1, 1, 0, 0, 0, 0, 0},
        .small_current          = 10,
        .small_current_cnt      = 10,
        .reign_current          = 0,
        .small_abnormal_current = 0,
        .small_abnormal_cnt     = 0,
        .big_abnormal_current   = 300,
        .current_abnormal_cnt   = 3,
        .abnormal_rpm           = 0,
        .rpm_abnormal_cnt       = 0,
        .retry_cnt              = 0,
        .delay_det_ms           = 3000,
        .delay_retry_ms         = 1000,
    },
    //污水泵
    {
        .enable                 = {0, 0, 0, 0, 1, 0, 0, 0},
        .open_state             = {0, 1, 1, 0, 1, 1, 1, 1},
        .currert_state          = {0, 1, 0, 4, 2, 2, 1, 3},
        .rpm_state              = {0, 1, 1, 0, 0, 0, 0, 0},
        .small_current          = 50,
        .small_current_cnt      = 3,
        .reign_current          = 0,
        .small_abnormal_current = 0,
        .small_abnormal_cnt     = 0,
        .big_abnormal_current   = 0,
        .current_abnormal_cnt   = 0,
        .abnormal_rpm           = 0,
        .rpm_abnormal_cnt       = 5,
        .retry_cnt              = 0,
        .delay_det_ms           = 3000,
        .delay_retry_ms         = 1000,
    },
    //风机
    {
        .enable                 = {0, 1, 1, 0, 1, 0, 1, 0},
        .open_state             = {0, 1, 1, 0, 1, 1, 1, 1},
        .currert_state          = {0, 1, 0, 4, 2, 2, 1, 3},
        .rpm_state              = {0, 1, 1, 0, 1, 0, 0, 0},
        .small_current          = 550,
        .small_current_cnt      = 3,
        .reign_current          = 0,
        .small_abnormal_current = 11000,
        .small_abnormal_cnt     = 3,
        .big_abnormal_current   = 13000,
        .current_abnormal_cnt   = 2,
        .abnormal_rpm           = 1500,
        .big_abnormal_rpm       = 48000,
        .rpm_abnormal_cnt       = 3,
        .retry_cnt              = 3,
        .delay_det_ms           = 9000,
        .delay_retry_ms         = 3000,
    },
    //驱动轮
    {
        .enable                 = {0, 0, 0, 0, 0, 0, 1, 0},
        .open_state             = {0, 1, 1, 0, 1, 1, 0, 1},
        .currert_state          = {0, 1, 0, 4, 2, 2, 1, 3},
        .rpm_state              = {0, 1, 1, 0, 1, 0, 0, 0},
        .small_current          = 0,
        .reign_current          = 0,
        .small_abnormal_current = 1200,
        .small_abnormal_cnt     = 2,
        .big_abnormal_current   = 1600,
        .current_abnormal_cnt   = 1,
        .abnormal_rpm           = 0,
        .rpm_abnormal_cnt       = 0,
        .retry_cnt              = 0,
        .delay_det_ms           = 3000,
        .delay_retry_ms         = 1000,
    }};

// heap堵电流定义
struct fan_heap_det_current_t fan_heap_det_current_limit[] = {{.gear = 90, .lower_current = 900, .upper_current = 2700},
                                                              {.gear = 100, .lower_current = 900, .upper_current = 4500}};

/*****************************************************************
 * 全局变量定义
 ******************************************************************/
const osThreadAttr_t clean_attributes = {
    .name       = "clean",
    .priority   = (osPriority_t) osPriorityNormal,
    .stack_size = 512 * 4,
};

const osThreadAttr_t sewagebox_full_detect_attributes = {
    .name       = "sewagebox_full_detect",
    .priority   = (osPriority_t) osPriorityNormal,
    .stack_size = 256 * 4,
};

bool is_sewage_tank_full = false;

/*****************************************************************
 * 私有全局变量定义
 ******************************************************************/
extern struct bus_info      bus_gpio_sewage_water_groove;
extern struct bus_info      bus_gpio_dirt_box;
extern struct bus_info      bus_gpio_carpet1;
extern struct bus_info      bus_gpio_carpet2;
extern struct bus_info      bus_gpio_carpet3;
extern char *               error_str[ERROR_TYPE_MAX];
SECURITY_MOTOR_ATTR_T       motor_attr[MOTOR_MAX];
static CLEAN_BUTTON_ATTR_T  clean_button_info[BUTTON_CLEAN_MODULE_MAX];
SENSOR_CB_ARGS_T            sensor_status_info[SENSOR_MAX];
CONSUMABLES_ATTR_T          consumables_attr[CONSUMABLES_MAX];
consumables_callback        cm_pub_callback;
static uint8_t              elevator_run_state      = LIFT_MOTOR_RUN_NORMAL;
static uint8_t              last_elevator_run_state = LIFT_MOTOR_RUN_NORMAL;
WATER_STATE_E               sewage_box_state        = WATER_POSITION_EMPTY;
static BUTTON_ATTACH_ATTR_T sewage_water_empty_attach_attr;
static BUTTON_ATTR_T        sewage_water_empty_attr;
uint8_t                     clean_water_state = CLEAN_WATER_SENSOR_NORMAL;

static uint32_t fan_hepa_det_ts            = 0;
static uint32_t fan_temp_det_ts            = 0;
static uint8_t  version_pin_state          = 0;
bool            is_have_sewage_tank_detect = false;
/*****************************************************************
 * 外部变量声明（如果全局变量没有在其它的H文件声明，引用时需在此处声明，
 *如果已在其它H文件声明，则只需包含此H文件即可）
 ******************************************************************/
extern security_t           wheel;
extern BRAKE_ENABLE_STATE_E wheel_brake_enable_flag;

extern int button_handle;
extern int wheel_handle;
extern int side_brush_handle;
extern int roller_brush_handle;
extern int roller_tube_handle;
extern int clean_water_pump_handle;
extern int sewage_water_pump_handle;
extern int fan_motor_handle;
extern int up_elevator_handle;
extern int down_elevator_handle;
extern int cliff_oml_handle1;
extern int dvt_switch_handle;
extern int clean_water_handle;
extern int get_sewage_empty_handle;
extern int detect_sewage_full_handle;
extern int sewageTank_full_detect_ver_handle;
extern int power_12V_handle;
extern int gpio_version1_handle;

extern struct bus_info bus_gpio_sewage_water_empty;
extern struct bus_info bus_gpio_sewage_water_check;

extern bool component_state_update_enable;
/*****************************************************************
 * 私有函数原型声明
 ******************************************************************/
void sewage_empty_update(void);
void clean_moudle_status_pub(uint8_t index, uint8_t state, bool is_update);
void clean_sensor_status_pub(uint8_t index, uint8_t state, bool is_update);
void motor_status_pub(uint8_t index);

int clean_button_callback(uint8_t index, BUTTON_STATUS_TYPE state) {
    if ((index == BUTTON_CLEAN_CARPET1) && (state == BUTTON_PRESS_DOWN)) {  //跳过1号地毯触发
        return 0;
    }
    clean_button_info[index].state = state;
    // log_d("clean_button_callback index = %d, state = %d", index, state);
    return 0;
}
void sewage_empty_update(void) {
    uint8_t sewage_flag = 0;
    device_read(get_sewage_empty_handle, &sewage_flag, sizeof(sewage_flag));
    if (!sewage_flag) {
        sewage_box_state = WATER_POSITION_NORMAL;
    } else {
        sewage_box_state = WATER_POSITION_EMPTY;
    }
    log_w(" sewage_empty_update success! ");
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), sewage_empty_update, sewage_empty_update, sewage_empty_update);
int sewage_water_empty_callback(uint8_t index, BUTTON_STATUS_TYPE type) {
    switch (type) {
        case BUTTON_PRESS_UP:
            sewage_box_state = WATER_POSITION_NORMAL;
            break;
        case BUTTON_PRESS_DOWN:
            sewage_box_state = WATER_POSITION_EMPTY;
            break;
        case BUTTON_LONG_PRESS_START:
            sewage_box_state = WATER_POSITION_EMPTY;
            break;
        default:
            break;
    }
    log_w("sewage_box_state to %d", sewage_box_state);
    return 0;
}

WATER_STATE_E get_sewage_box_state(void) {
    log_w("sewage_box_state to %d", sewage_box_state);
    return sewage_box_state;
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), get_sewage_box_state, get_sewage_box_state,
                 get_sewage_box_state);

uint8_t drive_wheel_error_callback(int num) {
    static int      cnt               = 0;
    static uint32_t driver_error_tick = 0;

    if (is_timeout(driver_error_tick, 30000)) {  //上次异常和这次异常时差超过30s则重新计数
        cnt = 1;
    } else {
        cnt++;
    }
    if (cnt >= 3) {
        //发布驱动轮异常
        if (num == 0) {
            pal_log_pub("warn", "hardware/drive_wheel", "右驱动轮异常");
            log_e("right drive wheel happen error !");
        } else {
            pal_log_pub("warn", "hardware/drive_wheel", "左驱动轮异常");
            log_e("left drive wheel happen error !");
        }
        cnt = 0;
    }
    driver_error_tick = osKernelGetTickCount();

    log_e("drive over current!");
    security_stop_ms(DRIVE_WHEEL_ERROR_TICK);
    device_ioctl(wheel_handle, MOTOR_CMD_BRAKE_DISABLE, NULL);
    return 0;
}
/*****************************************************************
 * 函数定义
 ******************************************************************/

void fal_clean_button(void) {
#ifdef DIRT_BOX
    memset(&clean_button_info[BUTTON_CLEAN_DIRT_BOX], 0, sizeof(CLEAN_BUTTON_ATTR_T));

    clean_button_info[BUTTON_CLEAN_DIRT_BOX].state      = BUTTON_PRESS_UP;
    clean_button_info[BUTTON_CLEAN_DIRT_BOX].last_state = BUTTON_PRESS_UP;

    clean_button_info[BUTTON_CLEAN_DIRT_BOX].button_attr.gpio_info            = &bus_gpio_dirt_box;
    clean_button_info[BUTTON_CLEAN_DIRT_BOX].button_attr.period               = 1000;
    clean_button_info[BUTTON_CLEAN_DIRT_BOX].button_attr.trigger_condition    = GPIO_TRIGGER_HIGH;
    clean_button_info[BUTTON_CLEAN_DIRT_BOX].button_attr.index                = BUTTON_CLEAN_DIRT_BOX;
    clean_button_info[BUTTON_CLEAN_DIRT_BOX].button_attr.long_press_hold_type = BUTTON_LONG_PRESS_HOLD_CONTINUE_TRIGGER;
    clean_button_info[BUTTON_CLEAN_DIRT_BOX].button_attr.prevent_shake_num    = 100;

    clean_button_info[BUTTON_CLEAN_DIRT_BOX].button_attach_attr.gpio_info                      = &bus_gpio_dirt_box;
    clean_button_info[BUTTON_CLEAN_DIRT_BOX].button_attach_attr.attach.button_press_down       = 1;
    clean_button_info[BUTTON_CLEAN_DIRT_BOX].button_attach_attr.attach.button_press_up         = 1;
    clean_button_info[BUTTON_CLEAN_DIRT_BOX].button_attach_attr.attach.button_long_press_start = 1;
    clean_button_info[BUTTON_CLEAN_DIRT_BOX].button_attach_attr.button_callback                = clean_button_callback;
    clean_button_info[BUTTON_CLEAN_DIRT_BOX].button_attach_attr.gpio_exti_callback             = NULL;

#endif

#ifdef SEWAGE_WATRE_GROOVE
    memset(&clean_button_info[BUTTON_CLEAN_SEWAGE_WATRE_GROOVE], 0, sizeof(CLEAN_BUTTON_ATTR_T));

    clean_button_info[BUTTON_CLEAN_SEWAGE_WATRE_GROOVE].state      = BUTTON_PRESS_UP;
    clean_button_info[BUTTON_CLEAN_SEWAGE_WATRE_GROOVE].last_state = BUTTON_PRESS_UP;

    clean_button_info[BUTTON_CLEAN_SEWAGE_WATRE_GROOVE].button_attr.gpio_info            = &bus_gpio_sewage_water_groove;
    clean_button_info[BUTTON_CLEAN_SEWAGE_WATRE_GROOVE].button_attr.period               = 1000;
    clean_button_info[BUTTON_CLEAN_SEWAGE_WATRE_GROOVE].button_attr.trigger_condition    = GPIO_TRIGGER_HIGH;
    clean_button_info[BUTTON_CLEAN_SEWAGE_WATRE_GROOVE].button_attr.index                = BUTTON_CLEAN_SEWAGE_WATRE_GROOVE;
    clean_button_info[BUTTON_CLEAN_SEWAGE_WATRE_GROOVE].button_attr.long_press_hold_type = BUTTON_LONG_PRESS_HOLD_CONTINUE_TRIGGER;
    clean_button_info[BUTTON_CLEAN_SEWAGE_WATRE_GROOVE].button_attr.prevent_shake_num    = 100;

    clean_button_info[BUTTON_CLEAN_SEWAGE_WATRE_GROOVE].button_attach_attr.gpio_info                      = &bus_gpio_sewage_water_groove;
    clean_button_info[BUTTON_CLEAN_SEWAGE_WATRE_GROOVE].button_attach_attr.attach.button_press_down       = 1;
    clean_button_info[BUTTON_CLEAN_SEWAGE_WATRE_GROOVE].button_attach_attr.attach.button_press_up         = 1;
    clean_button_info[BUTTON_CLEAN_SEWAGE_WATRE_GROOVE].button_attach_attr.attach.button_long_press_start = 1;
    clean_button_info[BUTTON_CLEAN_SEWAGE_WATRE_GROOVE].button_attach_attr.button_callback                = clean_button_callback;
    clean_button_info[BUTTON_CLEAN_SEWAGE_WATRE_GROOVE].button_attach_attr.gpio_exti_callback             = NULL;

#endif

    //污水空检测
    sewage_empty_update();  // 开机后更新排污传感器检测值
    sewage_water_empty_attr.gpio_info            = &bus_gpio_sewage_water_empty;
    sewage_water_empty_attr.index                = 0;
    sewage_water_empty_attr.trigger_condition    = GPIO_TRIGGER_HIGH;
    sewage_water_empty_attr.prevent_shake_num    = 60;  // 消抖时间 60*5 = 300
    sewage_water_empty_attr.period               = 1000;
    sewage_water_empty_attr.long_press_hold_type = BUTTON_LONG_PRESS_HOLD_SINGLE_TRIGGER;

    sewage_water_empty_attach_attr.gpio_info                      = &bus_gpio_sewage_water_empty;
    sewage_water_empty_attach_attr.attach.button_press_down       = 1;
    sewage_water_empty_attach_attr.attach.button_press_up         = 1;
    sewage_water_empty_attach_attr.attach.button_long_press_start = 1;
    sewage_water_empty_attach_attr.button_callback                = sewage_water_empty_callback;

    device_ioctl(button_handle, BUTTON_CMD_SET_ADD_BUTTON, (void *) &sewage_water_empty_attr);
    device_ioctl(button_handle, BUTTON_CMD_SET_ADD_ATTACH, (void *) &sewage_water_empty_attach_attr);

    memset(&clean_button_info[BUTTON_CLEAN_CARPET1], 0, sizeof(CLEAN_BUTTON_ATTR_T));

    clean_button_info[BUTTON_CLEAN_CARPET1].state      = BUTTON_PRESS_UP;
    clean_button_info[BUTTON_CLEAN_CARPET1].last_state = BUTTON_PRESS_UP;

    clean_button_info[BUTTON_CLEAN_CARPET1].button_attr.gpio_info            = &bus_gpio_carpet1;
    clean_button_info[BUTTON_CLEAN_CARPET1].button_attr.index                = BUTTON_CLEAN_CARPET1;
    clean_button_info[BUTTON_CLEAN_CARPET1].button_attr.trigger_condition    = GPIO_TRIGGER_LOW;
    clean_button_info[BUTTON_CLEAN_CARPET1].button_attr.prevent_shake_num    = 6;  //消抖时间 6*5 = 30ms
    clean_button_info[BUTTON_CLEAN_CARPET1].button_attr.period               = 400;
    clean_button_info[BUTTON_CLEAN_CARPET1].button_attr.long_press_hold_type = BUTTON_LONG_PRESS_HOLD_CONTINUE_TRIGGER;

    clean_button_info[BUTTON_CLEAN_CARPET1].button_attach_attr.gpio_info                      = &bus_gpio_carpet1;
    clean_button_info[BUTTON_CLEAN_CARPET1].button_attach_attr.attach.button_press_down       = 1;
    clean_button_info[BUTTON_CLEAN_CARPET1].button_attach_attr.attach.button_press_up         = 1;
    clean_button_info[BUTTON_CLEAN_CARPET1].button_attach_attr.attach.button_long_press_start = 1;
    clean_button_info[BUTTON_CLEAN_CARPET1].button_attach_attr.button_callback                = clean_button_callback;
    clean_button_info[BUTTON_CLEAN_CARPET1].button_attach_attr.gpio_exti_callback             = NULL;

    memset(&clean_button_info[BUTTON_CLEAN_CARPET2], 0, sizeof(CLEAN_BUTTON_ATTR_T));

    clean_button_info[BUTTON_CLEAN_CARPET2].state      = BUTTON_PRESS_UP;
    clean_button_info[BUTTON_CLEAN_CARPET2].last_state = BUTTON_PRESS_UP;

    clean_button_info[BUTTON_CLEAN_CARPET2].button_attr.gpio_info            = &bus_gpio_carpet2;
    clean_button_info[BUTTON_CLEAN_CARPET2].button_attr.index                = BUTTON_CLEAN_CARPET2;
    clean_button_info[BUTTON_CLEAN_CARPET2].button_attr.trigger_condition    = GPIO_TRIGGER_LOW;
    clean_button_info[BUTTON_CLEAN_CARPET2].button_attr.prevent_shake_num    = 40;  //消抖时间 40*5 = 200ms
    clean_button_info[BUTTON_CLEAN_CARPET2].button_attr.period               = 1000;
    clean_button_info[BUTTON_CLEAN_CARPET2].button_attr.long_press_hold_type = BUTTON_LONG_PRESS_HOLD_CONTINUE_TRIGGER;

    clean_button_info[BUTTON_CLEAN_CARPET2].button_attach_attr.gpio_info                      = &bus_gpio_carpet2;
    clean_button_info[BUTTON_CLEAN_CARPET2].button_attach_attr.attach.button_press_down       = 1;
    clean_button_info[BUTTON_CLEAN_CARPET2].button_attach_attr.attach.button_press_up         = 1;
    clean_button_info[BUTTON_CLEAN_CARPET2].button_attach_attr.attach.button_long_press_start = 1;
    clean_button_info[BUTTON_CLEAN_CARPET2].button_attach_attr.button_callback                = clean_button_callback;
    clean_button_info[BUTTON_CLEAN_CARPET2].button_attach_attr.gpio_exti_callback             = NULL;

    memset(&clean_button_info[BUTTON_CLEAN_CARPET3], 0, sizeof(CLEAN_BUTTON_ATTR_T));

    clean_button_info[BUTTON_CLEAN_CARPET3].state      = BUTTON_PRESS_UP;
    clean_button_info[BUTTON_CLEAN_CARPET3].last_state = BUTTON_PRESS_UP;

    clean_button_info[BUTTON_CLEAN_CARPET3].button_attr.gpio_info            = &bus_gpio_carpet3;
    clean_button_info[BUTTON_CLEAN_CARPET3].button_attr.index                = BUTTON_CLEAN_CARPET3;
    clean_button_info[BUTTON_CLEAN_CARPET3].button_attr.trigger_condition    = GPIO_TRIGGER_LOW;
    clean_button_info[BUTTON_CLEAN_CARPET3].button_attr.prevent_shake_num    = 40;  //消抖时间 40*5 = 200ms
    clean_button_info[BUTTON_CLEAN_CARPET3].button_attr.period               = 1000;
    clean_button_info[BUTTON_CLEAN_CARPET3].button_attr.long_press_hold_type = BUTTON_LONG_PRESS_HOLD_CONTINUE_TRIGGER;

    clean_button_info[BUTTON_CLEAN_CARPET3].button_attach_attr.gpio_info                      = &bus_gpio_carpet3;
    clean_button_info[BUTTON_CLEAN_CARPET3].button_attach_attr.attach.button_press_down       = 1;
    clean_button_info[BUTTON_CLEAN_CARPET3].button_attach_attr.attach.button_press_up         = 1;
    clean_button_info[BUTTON_CLEAN_CARPET3].button_attach_attr.attach.button_long_press_start = 1;
    clean_button_info[BUTTON_CLEAN_CARPET3].button_attach_attr.button_callback                = clean_button_callback;
    clean_button_info[BUTTON_CLEAN_CARPET3].button_attach_attr.gpio_exti_callback             = NULL;

    return;
}

// 非零右值比较大小，当右值为0时returm false
// 用于过滤掉异常检测参数表某些值为0时无效的情况
bool motor_non_zero_num_comp(uint16_t left_num, uint16_t non_zero_right_num, bool is_bigger) {
    if (!non_zero_right_num) {
        return false;
    }

    if (is_bigger) {
        return left_num >= non_zero_right_num;
    } else {
        return left_num < non_zero_right_num;
    }
}

// 电机异常状态标识左右电机异常
// motor_num为0时，state为 1； 为1时，state为 2
void motor_set_error_state(uint8_t motor_id, uint8_t motor_num) {
    motor_attr[motor_id].state |= (1 << (motor_num));
    motor_attr[motor_id].state_led_report |= (1 << (motor_num));
}

void motor_clear_error_state(uint8_t motor_id, uint8_t motor_num) {
    motor_attr[motor_id].state &= ~(1 << (motor_num));
    motor_attr[motor_id].state_led_report &= ~(1 << (motor_num));
}

void motor_clear_all_error_state(uint8_t motor_id) {
    motor_attr[motor_id].state            = 0;
    motor_attr[motor_id].state_led_report = 0;
}

// 电机异常错误码左右电机异常
// motor_num为0时，last_error_code为 1； 为1时，last_error_code为 2
void motor_set_last_error_code(uint8_t motor_id, uint8_t motor_num, uint8_t error_type) {
    motor_attr[motor_id].last_error_code[error_type] |= (1 << (motor_num));
}

void motor_clear_last_error_code(uint8_t motor_id, uint8_t motor_num, uint8_t error_type) {
    motor_attr[motor_id].last_error_code[error_type] &= ~(1 << (motor_num));
}

uint8_t motor_with_num_last_error_code(uint8_t motor_id, uint8_t motor_num, uint8_t error_type) {
    return (motor_attr[motor_id].last_error_code[error_type] & (1 << (motor_num)));
}

uint8_t motor_last_error_code(uint8_t motor_id, uint8_t error_type) {
    return motor_attr[motor_id].last_error_code[error_type];
}

// 查询组件是否处于检测中
bool is_motor_in_detecting(uint8_t index, uint8_t max_motor_num) {
    bool is_detect = false;
    for (uint8_t i = 0; i < max_motor_num; i++) {
        if (motor_attr[index].motor_data.err_cnt[i] || motor_attr[index].motor_data.small_err_cnt[i] ||
            motor_attr[index].motor_data.small_cnt[i] || motor_attr[index].motor_data.currert_state[i] ||
            motor_attr[index].motor_data.reign_cnt[i] || motor_attr[index].motor_data.rpm_error_cnt[i] ||
            motor_attr[index].motor_data.rpm_state[i]) {
            is_detect = true;
        }
    }
    return is_detect;
}

void motor_error_led(void) {
    cycle_led_t motor_error_led;
    motor_error_led.cycle      = 2000;
    motor_error_led.duty_cycle = 0.5;
    if (MOTOR_STATE_NORMAL != motor_attr[MOTOR_SECURITY_SIDE_BRUSH].state_led_report ||
        MOTOR_STATE_NORMAL != motor_attr[MOTOR_SECURITY_UP_DOWN_PUSH_ROD].state_led_report ||
        MOTOR_STATE_NORMAL != motor_attr[MOTOR_SECURITY_FAN_MOTOR].state_led_report ||
        MOTOR_STATE_NORMAL != motor_attr[MOTOR_SECURITY_ROLLER_BRUSH].state_led_report ||
        MOTOR_STATE_NORMAL != motor_attr[MOTOR_SECURITY_ROLLER_TUBE].state_led_report ||
        MOTOR_STATE_NORMAL != motor_attr[MOTOR_SECURITY_DRIVER].state) {
        priority_control_set(&led_control, ERROR_LVL, led_red_cycle, &motor_error_led, "motor_error");
    }
}

void clear_motor_led_state(void) {
    for (int index = 0; index < MOTOR_MAX; index++) {
        motor_attr[index].state_led_report = MOTOR_STATE_NORMAL;
    }
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), clear_motor_led_state, clear_motor_led_state,
                 clear_motor_led_state);

//寻找当前index所对应组件的错误类型
int find_last_error_code_num(uint8_t index) {
    int motor_num = 0;
    if (motor_attr[index].state != MOTOR_STATE_NORMAL) {
        for (int i = OVER_LOAD; i < ERROR_TYPE_MAX; i++) {
            if (motor_last_error_code(index, i))
                return i;
        }
    }
    return 0;
}

void reset_motor_det_ts(MOTOR_E index) {
    motor_attr[index].motor_data.det_delay_ts = osKernelGetTickCount();
    if (index == MOTOR_SECURITY_FAN_MOTOR) {
        fan_hepa_det_ts = osKernelGetTickCount();
        fan_temp_det_ts = osKernelGetTickCount();
    }
}

void reset_all_motor_det_ts(void) {
    for (MOTOR_E i = 0; i < MOTOR_MAX; i++) {
        reset_motor_det_ts(i);
    }
}

void register_cm_pub_cb(consumables_callback cb) {
    if (cb == NULL) {
        log_e("register consumables callback is null");
        return;
    }
    cm_pub_callback = cb;
}

// 1000ms跑一次
void security_motor_handle(void) {
    // 风机异常检测逻辑参数定义
    static uint8_t  fan_last_gear          = 0;  // 风机上一次挡位
    uint8_t         fan_high_temp_io_state = 0;
    uint16_t        fan_det_delay_ms       = 5000;  //风机延迟检测时间
    MOTOR_CB_ARGS_T motor_args;

    uint8_t    index      = 0;
    uint32_t   error_code = 0;
    MOTOR_DATA motor_data;
    uint8_t    motor_num = 0;

    // 用于应用层主动update
    if (component_state_update_enable == true) {
        for (index = 0; index < MOTOR_MAX; index++) {
            error_code = find_last_error_code_num(index);
            motor_error_code_pub(index, error_code, true);
            osDelay(20);
        }
        for (index = 0; index < BUTTON_CLEAN_CARPET1; index++) {
            clean_moudle_status_pub(index, clean_button_info[index].state, true);
            osDelay(20);
        }
        for (index = 0; index < SENSOR_MAX; index++) {
            clean_sensor_status_pub(index, sensor_status_info[index].error_code, true);
            osDelay(20);
        }
        // TODO: pub 集水槽水位状态

        component_state_update_enable = false;
    }

    if (READ_BIT(wheel.state, EMERG_CMD) == 1) {
        return;
    }

    // 升降电机特殊处理
    // 过流
    if (motor_attr[MOTOR_SECURITY_UP_DOWN_PUSH_ROD].motor_data.currert_state[0] == CURRENT_ERROR_OVER_CURRENT) {
        // 过流上报
        if (!motor_with_num_last_error_code(MOTOR_SECURITY_UP_DOWN_PUSH_ROD, 0, OVER_CURRENT)) {
            motor_set_last_error_code(MOTOR_SECURITY_UP_DOWN_PUSH_ROD, 0, OVER_CURRENT);
            // 上报log给平台
            motor_error_log_pub(MOTOR_SECURITY_UP_DOWN_PUSH_ROD, OVER_CURRENT, 0);
        }
    } else {
        motor_clear_last_error_code(MOTOR_SECURITY_UP_DOWN_PUSH_ROD, 0, OVER_CURRENT);
    }
    // 到位检测
    elevator_run_state = get_elevator_run_state();
    if (LIFT_MOTOR_RUN_TIMEOUT == elevator_run_state) {
        if (!motor_with_num_last_error_code(MOTOR_SECURITY_UP_DOWN_PUSH_ROD, 0, FG_ABNORMAL) ||
            motor_attr[MOTOR_SECURITY_UP_DOWN_PUSH_ROD].state_upload_flag) {
            motor_attr[MOTOR_SECURITY_UP_DOWN_PUSH_ROD].state_upload_flag = 0;

            motor_error_code_pub(MOTOR_SECURITY_UP_DOWN_PUSH_ROD, FG_ABNORMAL, false);

            if (!motor_with_num_last_error_code(MOTOR_SECURITY_UP_DOWN_PUSH_ROD, 0, FG_ABNORMAL)) {
                motor_error_log_pub(MOTOR_SECURITY_UP_DOWN_PUSH_ROD, FG_ABNORMAL, 0);
            }

            motor_set_error_state(MOTOR_SECURITY_UP_DOWN_PUSH_ROD, 0);
            motor_set_last_error_code(MOTOR_SECURITY_UP_DOWN_PUSH_ROD, 0, FG_ABNORMAL);
            // 上报错误码

            log_e("lift error, ctrl to up!");
            ctrl_elevator_up();
        }
    } else if (LIFT_MOTOR_RUN_NORMAL == elevator_run_state) {
        if (motor_with_num_last_error_code(MOTOR_SECURITY_UP_DOWN_PUSH_ROD, 0, FG_ABNORMAL) ||
            motor_attr[MOTOR_SECURITY_UP_DOWN_PUSH_ROD].state_upload_flag) {
            motor_attr[MOTOR_SECURITY_UP_DOWN_PUSH_ROD].state_upload_flag = 0;

            motor_clear_last_error_code(MOTOR_SECURITY_UP_DOWN_PUSH_ROD, 0, FG_ABNORMAL);
            // 上报恢复
            motor_error_code_pub(MOTOR_SECURITY_UP_DOWN_PUSH_ROD, 0, false);
        }
    }
    last_elevator_run_state = elevator_run_state;

    // 风机特殊逻辑处理
    // 过温
    if (motor_attr[MOTOR_SECURITY_FAN_MOTOR].motor_data.open_state && is_timeout(fan_temp_det_ts, fan_det_delay_ms)) {
        motor_num              = 0;
        fan_high_temp_io_state = device_ioctl(motor_attr[MOTOR_SECURITY_FAN_MOTOR].handle, MOTOR_CMD_GET_GPIO_ERROR, &motor_num);
        if (fan_high_temp_io_state && !motor_last_error_code(MOTOR_SECURITY_FAN_MOTOR, FAN_HIGH_TEMP_ERROR)) {
            log_e("fan high temp error! io,state:%d", fan_high_temp_io_state);
            motor_set_error_state(MOTOR_SECURITY_FAN_MOTOR, 0);
            motor_set_last_error_code(MOTOR_SECURITY_FAN_MOTOR, 0, FAN_HIGH_TEMP_ERROR);
            // 上报错误码
            motor_error_code_pub(MOTOR_SECURITY_FAN_MOTOR, FAN_HIGH_TEMP_ERROR, false);
            // 上报log给平台
            motor_error_log_pub(MOTOR_SECURITY_FAN_MOTOR, FAN_HIGH_TEMP_ERROR, motor_num);
            int rpm_0 = 0;
            device_ioctl(motor_attr[MOTOR_SECURITY_FAN_MOTOR].handle, MOTOR_CMD_SET_RPM, (void *) &rpm_0);
            fan_temp_det_ts = osKernelGetTickCount();
        } else {
            log_d("fan high temp error clear!!!");
            motor_clear_last_error_code(MOTOR_SECURITY_FAN_MOTOR, 0, FAN_HIGH_TEMP_ERROR);
        }
    }
    // 海帕检测
    // 获取风机档位，根据不同档位应用不同电流参数进行海帕堵检测
    for (uint8_t i = 0; i < sizeof(fan_heap_det_current_limit) / sizeof(fan_heap_det_current_limit[0]); i++) {
        if (motor_attr[MOTOR_SECURITY_FAN_MOTOR].motor_data.retrying ||
            (motor_attr[MOTOR_SECURITY_FAN_MOTOR].motor_data.current_gear != fan_last_gear &&
             motor_attr[MOTOR_SECURITY_FAN_MOTOR].motor_data.current_gear == fan_heap_det_current_limit[i].gear)) {
            fan_hepa_det_ts = osKernelGetTickCount();
            break;
        }
    }
    fan_last_gear = motor_attr[MOTOR_SECURITY_FAN_MOTOR].motor_data.current_gear;

    for (uint8_t i = 0; i < sizeof(fan_heap_det_current_limit) / sizeof(fan_heap_det_current_limit[0]); i++) {
        if (motor_attr[MOTOR_SECURITY_FAN_MOTOR].motor_data.open_state && is_timeout(fan_hepa_det_ts, fan_det_delay_ms) &&
            motor_attr[MOTOR_SECURITY_FAN_MOTOR].motor_data.current_gear == fan_heap_det_current_limit[i].gear) {
            if (motor_attr[MOTOR_SECURITY_FAN_MOTOR].motor_data.moudle_gain_current > fan_heap_det_current_limit[i].lower_current &&
                motor_attr[MOTOR_SECURITY_FAN_MOTOR].motor_data.moudle_gain_current < fan_heap_det_current_limit[i].upper_current) {
                log_e("current:%.2f, fan hepa error", motor_attr[MOTOR_SECURITY_FAN_MOTOR].motor_data.moudle_gain_current);
                motor_set_error_state(MOTOR_SECURITY_HEPA, 0);
                motor_set_last_error_code(MOTOR_SECURITY_HEPA, 0, FAN_HEPA_ERROR);
                // 上报错误码
                motor_error_code_pub(MOTOR_SECURITY_HEPA, FAN_HEPA_ERROR, false);
                // 上报log给平台
                motor_error_log_pub(MOTOR_SECURITY_FAN_MOTOR, FAN_HEPA_ERROR, motor_num);
                // int rpm_0 = 0;
                // device_ioctl(motor_attr[MOTOR_SECURITY_FAN_MOTOR].handle, MOTOR_CMD_SET_RPM, (void *) &rpm_0);
            } else {
                log_d("clear heap error");
                motor_clear_last_error_code(MOTOR_SECURITY_HEPA, 0, FAN_HEPA_ERROR);
                // 上报错误码
                motor_error_code_pub(MOTOR_SECURITY_HEPA, MOTOR_NORMAL, false);
            }
        }
    }
}

void clear_current_det_cnt_state(uint8_t index, uint8_t motor_num) {
    // 电流异常清除
    motor_attr[index].motor_data.err_cnt[motor_num]       = 0;
    motor_attr[index].motor_data.small_err_cnt[motor_num] = 0;
    motor_attr[index].motor_data.small_cnt[motor_num]     = 0;
    motor_attr[index].motor_data.reign_cnt[motor_num]     = 0;
    log_i("clear current state");
}

void clear_all_det_state(uint8_t index, uint8_t motor_num) {
    // 电流异常清除
    motor_attr[index].motor_data.err_cnt[motor_num]       = 0;
    motor_attr[index].motor_data.small_err_cnt[motor_num] = 0;
    motor_attr[index].motor_data.small_cnt[motor_num]     = 0;
    motor_attr[index].motor_data.reign_cnt[motor_num]     = 0;
    motor_attr[index].motor_data.currert_state[motor_num] = 0;
    motor_attr[index].motor_data.retry_cnt[motor_num]     = 0;

    // 转速异常清除
    motor_attr[index].motor_data.rpm_error_cnt[motor_num] = 0;
    motor_attr[index].motor_data.rpm_state[motor_num]     = 0;
    log_i("clear all state");
}

void motor_current_short_circuit_det(uint8_t index, uint16_t det_current, uint8_t motor_num) {
    if (motor_attr[index].motor_data.open_state || !motor_attr[index].motor_table.enable[SHORT_CIRCUIT]) {
        return;
    }

    // 短路检测
    if (motor_non_zero_num_comp(det_current, motor_attr[index].motor_table.short_circuit_current, true)) {
        motor_attr[index].motor_data.currert_state[motor_num] = CURRENT_ERROR_SHORT_CIRCUIT;
        log_e("motor:%s,motor_num:%d, currert_state error:%d", motor_attr[index].motor_data.name[motor_num], motor_num,
              motor_attr[index].motor_data.currert_state[motor_num]);
    }
}

/*电流状态判断，电机运行时电流连续x次低于小电流异常值，则视为电流过小异常；
电机运行时电流连续y次高于小电流异常值，低于未安装值，则视为未安装异常；
电机电流连续a次高于小过流值，或高于大过流值则异常+1，异常大于b算电流过大异常。
其余情况均为电流正常。*/
void motor_running_current_error_det(uint8_t index, uint16_t det_current, uint8_t motor_num, uint8_t gear) {
    // 驱动轮没有开关状态，使用驱动轮使能标志位作为检测依据
    if (index != MOTOR_SECURITY_DRIVER) {
        if (!motor_attr[index].motor_data.open_state) {
            return;
        }
    } else {
        if (get_security_wheel_state_bit(STOP_CMD)) {
            return;
        }
    }

    bool is_current_error = false;

    if (motor_non_zero_num_comp(det_current, motor_attr[index].motor_table.big_abnormal_current, true)) {
        // 大过流检测
        motor_attr[index].motor_data.err_cnt[motor_num]++;
        log_w("motor:%s, motor_num:%d, big over current, %d mA, current err cnt  :%d", motor_attr[index].motor_data.name[motor_num], motor_num,
              det_current, motor_attr[index].motor_data.err_cnt[motor_num]);
    } else if (motor_non_zero_num_comp(det_current, motor_attr[index].motor_table.small_abnormal_current, true)) {
        // 小过流检测
        motor_attr[index].motor_data.small_err_cnt[motor_num]++;
        log_w("motor:%s, motor_num:%d, small over current, %d mA, cnt:%d", motor_attr[index].motor_data.name[motor_num], motor_num, det_current,
              motor_attr[index].motor_data.small_err_cnt[motor_num]);
        if (motor_attr[index].motor_data.small_err_cnt[motor_num] >= motor_attr[index].motor_table.small_abnormal_cnt) {
            motor_attr[index].motor_data.err_cnt[motor_num]++;
            log_w("motor:%s, motor_num:%d, small over current cnt >= %d, current err cnt :%d", motor_attr[index].motor_data.name[motor_num],
                  motor_num, motor_attr[index].motor_data.small_err_cnt[motor_num], motor_attr[index].motor_data.err_cnt[motor_num]);
            motor_attr[index].motor_data.small_err_cnt[motor_num] = 0;
        }
    } else if (motor_non_zero_num_comp(det_current, motor_attr[index].motor_table.small_current, false)) {
        // 电流过小异常
        motor_attr[index].motor_data.small_cnt[motor_num]++;
        log_w("motor:%s, motor_num:%d, too small current, %d mA, cnt:%d", motor_attr[index].motor_data.name[motor_num], motor_num, det_current,
              motor_attr[index].motor_data.small_cnt[motor_num]);

        if (++motor_attr[index].motor_data.small_cnt[motor_num] >= motor_attr[index].motor_table.small_current_cnt) {
            motor_attr[index].motor_data.currert_state[motor_num] = CURRENT_ERROR_TOO_SMALL;
            is_current_error                                      = true;
        }
    } else if (motor_non_zero_num_comp(det_current, motor_attr[index].motor_table.small_current, true) &&
               motor_non_zero_num_comp(det_current, motor_attr[index].motor_table.reign_current, false) &&
               gear == motor_attr[index].motor_table.reign_gear) {
        // 不在位检测，不在位检测电流与档位强相关
        motor_attr[index].motor_data.reign_cnt[motor_num]++;
        log_w("motor:%s, motor_num:%d, no install current, %d mA, count:%d", motor_attr[index].motor_data.name[motor_num], motor_num,
              det_current, motor_attr[index].motor_data.reign_cnt[motor_num]);
        if (motor_attr[index].motor_data.reign_cnt[motor_num] >= motor_attr[index].motor_table.reign_current_cnt) {
            motor_attr[index].motor_data.currert_state[motor_num] = CURRENT_ERROR_NO_INSTALL;
            is_current_error                                      = true;
        }
    } else {
        if (index == MOTOR_SECURITY_ROLLER_BRUSH) {
            // 中扫只有恢复到原功率才要报电流异常解除
            if (get_moudle_task_raw_rmp(MOTOR_SECURITY_ROLLER_BRUSH) == gear) {
                motor_attr[index].motor_data.err_cnt[motor_num]       = 0;
                motor_attr[index].motor_data.small_err_cnt[motor_num] = 0;
                motor_attr[index].motor_data.small_cnt[motor_num]     = 0;
                motor_attr[index].motor_data.reign_cnt[motor_num]     = 0;
            }
        } else {
            motor_attr[index].motor_data.err_cnt[motor_num]       = 0;
            motor_attr[index].motor_data.small_err_cnt[motor_num] = 0;
            motor_attr[index].motor_data.small_cnt[motor_num]     = 0;
            motor_attr[index].motor_data.reign_cnt[motor_num]     = 0;
        }
    }

    // 过流
    if (motor_non_zero_num_comp(motor_attr[index].motor_data.err_cnt[motor_num], motor_attr[index].motor_table.current_abnormal_cnt, true)) {
        if (index == MOTOR_SECURITY_ROLLER_BRUSH) {
            // 中扫降低到最低功率还过流才置位
            if (gear == ROLLER_BRUSH_LIMIT_LOWER_RPM) {
                motor_attr[index].motor_data.currert_state[motor_num] = CURRENT_ERROR_OVER_CURRENT;
                is_current_error                                      = true;
            }
        } else {
            motor_attr[index].motor_data.currert_state[motor_num] = CURRENT_ERROR_OVER_CURRENT;
            is_current_error                                      = true;
        }
    }

    if (!is_current_error && motor_attr[index].motor_data.currert_state[motor_num]) {
        log_w("motor:%s, motor_num:%d,currert_state resume", motor_attr[index].motor_data.name[motor_num], motor_num);
        motor_attr[index].motor_data.currert_state[motor_num] = 0;
    }

    if (motor_attr[index].motor_data.currert_state[motor_num]) {
        log_e("motor:%s, motor_num:%d, currert_state error:%d", motor_attr[index].motor_data.name[motor_num], motor_num,
              motor_attr[index].motor_data.currert_state[motor_num]);
    }
}

/* 转速状态判断，电机运行时转速连续x次低于转速异常值，则视为转速异常。其余情况均为转速正常*/
void motor_speed_error_det(uint8_t index, uint16_t rpm, uint8_t motor_num, uint8_t gear) {
    if (!motor_attr[index].fg_ic || !motor_attr[index].motor_data.open_state || !motor_attr[index].motor_table.abnormal_rpm) {
        return;
    }

    uint16_t abnormal_rpm = 0;
    // 小于测量档位时，不按设定的转速参数进行转速判断，仅判断转速为0的情况
    if (gear < motor_attr[index].motor_table.rpm_lower_limit_gear) {
        abnormal_rpm = 0;
    } else {
        abnormal_rpm = motor_attr[index].motor_table.abnormal_rpm;
    }

    if (rpm <= abnormal_rpm) {
        motor_attr[index].motor_data.rpm_error_cnt[motor_num]++;
        log_w("motor:%s, motor_num:%d, rpm error, rpm:%d count:%d,det_rpm:%d", motor_attr[index].motor_data.name[motor_num], motor_num, rpm,
              motor_attr[index].motor_data.rpm_error_cnt[motor_num], abnormal_rpm);
        if (motor_attr[index].motor_data.rpm_error_cnt[motor_num] >= motor_attr[index].motor_table.rpm_abnormal_cnt) {
            motor_attr[index].motor_data.rpm_error_cnt[motor_num] = 0;
            motor_attr[index].motor_data.rpm_state[motor_num]     = 1;
            log_e("motor:%s, rpm error", motor_attr[index].motor_data.name[motor_num]);
        }
    } else if (rpm > abnormal_rpm) {
        if (motor_attr[index].motor_data.rpm_state[motor_num]) {
            log_e("motor:%s, rpm resume", motor_attr[index].motor_data.name[motor_num]);
        }
        motor_attr[index].motor_data.rpm_error_cnt[motor_num] = 0;
        motor_attr[index].motor_data.rpm_state[motor_num]     = 0;
    }
}

void left_brush_test(uint8_t val);

void current_motor_handle(void) {
    MOTOR_CB_ARGS_T    motor_args;
    MOTOR_CURRENT_INFO motor_common_current_info          = {0};
    float              motor_common_current_filterd_value = 0.0;
    motor_common_current_info.filtered_current_value      = &motor_common_current_filterd_value;

    if (READ_BIT(wheel.state, EMERG_CMD) == 1) {
        return;
    }

    for (int index = 0; index < MOTOR_MAX; index++) {
        uint8_t error_count = 0;
        uint8_t open_state  = 0;
        motor_current_info_clear(&motor_common_current_info);

        if (index == MOTOR_SECURITY_HEPA) {
            continue;
        }

        if (motor_attr[index].motor_data.retrying) {
            //达到关断后延迟启动时间，才再尝试使能组件 ，否则保持原来关闭状态。
            if (is_timeout(motor_attr[index].motor_data.retry_delay_ts, motor_attr[index].motor_table.delay_retry_ms)) {
                device_ioctl(motor_attr[index].handle, MOTOR_CMD_ENABLE, NULL);
                motor_attr[index].motor_data.det_delay_ts = osKernelGetTickCount();
                motor_attr[index].motor_data.retrying     = false;
            } else {
                // 组件失能时也要继续检查，如检测短路情况。所以屏蔽掉 continue
                // continue;
            }
        }

        for (int motor_num = 0; motor_num < motor_attr[index].motor_num; motor_num++) {
            //组件电流获取
            motor_common_current_info.motor_num = motor_num;
            device_ioctl(motor_attr[index].handle, MOTOR_CMD_GET_CURRENT_FILTERED_VALUE, &motor_common_current_info);
            motor_attr[index].motor_data.current_gear      = device_ioctl(motor_attr[index].handle, MOTOR_CMD_GET_RPM, &motor_num);
            motor_attr[index].motor_data.filtered_ad_value = *motor_common_current_info.filtered_current_value;
            open_state                                     = motor_common_current_info.open_flag;

            // 组件发生切换开关时，重置检测时间，避免电流转速不稳定
            if (open_state != motor_attr[index].motor_data.open_state) {
                log_d("motor:%s, motor_num:%d, motor open state change, last_state:%d, now:%d reset det tick",
                      motor_attr[index].motor_data.name[motor_num], motor_num, motor_attr[index].motor_data.open_state, open_state);
                motor_attr[index].motor_data.det_delay_ts = osKernelGetTickCount();

                if (open_state) {
                    motor_attr[index].state_upload_flag = 1;
                }
            }

            motor_attr[index].motor_data.open_state = open_state;
            motor_attr[index].motor_data.num_motor  = motor_common_current_info.motor_num;
            motor_attr[index].motor_data.moudle_current =
                *motor_common_current_info.filtered_current_value * module_table[index][MOTOR_INFO_REAL_CURRENT_RATIO];
            motor_attr[index].motor_data.moudle_gain_current = motor_attr[index].motor_data.moudle_current * 1000;

            char current_name[25] = "电流_";
            strcat(current_name, motor_attr[index].motor_data.name[motor_num]);
            motor_data_update(current_name, (int) motor_attr[index].motor_data.moudle_gain_current);
            //组件转速获取
            MOTOR_RPM_INFO motor_common_rpm_info = {0};
            float          motor_fg_rpm          = 0;
            motor_common_rpm_info.fg_value       = &motor_fg_rpm;
            motor_common_rpm_info.motor_num      = motor_num;
            device_ioctl(motor_attr[index].handle, MOTOR_CMD_GET_FG, &motor_common_rpm_info);
            if (!strcmp(motor_attr[index].motor_data.name[motor_num], "左驱动轮")) {
                *motor_common_rpm_info.fg_value = -*motor_common_rpm_info.fg_value;
            }
            int  rpm          = *motor_common_rpm_info.fg_value * module_table[index][MOTOR_INFO_FG_CONVERT];
            char rpm_name[25] = "转速_";

            // 滚筒堵转大电流时，FG杂波很多，测到高于1000转速是假的，强制为0
            /*
            if (!strcmp(motor_attr[index].motor_data.name[motor_num], "滚筒") && rpm >= motor_attr[index].motor_table.big_abnormal_rpm) {
                rpm = 0;
            }
            */

            strcat(rpm_name, motor_attr[index].motor_data.name[motor_num]);
            motor_data_update(rpm_name, rpm);

            // DVT1机器已全部撤场，后续全部机器都是双边刷
            // DVT1机器无左边刷
            /*
            uint32_t gpio_dvt_switch_state = 0;
            device_read(dvt_switch_handle, &gpio_dvt_switch_state, 0);
            if (gpio_dvt_switch_state && !strcmp(motor_attr[index].motor_data.name[motor_num], "左边刷")) {
                continue;
            }
            */

            log_d("motor:%s, motor_num:%d, state:%d num:%d ad:%d d:%d mA:%d,rpm:%d", motor_attr[index].motor_data.name[motor_num], motor_num,
                  motor_attr[index].motor_data.open_state, motor_attr[index].motor_data.num_motor,
                  (int) *motor_common_current_info.filtered_current_value, (int) module_table[index][MOTOR_INFO_GAIN_RATIO],
                  (int) motor_attr[index].motor_data.moudle_gain_current, rpm);

            // 避免刚开启时电流转速不稳定
            if (!is_timeout(motor_attr[index].motor_data.det_delay_ts, motor_attr[index].motor_table.delay_det_ms)) {
                continue;
            }

            // 电流短路检测
            motor_current_short_circuit_det(index, (int) motor_attr[index].motor_data.moudle_gain_current, motor_num);
            // 电流异常检测
            motor_running_current_error_det(index, (int) motor_attr[index].motor_data.moudle_gain_current, motor_num,
                                            motor_attr[index].motor_data.current_gear);
            // 转速异常检测
            motor_speed_error_det(index, rpm, motor_num, motor_attr[index].motor_data.current_gear);

            for (size_t i = OVER_LOAD; i <= NO_PRESENT; i++) {
                if (!motor_attr[index].motor_table.enable[i]) {
                    continue;
                }

                if (motor_attr[index].motor_data.open_state != motor_attr[index].motor_table.open_state[i]) {
                    error_count += motor_attr[index].last_error_code[i];
                    continue;
                }

                if (motor_attr[index].motor_data.currert_state[motor_num] == motor_attr[index].motor_table.currert_state[i] &&
                    motor_attr[index].motor_data.rpm_state[motor_num] == motor_attr[index].motor_table.rpm_state[i]) {
                    log_d("error:%d,current:%d,rpm:%d", i,
                          motor_attr[index].motor_data.currert_state[motor_num] == motor_attr[index].motor_table.currert_state[i],
                          motor_attr[index].motor_data.rpm_state[motor_num] == motor_attr[index].motor_table.rpm_state[i]);

                    // 重试
                    int rpm_0 = 0;
                    // 组件处于短路、过流不参与重试
                    if (motor_attr[index].motor_data.open_state && i != SHORT_CIRCUIT && i != OVER_CURRENT) {
                        if (motor_attr[index].motor_data.retry_cnt[motor_num] >= motor_attr[index].motor_table.retry_cnt) {
                            log_e("motor:%s, motor_num:%d, error, close motor", motor_attr[index].motor_data.name[motor_num], motor_num);
                            device_ioctl(motor_attr[index].handle, MOTOR_CMD_SET_RPM, (void *) &rpm_0);

                            // 更新错误状态会影响其他异常检测
                            // motor_attr[index].motor_data.rpm_state[motor_num] = 0;

                            motor_attr[index].motor_data.retry_cnt[motor_num] = 0;

                            motor_attr[index].motor_data.retry_cnt[motor_num] = 0;

                            motor_attr[index].motor_data.det_delay_ts = osKernelGetTickCount();
                        } else {
                            motor_attr[index].motor_data.retry_cnt[motor_num]++;
                            log_w("motor:%s, motor_num:%d, close, retry count: %d, all count:%d", motor_attr[index].motor_data.name[motor_num],
                                  motor_num, motor_attr[index].motor_data.retry_cnt[motor_num], motor_attr[index].motor_table.retry_cnt);
                            device_ioctl(motor_attr[index].handle, MOTOR_CMD_SET_RPM, (void *) &rpm_0);
                            device_ioctl(motor_attr[index].handle, MOTOR_CMD_DISABLE, NULL);

                            // 更新错误状态会影响其他异常检测
                            // motor_attr[index].motor_data.rpm_state[motor_num] = 0;

                            motor_attr[index].motor_data.retrying       = true;
                            motor_attr[index].motor_data.det_delay_ts   = osKernelGetTickCount();
                            motor_attr[index].motor_data.retry_delay_ts = osKernelGetTickCount();
                            break;
                        }
                    }

                    // 电机异常状态标识左右电机异常，上报清除错误码时要
                    // motor_num为0时，state为 1； 为1时，state为 2
                    if (!motor_with_num_last_error_code(index, motor_num, i) || motor_attr[index].state_upload_flag) {
                        motor_attr[index].state_upload_flag = 0;

                        if (!motor_with_num_last_error_code(index, motor_num, i)) {
                            motor_error_log_pub(index, i, motor_num);
                        }

                        motor_set_last_error_code(index, motor_num, i);
                        // 上报错误码
                        motor_error_code_pub(index, i, false);
                        // 上报log给平台
                        log_w("motor:%s, error pub:%d,%s", motor_attr[index].motor_data.name[motor_num], i, error_str[i]);

                        if (i == SHORT_CIRCUIT) {
                            log_e("motor:%s, motor_num:%d, motor num:%d, short circuit off power", motor_attr[index].motor_data.name[motor_num],
                                  motor_num, motor_num);
                            device_ioctl(motor_attr[index].handle, MOTOR_CMD_STOP, &motor_num);

                            // 需要判断板卡版本，D版卡不用关12V
                            // C板卡版本右边刷电源接到了12V，要进行特殊关闭

                            int version_pin_a;
                            device_read(gpio_version1_handle, &version_pin_a, 4);

                            if (!strcmp(motor_attr[index].motor_data.name[motor_num], "右边刷") && version_pin_a == 0) {
                                pal_log_pub("warn", "c3_clean_task", "右边刷短路，关闭12V电源（含雷达）！！");
                                log_e("Right side brush shortcut, disable 12V power!");
                                device_ioctl(power_12V_handle, GPIO_ACTIVE_LOW, NULL);
                            }
                        }

                        if (motor_attr[index].error_callback) {
                            motor_attr[index].error_callback(motor_num);
                        }
                    }
                } else {
                    // 在检测中不要清除错误码
                    if (!is_motor_in_detecting(index, motor_attr[index].motor_num) && motor_with_num_last_error_code(index, motor_num, i)) {
                        motor_clear_last_error_code(index, motor_num, i);
                    }
                }

                // 过流仅上报平台, 不参与错误累计, 避免频繁上报恢复
                if (index == MOTOR_SECURITY_ROLLER_BRUSH || index == MOTOR_SECURITY_FAN_MOTOR || index == MOTOR_SECURITY_DRIVER) {
                    error_count += motor_last_error_code(index, i);
                } else {
                    if (i != OVER_CURRENT) {
                        error_count += motor_last_error_code(index, i);
                    }
                }
            }

            // 风机逻辑特殊处理，因为多了高温异常和heap堵转两种异常
            if (index == MOTOR_SECURITY_FAN_MOTOR) {
                for (uint8_t i = NO_PRESENT + 1; i <= FAN_HIGH_TEMP_ERROR; i++) {
                    error_count += motor_last_error_code(index, i);
                }
            }

            if (error_count == 0 && (motor_attr[index].state & (1 << (motor_num)) || motor_attr[index].state_upload_flag) &&
                index != MOTOR_SECURITY_UP_DOWN_PUSH_ROD) {  // 升降电机特殊处理
                if (!is_motor_in_detecting(index, motor_attr[index].motor_num)) {
                    motor_attr[index].state_upload_flag = 0;

                    // 上报组件恢复
                    motor_error_code_pub(index, MOTOR_STATE_NORMAL, false);
                    // 上报log给平台
                    if (motor_attr[index].state & (1 << (motor_num))) {
                        motor_error_log_pub(index, MOTOR_STATE_NORMAL, motor_num);
                    }
                    log_w("motor:%s pub normal", motor_attr[index].motor_data.name[motor_num]);
                    clear_all_det_state(index, motor_num);
                } else {
                    log_w("motor:%s in deteting", motor_attr[index].motor_data.name[motor_num]);
                }
            }

            if (error_count) {
                motor_set_error_state(index, motor_num);
            } else if (!is_motor_in_detecting(index, motor_attr[index].motor_num)) {
                motor_clear_error_state(index, motor_num);
            }

            // 转速无异常，电流无异常，重置retry
            if ((motor_attr[index].motor_data.currert_state[motor_num] == CURRENT_ERROR_NORMAL ||
                 motor_attr[index].motor_data.currert_state[motor_num] == CURRENT_ERROR_TOO_SMALL) &&
                !motor_attr[index].motor_data.rpm_state[motor_num] && motor_attr[index].motor_data.retry_cnt[motor_num]) {
                log_i("motor:%s reset retry cnt", motor_attr[index].motor_data.name[motor_num]);
                motor_attr[index].motor_data.retry_cnt[motor_num] = 0;
            }
        }
    }

    motor_data_publish();
}

//用于返回判断电机是否正常 true：电机正常 false：电机不正常（异常检测中或异常）
bool check_all_motor_normal(void) {
    for (int index = 0; index < MOTOR_MAX; index++) {
        for (int motor_num = 0; motor_num < motor_attr[index].motor_num; motor_num++) {
            if (motor_attr[index].motor_data.err_cnt[motor_num] || motor_attr[index].motor_data.err_cnt[motor_num] ||
                motor_attr[index].motor_data.small_err_cnt[motor_num] || motor_attr[index].motor_data.small_cnt[motor_num] ||
                motor_attr[index].motor_data.reign_cnt[motor_num] || motor_attr[index].motor_data.rpm_error_cnt[motor_num] ||
                motor_attr[index].motor_data.retry_cnt[motor_num]) {
                return false;
            }
        }
    }
    return true;
}

void button_clean_priority_control_set(uint8_t index) {
    cycle_led_t cycle_led;
    cycle_led.cycle      = 2000;
    cycle_led.duty_cycle = 0.5;
    switch (index) {
        case BUTTON_CLEAN_DIRT_BOX:
            priority_control_set(&led_control, ERROR_LVL, led_red_cycle, &cycle_led, "dust box");
            break;

        case BUTTON_CLEAN_SEWAGE_WATRE_GROOVE:
            priority_control_set(&led_control, ERROR_LVL, led_red_cycle, &cycle_led, "sewage water groove");
            break;

        default:
            break;
    }
}
void button_clean_handler(void) {
    static uint8_t  index        = 0;
    static uint32_t carpet1_tick = 0;

    for (index = 0; index < BUTTON_CLEAN_MODULE_MAX; index++) {
        if (clean_button_info[index].state != clean_button_info[index].last_state) {
            switch (clean_button_info[index].state) {
                case BUTTON_PRESS_DOWN:
                    if (index < BUTTON_CLEAN_CARPET1) {
                        // 非地毯传感器，如污水槽、尘盒
                        clean_moudle_status_pub(index, BUTTON_PRESS_DOWN, false);
                        // button_clean_priority_control_set(index);
                        log_w("NO----BUTTON_PRESS_DOWN-----index = %d, state = %d", index, clean_button_info[index].state);
                    }
                    break;

                case BUTTON_PRESS_UP:
                    if (index < BUTTON_CLEAN_CARPET1) {
                        clean_moudle_status_pub(index, BUTTON_PRESS_UP, false);
                        log_w("YES----BUTTON_PRESS_UP-----index = %d, state = %d", index, clean_button_info[index].state);
                    }
                    break;

                default:
                    break;
            }
        }
        if (clean_button_info[index].state == BUTTON_LONG_PRESS_START) {
            if (is_timeout(clean_button_info[index].last_long_tick_count, clean_button_info[index].button_attr.period)) {
                // button_clean_priority_control_set(index);
                clean_button_info[index].last_long_tick_count = osKernelGetTickCount();
            }
        }
        clean_button_info[index].last_state = clean_button_info[index].state;
    }
}

void get_carpet_state(uint8_t *carpet_state) {
    if ((clean_button_info[BUTTON_CLEAN_CARPET1].state == BUTTON_PRESS_DOWN) ||
        (clean_button_info[BUTTON_CLEAN_CARPET1].state == BUTTON_LONG_PRESS_START)) {
        carpet_state[CARPET_SENSOR_ID_1] = 1;
    } else {
        carpet_state[CARPET_SENSOR_ID_1] = 0;
    }

    if ((clean_button_info[BUTTON_CLEAN_CARPET2].state == BUTTON_PRESS_DOWN) ||
        (clean_button_info[BUTTON_CLEAN_CARPET2].state == BUTTON_LONG_PRESS_START)) {
        carpet_state[CARPET_SENSOR_ID_2] = 1;
    } else {
        carpet_state[CARPET_SENSOR_ID_2] = 0;
    }

    if ((clean_button_info[BUTTON_CLEAN_CARPET3].state == BUTTON_PRESS_DOWN) ||
        (clean_button_info[BUTTON_CLEAN_CARPET3].state == BUTTON_LONG_PRESS_START)) {
        carpet_state[CARPET_SENSOR_ID_3] = 1;
    } else {
        carpet_state[CARPET_SENSOR_ID_3] = 0;
    }
}

bool get_sewage_tank_full_state(void) {
    log_w("is_sewage_tank_full:%d", is_sewage_tank_full);
    return is_sewage_tank_full;
}

bool is_consumables_pub(uint32_t cur_time, uint32_t last_time) {
    if (cur_time > last_time) {
        if (cur_time - last_time >= FULL_USED_REPORT_TICK) {
            return true;
        }
    } else if (cur_time < last_time) {
        if (0xFFFFFFFF - last_time + cur_time >= FULL_USED_REPORT_TICK) {
            return true;
        }
    }
    return false;
}

void consumables_statistics_handler(void) {
    uint8_t index = 0;

    for (index = 0; index < CONSUMABLES_MAX; index++) {
        if (-1 == consumables_attr[index].motor_handle) {
            if (index == CONSUMABLES_RULLER_TUBE) {  //滚筒布通过滚筒降下计时
                if (get_elevator_state() == DOWN_STATE) {
                    consumables_attr[index].used_time++;
                }
            } else {
                return;
            }
        } else {
            device_ioctl(consumables_attr[index].motor_handle, MOTOR_CMD_GET_RUN_TIME, &consumables_attr[index].used_time);
        }
        if (is_consumables_pub(consumables_attr[index].used_time, consumables_attr[index].last_used_time)) {  //是否到达发布时间
            if (NULL != cm_pub_callback) {
                cm_pub_callback(consumables_attr[index].cm_name);
            }
            consumables_attr[index].last_used_time = consumables_attr[index].used_time;  //更新最近一次的发布时间
        }
    }
}

void sensor_status_handle(void) {
    SENSOR_CB_ARGS_T sensor_args;
    static uint8_t   w_sensor_Laststate          = CLEAN_WATER_SENSOR_NORMAL;
    static bool      sewage_tank_full_last_state = false;
    // Get Sensor State List
    device_ioctl(clean_water_handle, WATER_POSITION_IOCTL_GET_SENSOR_STATE, &clean_water_state);
    // ---
    for (uint8_t index = 0; index < SENSOR_MAX; index++) {
        sensor_args.index               = index;
        sensor_args.is_update           = false;
        sensor_status_info[index].index = index;
        switch (index) {
#ifdef CLEAN_WATER_SENSOR
            case SENSOR_WATER:
                if (w_sensor_Laststate != clean_water_state && CLEAN_WATER_SENSOR_NORMAL != clean_water_state) {
                    // component_state/pub
                    sensor_args.error_code = EEROR_END_BY_TASK;
                    // component_state/update
                    sensor_status_info[SENSOR_WATER].error_code = EEROR_END_BY_TASK;

                    w_sensor_Laststate = clean_water_state;
                    security_pub_data(SECURITY_SENSOR, &sensor_args);
                } else if (w_sensor_Laststate != clean_water_state && CLEAN_WATER_SENSOR_NORMAL == clean_water_state) {
                    // component_state/pub
                    sensor_args.error_code = EEROR_NORMAL_PUB;
                    // component_state/update
                    sensor_status_info[SENSOR_WATER].error_code = EEROR_NORMAL_PUB;

                    w_sensor_Laststate = clean_water_state;
                    security_pub_data(SECURITY_SENSOR, &sensor_args);
                } else {
                    //
                }
                break;
#endif
#ifdef SEWAGE_TANK_FULL_DET_SENSOR
            case SENSOR_SEWAGE_TANK_FULL:
                if (sewage_tank_full_last_state != is_sewage_tank_full) {
                    if (is_sewage_tank_full) {
                        sensor_args.error_code                                 = EEROR_CONT_TASK;
                        sensor_status_info[SENSOR_SEWAGE_TANK_FULL].error_code = EEROR_CONT_TASK;
                        security_pub_data(SECURITY_SENSOR, &sensor_args);
                    } else {
                        sensor_args.error_code                                 = EEROR_NORMAL_PUB;
                        sensor_status_info[SENSOR_SEWAGE_TANK_FULL].error_code = EEROR_NORMAL_PUB;
                        security_pub_data(SECURITY_SENSOR, &sensor_args);
                    }
                    sewage_tank_full_last_state = is_sewage_tank_full;
                }
                break;
#endif
            default:
                break;
        }
    }
}

static void clean_run(void *argument) {
    uint32_t motor_time          = 0;
    uint32_t crash_adc_time      = 0;
    uint32_t button_clean_time   = 0;
    uint32_t consumables_time    = 0;
    uint32_t motor_current_time  = 0;
    uint32_t sensor_current_time = 0;

    // 防止上电时升降电机未到位
    osDelay(5000);

    while (1) {
        if (is_timeout(button_clean_time, BUTTON_CLEAN_TICK)) {
            button_clean_handler();
            button_clean_time = osKernelGetTickCount();
        }

        if (is_timeout(motor_time, MOTOR_TICK)) {
            motor_time = osKernelGetTickCount();
            security_motor_handle();
        }

        if (is_timeout(motor_current_time, MOTOR_TICK)) {
            motor_current_time = osKernelGetTickCount();
            current_motor_handle();
        }

        if (is_timeout(consumables_time, CONSUMABLES_TICK)) {
            consumables_statistics_handler();
            consumables_time = osKernelGetTickCount();
        }

        if (is_timeout(sensor_current_time, SENSOR_CLEAN_TICK)) {
            sensor_status_handle();
            sensor_current_time = osKernelGetTickCount();
        }

        osDelay(CLEAN_TICK);
    }

    return;
}

void fal_clean_motor_init(void) {
    for (int i = 0; i < 8; i++) {
        motor_attr[i].motor_table = default_params[i];
    }

    wheel_brake_enable_flag = 0;
    enable_wheel_brake();

    motor_attr[MOTOR_SECURITY_DRIVER].handle             = wheel_handle;
    motor_attr[MOTOR_SECURITY_DRIVER].fg_ic              = true;
    motor_attr[MOTOR_SECURITY_DRIVER].motor_data.name[0] = "右驱动轮";
    motor_attr[MOTOR_SECURITY_DRIVER].motor_data.name[1] = "左驱动轮";
    motor_attr[MOTOR_SECURITY_DRIVER].motor_num          = 2;
    motor_attr[MOTOR_SECURITY_DRIVER].error_callback     = drive_wheel_error_callback;

    motor_attr[MOTOR_SECURITY_UP_DOWN_PUSH_ROD].handle             = up_elevator_handle;
    motor_attr[MOTOR_SECURITY_UP_DOWN_PUSH_ROD].fg_ic              = false;
    motor_attr[MOTOR_SECURITY_UP_DOWN_PUSH_ROD].motor_data.name[0] = "升降电机";
    motor_attr[MOTOR_SECURITY_UP_DOWN_PUSH_ROD].motor_num          = 1;

    motor_attr[MOTOR_SECURITY_SIDE_BRUSH].handle             = side_brush_handle;
    motor_attr[MOTOR_SECURITY_SIDE_BRUSH].fg_ic              = true;
    motor_attr[MOTOR_SECURITY_SIDE_BRUSH].motor_data.name[0] = "左边刷";
    motor_attr[MOTOR_SECURITY_SIDE_BRUSH].motor_data.name[1] = "右边刷";
    motor_attr[MOTOR_SECURITY_SIDE_BRUSH].motor_num          = 2;

    motor_attr[MOTOR_SECURITY_ROLLER_BRUSH].handle             = roller_brush_handle;
    motor_attr[MOTOR_SECURITY_ROLLER_BRUSH].fg_ic              = true;
    motor_attr[MOTOR_SECURITY_ROLLER_BRUSH].motor_data.name[0] = "中扫";
    motor_attr[MOTOR_SECURITY_ROLLER_BRUSH].motor_num          = 1;

    motor_attr[MOTOR_SECURITY_ROLLER_TUBE].handle             = roller_tube_handle;
    motor_attr[MOTOR_SECURITY_ROLLER_TUBE].fg_ic              = true;
    motor_attr[MOTOR_SECURITY_ROLLER_TUBE].motor_data.name[0] = "滚筒";
    motor_attr[MOTOR_SECURITY_ROLLER_TUBE].motor_num          = 1;

    motor_attr[MOTOR_SECURITY_CLEAN_WATER_PUMP].handle             = clean_water_pump_handle;
    motor_attr[MOTOR_SECURITY_CLEAN_WATER_PUMP].fg_ic              = false;
    motor_attr[MOTOR_SECURITY_CLEAN_WATER_PUMP].motor_data.name[0] = "清水泵";
    motor_attr[MOTOR_SECURITY_CLEAN_WATER_PUMP].motor_num          = 1;

    motor_attr[MOTOR_SECURITY_SEWAGE_WATER_PUMP].handle             = sewage_water_pump_handle;
    motor_attr[MOTOR_SECURITY_SEWAGE_WATER_PUMP].fg_ic              = false;
    motor_attr[MOTOR_SECURITY_SEWAGE_WATER_PUMP].motor_data.name[0] = "污水泵";
    motor_attr[MOTOR_SECURITY_SEWAGE_WATER_PUMP].motor_num          = 1;

    motor_attr[MOTOR_SECURITY_FAN_MOTOR].handle             = fan_motor_handle;
    motor_attr[MOTOR_SECURITY_FAN_MOTOR].fg_ic              = true;
    motor_attr[MOTOR_SECURITY_FAN_MOTOR].motor_data.name[0] = "风机";
    motor_attr[MOTOR_SECURITY_FAN_MOTOR].motor_num          = 1;
}

//发布当前index对应清洁模块的错误信息
void clean_moudle_status_pub(uint8_t index, uint8_t state, bool is_update) {
    MOTOR_CB_ARGS_T motor_args;
    motor_args.index     = index;
    motor_args.is_update = is_update;

    switch (state) {
        case BUTTON_PRESS_DOWN:
            motor_args.error_code = EEROR_END_BY_TASK;
            break;

        case BUTTON_PRESS_UP:
            // 恢复正常
            motor_args.error_code = EEROR_NORMAL_PUB;
            break;

        case BUTTON_LONG_PRESS_START:
            motor_args.error_code = EEROR_END_BY_TASK;
            break;

        default:
            break;
    }

    security_pub_data(SECURITY_CLEAN_MODULE, &motor_args);
}

//发布当前index对应组件的错误信息
void motor_status_pub(uint8_t index) {
    MOTOR_CB_ARGS_T motor_args;
    motor_args.index      = index;
    motor_args.error_code = find_last_error_code_num(index);
    security_pub_data(SECURITY_MOTOR, &motor_args);
}

void fal_clean_button_init(void) {
    int          index           = 0;
    osThreadId_t security_thread = osThreadNew(clean_run, NULL, &clean_attributes);

    fal_clean_button();

    for (index = 0; index < BUTTON_CLEAN_MODULE_MAX; index++) {
        device_ioctl(button_handle, BUTTON_CMD_SET_ADD_BUTTON, (void *) &clean_button_info[index].button_attr);
        device_ioctl(button_handle, BUTTON_CMD_SET_ADD_ATTACH, (void *) &clean_button_info[index].button_attach_attr);
    }

    return;
}

void fal_consumables_init(void) {
    memset(consumables_attr, 0, sizeof(consumables_attr));

    consumables_attr[CONSUMABLES_RULLER_TUBE].cm_name      = "rullerTubeRemainTime";  //滚筒布
    consumables_attr[CONSUMABLES_RULLER_TUBE].motor_handle = -1;

    consumables_attr[CONSUMABLES_SIDE_BRUSH].cm_name      = "sideBrushRemainTime";  //边刷
    consumables_attr[CONSUMABLES_SIDE_BRUSH].motor_handle = side_brush_handle;

    consumables_attr[CONSUMABLES_RULLER_BRUSH].cm_name      = "rollBrushRemainTime";  //中扫
    consumables_attr[CONSUMABLES_RULLER_BRUSH].motor_handle = roller_brush_handle;

    consumables_attr[CONSUMABLES_HEPA].cm_name      = "heapRemainTime";  // HEPA过滤网
    consumables_attr[CONSUMABLES_HEPA].motor_handle = fan_motor_handle;
}

//该线程用于持续检测污水槽槽满情况 若检测污水槽满 则及时进行停止布水与持续抽污动作
void sewage_tank_full_detect() {
    static uint32_t sewage_full_adc          = 0;
    static bool     start_flag               = false;
    static bool     is_sewage_tank_full_last = false;
    static uint8_t  tick_nums                = 0;
    TickType_t      start_ts                 = 0;
    TickType_t      xLastWakeTime            = xTaskGetTickCount();
    while (1) {
        //污水满检测
        device_ioctl(detect_sewage_full_handle, CMD_READ_RAW_ADC_VALUE, &sewage_full_adc);
        log_d("sewage_full_adc : %d", sewage_full_adc);

        if (sewage_full_adc <= SEWAGE_TANK_FULL_LOWWER_LIMIT) {
            if (!start_flag) {
                start_ts   = osKernelGetTickCount();
                start_flag = true;
            }
            tick_nums++;
        }

        //此处作用是消抖 每100ms采集一次 若1000毫秒内有大于六次都是检测到污水槽水满 则认为此时水满
        if (is_timeout(start_ts, 1000) && start_flag) {
            if (tick_nums > SEWAGE_TANK_FULL_JUDGE_TICKS) {
                is_sewage_tank_full = true;
                log_w("sewage_tank is full,adc:%d", sewage_full_adc);
            }
            tick_nums  = 0;
            start_flag = false;
        } else if ((sewage_full_adc > SEWAGE_TANK_FULL_UPPER_LIMIT) && (!start_flag)) {
            is_sewage_tank_full = false;
            log_d("sewage_tank is not full,adc:%d", sewage_full_adc);
        }

        if (is_sewage_tank_full != is_sewage_tank_full_last) {
            if (is_sewage_tank_full) {
                pal_log_pub("warn", "hardware/sewage_tank_sensor", "集水槽满");
            } else {
                pal_log_pub("info", "hardware/sewage_tank_sensor", "集水槽满恢复！");
            }
            is_sewage_tank_full_last = is_sewage_tank_full;
            // TODO:发布集水槽水满状态
        }

        vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(100));
    }
}

bool Is_have_sewage_tank_detect(void) {
    return is_have_sewage_tank_detect;
}

int fal_clean_init(void) {
    fal_clean_button_init();
    fal_clean_motor_init();
    fal_consumables_init();
    device_read(sewageTank_full_detect_ver_handle, &version_pin_state, sizeof(version_pin_state));
    if (!version_pin_state) {
        // version_pin_state 1->PTV3以前 0->PVT3以后
        is_have_sewage_tank_detect   = true;
        osThreadId_t security_thread = osThreadNew(sewage_tank_full_detect, NULL, &sewagebox_full_detect_attributes);
    }
    return 0;
}

FAL_MODULE_INIT(fal_clean_init);

/*****************************************************************
 * 函数定义
 *注意，编写函数需首先定义所有的局部变量等，不允许在
 *函数的中间出现新的变量的定义。
 ******************************************************************/

#ifdef __cplusplus
}
#endif

/* @} Robot_PAL_UROS */
/* @} Robot_PAL */
