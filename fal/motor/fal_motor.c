#define LOG_TAG "fal_motor"
#include "log.h"
#include "fal.h"
#include "devices.h"
#include "define.h"
#include <stdarg.h>
#include <stdio.h>
#include <string.h>
#include <math.h>
#include "water_position_scw.h"
#include "pal_zbus.h"
#include "shell.h"
#include "define_motor.h"
#include "gpio.h"
#include "fal_motor.h"
#include "fal.h"
#include "define_carpet.h"
#include "define_button.h"
#include "fal_security.h"
#include "fal_led.h"
#include "pal_log.h"
#include "pal_clean.h"
#include "sliding_average_filter.h"
#include "utils_tick.h"
#include "fal_clean.h"
#include "fal_common.h"
#include "adc_reference.h"
/************************** input ************************************************************/
/* handle */
extern int clean_water_pump_handle;
extern int wheel_handle;
extern int side_brush_handle;
extern int roller_brush_handle;
extern int roller_tube_handle;
extern int clean_water_pump_handle;
extern int sewage_water_pump_handle;
extern int fan_motor_handle;
extern int up_elevator_handle;    //上升电机句柄
extern int down_elevator_handle;  //下降电机句柄
extern int button_handle;         //按键句柄（光电传感器使用）
// extern int        carpet_handle;
extern int        switch_oc1_handle;
extern int        switch_oc2_handle;
extern security_t wheel;  // extern安全管理内容，为了实现清洁start时机器不运动
extern int        clean_water_handle;
extern int        battery_handle;
extern int        sewage_valve_handle;  //排污阀句柄
extern int        adc_version_handle;
extern int        adc_light_det_handle;
extern int        switch_oc2_handle;
extern int        gpio_version1_handle;
extern int        gpio_version2_handle;
/*****  调试使用相关变量  *****/
int speed_0 = 0;
int speed_1 = 1;

int fan_rpm         = 4;
int ruller_tube_rpm = 5;
int side_rush_rpm   = 3;
int ruller_rush_rpm = 10;

extern bool                  clean_task_just_start_enable;
extern SECURITY_MOTOR_ATTR_T motor_attr[MOTOR_MAX];

typedef struct clean_info {
    clean_module_ctrl_t clean_module_ctrl[CLEAN_MODULE_TYPE_MAX];        //组建控制信息
    int8_t              clean_module_rpm[CLEAN_MODULE_TYPE_MAX];         //平台传下来组建对应的rpm
    LIFT_MOTOR_E        last_elevator_status;                            //升降电机状态
    uint8_t             carpet_state;                                    //当前地毯传感器的状态
    uint8_t             carpet_state_last;                               //上次地毯传感器的状态
    uint8_t             carpet_sensor_state[CARPET_SENSOR_ID_MAX];       //存储传感器检测到的状态
    uint8_t             carpet_sensor_state_last[CARPET_SENSOR_ID_MAX];  //存储传感器上次检测到的状态
    int                 ruller_tube_72_tim;                              //滚筒转72度用的时间
    uint8_t             swp_timer;                                       //污水泵抽水时间
    uint8_t             ctrl_info_init_success_flag;                     //获取清洁信息成功
} clean_info_t;

static clean_info_t clean_info = {
    .last_elevator_status        = UNKNOWN_STATE,
    .carpet_state                = CARPET_TOUGH_GROUND,
    .carpet_state_last           = CARPET_TOUGH_GROUND,
    .ctrl_info_init_success_flag = 0,
};

/************************** input end ********************************************************/

/************************** output ************************************************************/
/************************** output end ********************************************************/
#define SIDE_BRUSH_MOTOR_DEFAULT_VALUE 3                                /* side brush default value */
#define ROLL_BRUSH_MOTOR_DEFAULT_VALUE 9                                /* roll brush default value */
#define ROLL_TUBE_MOTOR_DEFAULT_VALUE  3                                /* roll tube default value */
#define FAN_DEFAULT_VALUE              6                                /* fan default value */
#define MOTOR_CTRL_THREAD_YIELD_TIME   10                               /* 组件控制线程时间 */
#define CARPET_DET_TIME                50                               /* 地毯检测时间 */
#define CARPET_PREVENT_SHARK           4                                /* 地毯检测传感器消抖次数 */
#define ROLLER_BRUSH_AD_TO_CURRENT_MA  (3.3 / 4096 / 8.5 / 0.15 * 1000) /* 滚刷ad转电流 */

clean_ctrl_component_t *clean_ctrl_component_info = NULL;

char *mcu_dev_name[CLEAN_MODULE_TYPE_MAX] = {"lift_motor",       "side_brush",        "roll_brush", "roll_tube",
                                             "clean_water_pump", "filter_water_pump", "fan"};

typedef struct {
    float   speed;
    bool    overflag;
    bool    ops_again;
    uint8_t ops_again_num;
} motor_ctrl_message;

/* 滑动窗口平均滤波器 */
#define MOTOR_FILTER_COMMON_SIZE 50
//左边刷
uint16_t           side_brush_left_filter_buff[MOTOR_FILTER_COMMON_SIZE];
slidave_filter_obj side_brush_left_slidave_filter = {0};
//右边刷
uint16_t           side_brush_right_filter_buff[MOTOR_FILTER_COMMON_SIZE];
slidave_filter_obj side_brush_right_slidave_filter = {0};
//中扫
uint16_t           roller_bursh_filter_buff[MOTOR_FILTER_COMMON_SIZE];
slidave_filter_obj roller_bursh_slidave_filter = {0};
//滚筒
uint16_t           roller_tube_filter_buff[MOTOR_FILTER_COMMON_SIZE];
slidave_filter_obj roller_tube_slidave_filter = {0};
//风机
uint16_t           fan_filter_buff[MOTOR_FILTER_COMMON_SIZE];
slidave_filter_obj fan_slidave_filter = {0};
//左驱动轮
uint16_t           wheel_motor_left_filter_buff[MOTOR_FILTER_COMMON_SIZE];
slidave_filter_obj wheel_motor_left_slidave_filter = {0};
//右驱动轮
uint16_t           wheel_motor_right_filter_buff[MOTOR_FILTER_COMMON_SIZE];
slidave_filter_obj wheel_motor_right_slidave_filter = {0};
//升降
uint16_t           up_elevator_filter_buff[MOTOR_FILTER_COMMON_SIZE];
slidave_filter_obj up_elevator_slidave_filter = {0};
// 污水泵
uint16_t           sewage_water_pump_filter_buff[MOTOR_FILTER_COMMON_SIZE];
slidave_filter_obj sewage_water_pump_slidave_filter = {0};
// 清水泵
uint16_t           clean_water_pump_buff[MOTOR_FILTER_COMMON_SIZE];
slidave_filter_obj clean_water_pump_slidave_filter = {0};

void clean_motor_run(void *argument);
int  motor_param_init(void);
void carpet_detect(void);
void carpet_test(void);

/* Definitions for microros */
osThreadId_t         motorHandle_t;
const osThreadAttr_t motor_run_attributes = {.name = "clean_motor_run", .stack_size = 512 * 4, .priority = (osPriority_t) osPriorityNormal};
/* Definitions for microros */
osThreadId_t         motor_update_status_Handle_t;
const osThreadAttr_t motor_update_status_attributes = {.name       = "clean_motor_update_status",
                                                       .stack_size = 256 * 4,
                                                       .priority   = (osPriority_t) osPriorityNormal};

/*升降电机控制相关*/
#define ELEVATOR_TIMEOUT 5000

extern struct bus_info bus_gpio_lift_motor_sewage;
extern struct bus_info bus_gpio_lift_motor_down;
extern struct bus_info bus_gpio_lift_motor_up;

BUTTON_ATTR_T        up_detect_attr;
BUTTON_ATTACH_ATTR_T up_detect_attach;

BUTTON_ATTR_T        down_detect_attr;
BUTTON_ATTACH_ATTR_T down_detect_attach;

BUTTON_ATTR_T        sewage_detect_attr;
BUTTON_ATTACH_ATTR_T sewage_detect_attach;

static LIFT_MOTOR_E           lift_motor_state     = UNKNOWN_STATE;
static LIFT_MOTOR_RUN_STATE_E lift_motor_run_state = LIFT_MOTOR_RUN_NORMAL;
osMutexId_t                   mutex_elevator_id;  //升降电机互斥锁

//升降电机互斥锁属性
const osMutexAttr_t mutex_elevator_attr = {
    "mutex_elevator",                       // human readable mutex name
    osMutexRecursive | osMutexPrioInherit,  // attr_bits
    NULL,                                   // memory for control block
    0U                                      // size for control block
};

int lift_motor_in_place_callback(uint8_t index, BUTTON_STATUS_TYPE type) {
    switch (index) {
        case UP_STATE:
            if ((BUTTON_PRESS_DOWN == type) || (BUTTON_LONG_PRESS_START == type)) {
                lift_motor_state = UP_STATE;
            } else if (BUTTON_PRESS_UP == type) {
                lift_motor_state = UNKNOWN_STATE;
            }
            break;
        case DOWN_STATE:
            if ((BUTTON_PRESS_DOWN == type) || (BUTTON_LONG_PRESS_START == type)) {
                lift_motor_state = DOWN_STATE;
            } else if (BUTTON_PRESS_UP == type) {
                lift_motor_state = UNKNOWN_STATE;
            }
            break;
        case SEWAGE_STATE:
            if ((BUTTON_PRESS_DOWN == type) || (BUTTON_LONG_PRESS_START == type)) {
                lift_motor_state = SEWAGE_STATE;
            } else if (BUTTON_PRESS_UP == type) {
                lift_motor_state = UNKNOWN_STATE;
            }
            break;
        default:
            return -1;
    }
    return 0;
}

int fal_motor_elevator_init(void) {
    mutex_elevator_id = osMutexNew(&mutex_elevator_attr);

    memset(&up_detect_attr, 0, sizeof(BUTTON_ATTR_T));
    memset(&up_detect_attach, 0, sizeof(BUTTON_ATTACH_ATTR_T));

    up_detect_attr.gpio_info                        = &bus_gpio_lift_motor_up;
    up_detect_attr.index                            = UP_STATE;
    up_detect_attr.trigger_condition                = GPIO_TRIGGER_HIGH;  //高电平触发
    up_detect_attr.long_press_hold_type             = BUTTON_LONG_PRESS_HOLD_CONTINUE_TRIGGER;
    up_detect_attach.gpio_info                      = &bus_gpio_lift_motor_up;
    up_detect_attach.attach.button_press_down       = 1;
    up_detect_attach.attach.button_press_up         = 1;
    up_detect_attach.attach.button_long_press_start = 1;
    up_detect_attach.button_callback                = lift_motor_in_place_callback;

    //注册光电传感器io
    device_ioctl(button_handle, BUTTON_CMD_SET_ADD_BUTTON, (void *) &up_detect_attr);
    device_ioctl(button_handle, BUTTON_CMD_SET_ADD_ATTACH, (void *) &up_detect_attach);

    memset(&down_detect_attr, 0, sizeof(BUTTON_ATTR_T));
    memset(&down_detect_attach, 0, sizeof(BUTTON_ATTACH_ATTR_T));

    down_detect_attr.gpio_info                        = &bus_gpio_lift_motor_down;
    down_detect_attr.index                            = DOWN_STATE;
    down_detect_attr.trigger_condition                = GPIO_TRIGGER_HIGH;  //高电平触发
    down_detect_attr.long_press_hold_type             = BUTTON_LONG_PRESS_HOLD_CONTINUE_TRIGGER;
    down_detect_attach.gpio_info                      = &bus_gpio_lift_motor_down;
    down_detect_attach.attach.button_press_down       = 1;
    down_detect_attach.attach.button_press_up         = 1;
    down_detect_attach.attach.button_long_press_start = 1;
    down_detect_attach.button_callback                = lift_motor_in_place_callback;

    //注册光电传感器io
    device_ioctl(button_handle, BUTTON_CMD_SET_ADD_BUTTON, (void *) &down_detect_attr);
    device_ioctl(button_handle, BUTTON_CMD_SET_ADD_ATTACH, (void *) &down_detect_attach);

    memset(&sewage_detect_attr, 0, sizeof(BUTTON_ATTR_T));
    memset(&sewage_detect_attach, 0, sizeof(BUTTON_ATTACH_ATTR_T));

    sewage_detect_attr.gpio_info                        = &bus_gpio_lift_motor_sewage;
    sewage_detect_attr.index                            = SEWAGE_STATE;
    sewage_detect_attr.trigger_condition                = GPIO_TRIGGER_HIGH;  //高电平触发
    sewage_detect_attr.long_press_hold_type             = BUTTON_LONG_PRESS_HOLD_CONTINUE_TRIGGER;
    sewage_detect_attach.gpio_info                      = &bus_gpio_lift_motor_sewage;
    sewage_detect_attach.attach.button_press_down       = 1;
    sewage_detect_attach.attach.button_press_up         = 1;
    sewage_detect_attach.attach.button_long_press_start = 1;
    sewage_detect_attach.button_callback                = lift_motor_in_place_callback;

    //注册光电传感器io
    device_ioctl(button_handle, BUTTON_CMD_SET_ADD_BUTTON, (void *) &sewage_detect_attr);
    device_ioctl(button_handle, BUTTON_CMD_SET_ADD_ATTACH, (void *) &sewage_detect_attach);

    return 0;
}

FAL_MODULE_INIT(fal_motor_elevator_init);

uint8_t get_elevator_run_state(void) {
    log_i("clift_motor_run_state :%d", lift_motor_run_state);
    return lift_motor_run_state;
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), get_elevator_run_state, get_elevator_run_state,
                 get_elevator_run_state);

uint8_t get_elevator_state(void) {
    log_i("lift_motor_state :%d", lift_motor_state);
    return lift_motor_state;
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), get_elevator_state, get_elevator_state, get_elevator_state);

uint32_t downdelay = 0;
void     change_down_delay(uint32_t delay) {
    downdelay = delay;
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), change_down_delay, change_down_delay, change_down_delay);

uint32_t up_delay = 0;
void     change_up_delay(uint32_t delay) {
    up_delay = delay;
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), change_up_delay, change_up_delay, change_up_delay);

//拖布不工作
int ctrl_elevator_up(void) {
    uint16_t     count                = ELEVATOR_TIMEOUT;
    int32_t      rpm[2]               = {0};
    LIFT_MOTOR_E first_elevator_state = lift_motor_state;

    if (osMutexAcquire(mutex_elevator_id, 0) != osOK) {
        return -1;
    }
    //已经在中位上 无需转动
    if (lift_motor_state == UP_STATE) {
        osMutexRelease(mutex_elevator_id);
        return 0;
    }

    if (lift_motor_state == DOWN_STATE) {
        /*排污方向*/
        rpm[0] = 1;
        rpm[1] = 0;
    } else {
        /*拖地方向*/
        rpm[0] = 0;
        rpm[1] = 1;
    }

    while (--count) {
        device_ioctl(up_elevator_handle, MOTOR_CMD_SET_RPM, &rpm[0]);
        device_ioctl(down_elevator_handle, MOTOR_CMD_SET_RPM, &rpm[1]);

        if (lift_motor_state == UP_STATE) {
            osDelay(up_delay);
            rpm[0] = 1;
            rpm[1] = 1;
            device_ioctl(down_elevator_handle, MOTOR_CMD_SET_RPM, &rpm[0]);
            device_ioctl(up_elevator_handle, MOTOR_CMD_SET_RPM, &rpm[1]);
            break;
        } else if (first_elevator_state == UNKNOWN_STATE && lift_motor_state == DOWN_STATE) {
            rpm[0] = 1;
            rpm[1] = 1;
            device_ioctl(down_elevator_handle, MOTOR_CMD_SET_RPM, &rpm[0]);
            device_ioctl(up_elevator_handle, MOTOR_CMD_SET_RPM, &rpm[1]);
            break;
        }
        osDelay(1);
    }
    //超时
    rpm[0] = 1;
    rpm[1] = 1;
    device_ioctl(down_elevator_handle, MOTOR_CMD_SET_RPM, &rpm[0]);
    device_ioctl(up_elevator_handle, MOTOR_CMD_SET_RPM, &rpm[1]);
    osMutexRelease(mutex_elevator_id);
    if (!count) {
        lift_motor_run_state = LIFT_MOTOR_RUN_TIMEOUT;
        log_e("control elevator up timeout!");
    } else {
        lift_motor_run_state = LIFT_MOTOR_RUN_NORMAL;
        log_i("control elevator up success!");
    }

    motor_attr[MOTOR_SECURITY_UP_DOWN_PUSH_ROD].state_upload_flag = 1;

    rpm[0] = 0;
    device_ioctl(sewage_valve_handle, MOTOR_CMD_SET_RPM, (void *) &rpm[0]);  //关闭排污阀

    return 0;
}

//此时排污水
int ctrl_elevator_sewage(void) {
    int32_t  rpm   = 0;
    uint16_t count = ELEVATOR_TIMEOUT;

    rpm = 1;
    device_ioctl(sewage_valve_handle, MOTOR_CMD_SET_RPM, (void *) &rpm);  //打开排污阀

    //互斥锁
    if (osMutexAcquire(mutex_elevator_id, 0) != osOK) {
        return -1;
    }

    if (lift_motor_state == SEWAGE_STATE) {
        osMutexRelease(mutex_elevator_id);
        return 0;
    }
    while (--count) {
        rpm = 0;
        device_ioctl(down_elevator_handle, MOTOR_CMD_SET_RPM, &rpm);
        rpm = 1;
        device_ioctl(up_elevator_handle, MOTOR_CMD_SET_RPM, &rpm);
        osDelay(1);
        if (lift_motor_state == SEWAGE_STATE) {
            rpm = 1;
            device_ioctl(up_elevator_handle, MOTOR_CMD_SET_RPM, &rpm);
            device_ioctl(down_elevator_handle, MOTOR_CMD_SET_RPM, &rpm);
            break;
        }
    }
    rpm = 1;
    device_ioctl(down_elevator_handle, MOTOR_CMD_SET_RPM, &rpm);
    device_ioctl(up_elevator_handle, MOTOR_CMD_SET_RPM, &rpm);
    osMutexRelease(mutex_elevator_id);
    if (!count) {
        lift_motor_run_state = LIFT_MOTOR_RUN_TIMEOUT;
        log_e("control elevator sewage timeout!");
    } else {
        lift_motor_run_state = LIFT_MOTOR_RUN_NORMAL;
        log_i("control elevator sewage success!");
    }

    motor_attr[MOTOR_SECURITY_UP_DOWN_PUSH_ROD].state_upload_flag = 1;

    return 0;
}

//此时进行拖地工作
int ctrl_elevator_down(void) {
    int32_t  rpm   = 0;
    uint16_t count = ELEVATOR_TIMEOUT;
    //互斥锁
    if (osMutexAcquire(mutex_elevator_id, 0) != osOK) {
        return -1;
    }
    //已经在上位了
    if (lift_motor_state == DOWN_STATE) {
        osMutexRelease(mutex_elevator_id);
        return 0;
    }

    while (--count) {
        rpm = 1;
        device_ioctl(down_elevator_handle, MOTOR_CMD_SET_RPM, &rpm);
        rpm = 0;
        device_ioctl(up_elevator_handle, MOTOR_CMD_SET_RPM, &rpm);
        osDelay(1);
        if (lift_motor_state == DOWN_STATE) {
            osDelay(downdelay);
            rpm = 1;
            device_ioctl(up_elevator_handle, MOTOR_CMD_SET_RPM, &rpm);
            device_ioctl(down_elevator_handle, MOTOR_CMD_SET_RPM, &rpm);
            break;
        }
    }
    rpm = 1;
    device_ioctl(down_elevator_handle, MOTOR_CMD_SET_RPM, &rpm);
    device_ioctl(up_elevator_handle, MOTOR_CMD_SET_RPM, &rpm);
    osMutexRelease(mutex_elevator_id);
    if (!count) {
        lift_motor_run_state = LIFT_MOTOR_RUN_TIMEOUT;
        log_e("control elevator down timeout!");
    } else {
        lift_motor_run_state = LIFT_MOTOR_RUN_NORMAL;
        log_i("control elevator down success!");
    }

    motor_attr[MOTOR_SECURITY_UP_DOWN_PUSH_ROD].state_upload_flag = 1;

    rpm = 0;
    device_ioctl(sewage_valve_handle, MOTOR_CMD_SET_RPM, (void *) &rpm);  //关闭排污阀

    return 0;
}

int ruller_tube_run(int32_t gear, uint32_t degree) {
    device_ioctl(clean_info.clean_module_ctrl[ROLLER_TUBE].motor_handle, MOTOR_CMD_SET_RPM, &gear);
    osDelay(degree);
    gear = 0;
    device_ioctl(clean_info.clean_module_ctrl[ROLLER_TUBE].motor_handle, MOTOR_CMD_SET_RPM, &gear);
    return 0;
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), ruller_tube_run, ruller_tube_run, ruller_tube_run);

void motor_current_info_clear(MOTOR_CURRENT_INFO *motor_current_info) {
    if (motor_current_info->current_value != NULL) {
        *motor_current_info->current_value = 0;
    }

    if (motor_current_info->filtered_current_value != NULL) {
        *motor_current_info->filtered_current_value = 0.0;
    }
    motor_current_info->motor_num = 0;
    motor_current_info->open_flag = 0;
}

void print_all_motor_current() {
    MOTOR_CURRENT_INFO motor_common_current_info          = {0};
    float              motor_common_current_filterd_value = 0.0;
    motor_common_current_info.filtered_current_value      = &motor_common_current_filterd_value;
    // 左边刷
    motor_current_info_clear(&motor_common_current_info);
    motor_common_current_info.motor_num = 0;
    device_ioctl(side_brush_handle, MOTOR_CMD_GET_CURRENT_FILTERED_VALUE, &motor_common_current_info);
    log_i("left side_brush:%f", motor_common_current_filterd_value);
    // 右边刷
    motor_current_info_clear(&motor_common_current_info);
    motor_common_current_info.motor_num = 1;
    device_ioctl(side_brush_handle, MOTOR_CMD_GET_CURRENT_FILTERED_VALUE, &motor_common_current_info);
    log_i("right side_brush:%f", motor_common_current_filterd_value);
    // 清水泵
    motor_current_info_clear(&motor_common_current_info);
    device_ioctl(clean_water_pump_handle, MOTOR_CMD_GET_CURRENT_FILTERED_VALUE, &motor_common_current_info);
    log_i("clean_water_pump:%f", motor_common_current_filterd_value);
    // 滚桶
    motor_current_info_clear(&motor_common_current_info);
    device_ioctl(roller_tube_handle, MOTOR_CMD_GET_CURRENT_FILTERED_VALUE, &motor_common_current_info);
    log_i("roller_tube:%f", motor_common_current_filterd_value);
    // 污水泵
    motor_current_info_clear(&motor_common_current_info);
    device_ioctl(sewage_water_pump_handle, MOTOR_CMD_GET_CURRENT_FILTERED_VALUE, &motor_common_current_info);
    log_i("sewage_water_pump:%f", motor_common_current_filterd_value);
    // 中扫
    motor_current_info_clear(&motor_common_current_info);
    device_ioctl(roller_brush_handle, MOTOR_CMD_GET_CURRENT_FILTERED_VALUE, &motor_common_current_info);
    log_i("roller_brush:%f,%f,", motor_common_current_filterd_value, motor_common_current_filterd_value * 3.3 / 4096 / 8.5 / 0.15);
    // 升降
    motor_current_info_clear(&motor_common_current_info);
    device_ioctl(up_elevator_handle, MOTOR_CMD_GET_CURRENT_FILTERED_VALUE, &motor_common_current_info);
    log_i("up_elevator:%f", motor_common_current_filterd_value);
    // 风机
    motor_current_info_clear(&motor_common_current_info);
    device_ioctl(fan_motor_handle, MOTOR_CMD_GET_CURRENT_FILTERED_VALUE, &motor_common_current_info);
    log_i("fan:%f", motor_common_current_filterd_value);
    // 左驱动轮
    motor_current_info_clear(&motor_common_current_info);
    motor_common_current_info.motor_num = 0;
    device_ioctl(wheel_handle, MOTOR_CMD_GET_CURRENT_FILTERED_VALUE, &motor_common_current_info);
    log_i("left_wheel:%f", motor_common_current_filterd_value);
    // 右驱动轮
    motor_current_info_clear(&motor_common_current_info);
    motor_common_current_info.motor_num = 1;
    device_ioctl(wheel_handle, MOTOR_CMD_GET_CURRENT_FILTERED_VALUE, &motor_common_current_info);
    log_i("rgiht_wheel:%f", motor_common_current_filterd_value);
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), print_all_motor_current, print_all_motor_current,
                 print_all_motor_current);

extern void pub_light_det_value(uint16_t val);

void clean_update_status_run(void *argument) {
    uint8_t  carpet_state               = CARPET_SENSOR_NORMAL;
    uint8_t  clean_water_state          = CLEAN_WATER_SENSOR_NORMAL;
    uint32_t carpet_ts                  = 0;
    uint32_t clean_water_ts             = 0;
    uint8_t  exception_flag             = 0;
    uint8_t  clean_water_exception_flag = 0;

    uint32_t motor_continuous_adc_value           = 0;
    uint8_t  motor_continuous_adc_read_times      = 20;
    uint16_t motor_continuous_avg_adc_value       = 0;
    uint8_t  halt_motor_continuous_adc_read_times = motor_continuous_adc_read_times / 2;

    MOTOR_CURRENT_INFO motor_common_current_info          = {0};
    uint16_t           motor_common_current_adc_value     = 0;
    float              motor_common_current_filterd_value = 0.0;

    motor_common_current_info.current_value          = &motor_common_current_adc_value;
    motor_common_current_info.filtered_current_value = &motor_common_current_filterd_value;

    uint32_t version_count = 0;  //用于读取版本
    uint32_t get_adc       = 0;
    while (1) {
        //边刷
        device_ioctl(switch_oc1_handle, 0, NULL);
        device_ioctl(switch_oc2_handle, 0, NULL);
        osDelay(2);
        // 左边刷获取电流adc
        motor_current_info_clear(&motor_common_current_info);
        motor_common_current_info.motor_num = 0;
        motor_continuous_adc_value          = 0;
        for (int i = 0; i < motor_continuous_adc_read_times;) {
            if (device_ioctl(side_brush_handle, MOTOR_CMD_GET_CURRENT_VALUE, &motor_common_current_info) == 0) {
                motor_continuous_adc_value += motor_common_current_adc_value;
                i++;
                if (i % halt_motor_continuous_adc_read_times == 0) {
                    osDelay(1);
                }

            } else {
                osDelay(1);
            }
        }
        motor_continuous_avg_adc_value     = motor_continuous_adc_value / motor_continuous_adc_read_times;
        motor_common_current_filterd_value = sliding_average_filter(&side_brush_left_slidave_filter, motor_continuous_avg_adc_value);
        device_ioctl(side_brush_handle, MOTOR_CMD_SET_CURRENT_FILTERED_VALUE, &motor_common_current_info);

        // 右边刷获取电流adc
        motor_current_info_clear(&motor_common_current_info);
        motor_common_current_info.motor_num = 1;
        motor_continuous_adc_value          = 0;
        for (int i = 0; i < motor_continuous_adc_read_times;) {
            if (device_ioctl(side_brush_handle, MOTOR_CMD_GET_CURRENT_VALUE, &motor_common_current_info) == 0) {
                motor_continuous_adc_value += motor_common_current_adc_value;
                i++;
                if (i % halt_motor_continuous_adc_read_times == 0) {
                    osDelay(1);
                }

            } else {
                osDelay(1);
            }
        }
        motor_continuous_avg_adc_value     = motor_continuous_adc_value / motor_continuous_adc_read_times;
        motor_common_current_filterd_value = sliding_average_filter(&side_brush_right_slidave_filter, motor_continuous_avg_adc_value);
        device_ioctl(side_brush_handle, MOTOR_CMD_SET_CURRENT_FILTERED_VALUE, &motor_common_current_info);

        //清水泵 & 滚桶
        device_ioctl(switch_oc1_handle, 1, NULL);
        device_ioctl(switch_oc2_handle, 1, NULL);
        osDelay(2);
        // 清水泵获取电流adc
        motor_current_info_clear(&motor_common_current_info);
        motor_continuous_adc_value = 0;
        for (int i = 0; i < motor_continuous_adc_read_times;) {
            if (device_ioctl(clean_water_pump_handle, MOTOR_CMD_GET_CURRENT_VALUE, &motor_common_current_info) == 0) {
                motor_continuous_adc_value += motor_common_current_adc_value;
                i++;
                if (i % halt_motor_continuous_adc_read_times == 0) {
                    osDelay(1);
                }

            } else {
                osDelay(1);
            }
        }
        motor_continuous_avg_adc_value     = motor_continuous_adc_value / motor_continuous_adc_read_times;
        motor_common_current_filterd_value = sliding_average_filter(&clean_water_pump_slidave_filter, motor_continuous_avg_adc_value);
        device_ioctl(clean_water_pump_handle, MOTOR_CMD_SET_CURRENT_FILTERED_VALUE, &motor_common_current_info);
        // 滚桶获取电流adc
        motor_current_info_clear(&motor_common_current_info);
        motor_continuous_adc_value = 0;
        for (int i = 0; i < motor_continuous_adc_read_times;) {
            if (device_ioctl(roller_tube_handle, MOTOR_CMD_GET_CURRENT_VALUE, &motor_common_current_info) == 0) {
                motor_continuous_adc_value += motor_common_current_adc_value;
                i++;
                if (i % halt_motor_continuous_adc_read_times == 0) {
                    osDelay(1);
                }

            } else {
                osDelay(1);
            }
        }
        motor_continuous_avg_adc_value     = motor_continuous_adc_value / motor_continuous_adc_read_times;
        motor_common_current_filterd_value = sliding_average_filter(&roller_tube_slidave_filter, motor_continuous_avg_adc_value);
        device_ioctl(roller_tube_handle, MOTOR_CMD_SET_CURRENT_FILTERED_VALUE, &motor_common_current_info);

        //污水泵 & 中扫
        device_ioctl(switch_oc2_handle, 2, NULL);
        device_ioctl(switch_oc1_handle, 2, NULL);
        osDelay(2);
        // 污水泵获取电流adc
        motor_current_info_clear(&motor_common_current_info);
        motor_continuous_adc_value = 0;
        for (int i = 0; i < motor_continuous_adc_read_times;) {
            if (device_ioctl(sewage_water_pump_handle, MOTOR_CMD_GET_CURRENT_VALUE, &motor_common_current_info) == 0) {
                motor_continuous_adc_value += motor_common_current_adc_value;
                i++;
            } else {
                osDelay(1);
            }
        }
        motor_continuous_avg_adc_value     = motor_continuous_adc_value / motor_continuous_adc_read_times;
        motor_common_current_filterd_value = sliding_average_filter(&sewage_water_pump_slidave_filter, motor_continuous_avg_adc_value);
        device_ioctl(sewage_water_pump_handle, MOTOR_CMD_SET_CURRENT_FILTERED_VALUE, &motor_common_current_info);

        // 中扫获取电流adc
        motor_current_info_clear(&motor_common_current_info);
        motor_continuous_adc_value = 0;
        for (int i = 0; i < motor_continuous_adc_read_times;) {
            if (device_ioctl(roller_brush_handle, MOTOR_CMD_GET_CURRENT_VALUE, &motor_common_current_info) == 0) {
                motor_continuous_adc_value += motor_common_current_adc_value;
                i++;
                if (i % halt_motor_continuous_adc_read_times == 0) {
                    osDelay(1);
                }

            } else {
                osDelay(1);
            }
        }
        motor_continuous_avg_adc_value     = motor_continuous_adc_value / motor_continuous_adc_read_times;
        motor_common_current_filterd_value = sliding_average_filter(&roller_bursh_slidave_filter, motor_continuous_avg_adc_value);
        device_ioctl(roller_brush_handle, MOTOR_CMD_SET_CURRENT_FILTERED_VALUE, &motor_common_current_info);

        //升降
        device_ioctl(switch_oc1_handle, 3, NULL);
        osDelay(2);
        // 升降获取电流adc
        motor_current_info_clear(&motor_common_current_info);
        motor_continuous_adc_value = 0;
        for (int i = 0; i < motor_continuous_adc_read_times;) {
            if (device_ioctl(up_elevator_handle, MOTOR_CMD_GET_CURRENT_VALUE, &motor_common_current_info) == 0) {
                motor_continuous_adc_value += motor_common_current_adc_value;
                i++;
                if (i % halt_motor_continuous_adc_read_times == 0) {
                    osDelay(1);
                }

            } else {
                osDelay(1);
            }
        }
        motor_continuous_avg_adc_value     = motor_continuous_adc_value / motor_continuous_adc_read_times;
        motor_common_current_filterd_value = sliding_average_filter(&up_elevator_slidave_filter, motor_continuous_avg_adc_value);
        device_ioctl(up_elevator_handle, MOTOR_CMD_SET_CURRENT_FILTERED_VALUE, &motor_common_current_info);

        // 风机获取电流adc
        motor_current_info_clear(&motor_common_current_info);
        motor_continuous_adc_value = 0;
        for (int i = 0; i < motor_continuous_adc_read_times;) {
            if (device_ioctl(fan_motor_handle, MOTOR_CMD_GET_CURRENT_VALUE, &motor_common_current_info) == 0) {
                motor_continuous_adc_value += motor_common_current_adc_value;
                i++;
                if (i % halt_motor_continuous_adc_read_times == 0) {
                    osDelay(1);
                }

            } else {
                osDelay(1);
            }
        }
        motor_continuous_avg_adc_value     = motor_continuous_adc_value / motor_continuous_adc_read_times;
        motor_common_current_filterd_value = sliding_average_filter(&fan_slidave_filter, motor_continuous_avg_adc_value);
        device_ioctl(fan_motor_handle, MOTOR_CMD_SET_CURRENT_FILTERED_VALUE, &motor_common_current_info);

        //左驱动轮 获取电流adc
        motor_current_info_clear(&motor_common_current_info);
        motor_common_current_info.motor_num = 0;
        motor_continuous_adc_value          = 0;
        for (int i = 0; i < motor_continuous_adc_read_times;) {
            if (device_ioctl(wheel_handle, MOTOR_CMD_GET_CURRENT_VALUE, &motor_common_current_info) == 0) {
                motor_continuous_adc_value += motor_common_current_adc_value;
                i++;
                if (i % halt_motor_continuous_adc_read_times == 0) {
                    osDelay(1);
                }

            } else {
                osDelay(1);
            }
        }
        motor_continuous_avg_adc_value     = motor_continuous_adc_value / motor_continuous_adc_read_times;
        motor_common_current_filterd_value = sliding_average_filter(&wheel_motor_left_slidave_filter, motor_continuous_avg_adc_value);
        device_ioctl(wheel_handle, MOTOR_CMD_SET_CURRENT_FILTERED_VALUE, &motor_common_current_info);

        // 右驱动轮 获取电流adc
        motor_current_info_clear(&motor_common_current_info);
        motor_common_current_info.motor_num = 1;
        motor_continuous_adc_value          = 0;
        for (int i = 0; i < motor_continuous_adc_read_times;) {
            if (device_ioctl(wheel_handle, MOTOR_CMD_GET_CURRENT_VALUE, &motor_common_current_info) == 0) {
                motor_continuous_adc_value += motor_common_current_adc_value;
                i++;
                if (i % halt_motor_continuous_adc_read_times == 0) {
                    osDelay(1);
                }

            } else {
                osDelay(1);
            }
        }
        motor_continuous_avg_adc_value     = motor_continuous_adc_value / motor_continuous_adc_read_times;
        motor_common_current_filterd_value = sliding_average_filter(&wheel_motor_right_slidave_filter, motor_continuous_avg_adc_value);
        device_ioctl(wheel_handle, MOTOR_CMD_SET_CURRENT_FILTERED_VALUE, &motor_common_current_info);

        //水位传感器异常检测
        if (is_timeout(clean_water_ts, 1000) && osKernelGetTickCount() >= SENSOR_START_EXCEPTION_TIME) {
            device_ioctl(clean_water_handle, WATER_POSITION_IOCTL_GET_SENSOR_STATE, &clean_water_state);
            if (CLEAN_WATER_SENSOR_NORMAL != clean_water_state) {
                if (0 == clean_water_exception_flag) {  //异常发生首次要pub log
                    if (CLEAN_WATER_SENSOR_DATA_REC_TIMEOUT_EXCEPTION == clean_water_state) {
                        if (pal_log_pub("error", "hardware/clean_water_sensor", "传感器异常：清水水位传感器故障")) {
                            clean_water_exception_flag = 1;
                        }
                    } else if (CLEAN_WATER_SENSOR_DATA_NOT_NORMAL_EXCEPTION == clean_water_state) {
                        if (pal_log_pub("error", "hardware/clean_water_sensor", "传感器异常：水位读取异常")) {
                            clean_water_exception_flag = 1;
                        }
                    }
                }
            } else {
                exception_flag = 0;
            }
            clean_water_ts = osKernelGetTickCount();  //每秒检测一次
        }

        //版本检测adc读取 oc2放在其他线程切换会有异常 所以在这里处理
        //板卡调试打印，正式版本屏蔽
        /*
        version_count++;
        if (version_count > 50) {
            device_ioctl(switch_oc2_handle, 3, NULL);
            osDelay(2);
            device_ioctl(adc_version_handle, 0, &get_adc);
            log_i(">>>>>>>>>>>>>>>>>version_adc : %d", get_adc);
            int a, b;
            device_read(gpio_version1_handle, &a, 4);
            device_read(gpio_version2_handle, &b, 4);
            log_i("gpio_version :%d  %d", a, b);
            version_count = 0;
        }
        */
        static unsigned int light_det_ts  = 0;
        unsigned int        light_det_adc = 0;

        if (is_timeout(light_det_ts, 1000)) {
            light_det_ts = osKernelGetTickCount();
            device_ioctl(adc_light_det_handle, CMD_READ_RAW_ADC_VALUE, &light_det_adc);
            pub_light_det_value(light_det_adc);

            log_i("light det adc[%d]", light_det_adc);
        }

        osDelay(1);
    }
}

//边刷参数
MOTOR_PARAM bldcm_side_brush_param = {
    .motor_type        = MOTOR_TYPE_BLDCM,  //电机类型
    .motor_rpm_max     = 100,               //电机最大转速
    .motor_current_max = 15000,             //  其实是ad值，若要转换成电流需要 ad *3.3/4096/0.3
};

MOTOR_PARAM bldcm_roller_tube_param = {
    .motor_type        = MOTOR_TYPE_BLDCM,  //电机类型
    .motor_rpm_max     = 100,               //电机最大转速
    .motor_current_max = 15000,             //  其实是ad值，若要转换成电流需要 ad *3.3/4096/0.3
};

MOTOR_PARAM bldcm_roller_brush_param = {
    .motor_type        = MOTOR_TYPE_BLDCM,  //电机类型
    .motor_rpm_max     = 100,               //电机最大转速
    .motor_current_max = 5000,              //  其实是ad值，若要转换成电流需要 ad *3.3/4096/0.3
};

void motor_init(void) {
    //边刷配置
    device_ioctl(side_brush_handle, MOTOR_CMD_SET_PARAM, (void *) &bldcm_side_brush_param);
    //滚筒配置
    device_ioctl(roller_tube_handle, MOTOR_CMD_SET_PARAM, (void *) &bldcm_roller_tube_param);
    //滚筒配置
    device_ioctl(roller_brush_handle, MOTOR_CMD_SET_PARAM, (void *) &bldcm_roller_brush_param);
}

void moter_filter_init() {
    // 左边刷
    sliding_average_filter_init(&side_brush_left_slidave_filter, side_brush_left_filter_buff, MOTOR_FILTER_COMMON_SIZE);
    // 右边刷
    sliding_average_filter_init(&side_brush_right_slidave_filter, side_brush_right_filter_buff, MOTOR_FILTER_COMMON_SIZE);
    // 中扫
    sliding_average_filter_init(&roller_bursh_slidave_filter, roller_bursh_filter_buff, MOTOR_FILTER_COMMON_SIZE);
    //滚桶
    sliding_average_filter_init(&roller_tube_slidave_filter, roller_tube_filter_buff, MOTOR_FILTER_COMMON_SIZE);
    //风机
    sliding_average_filter_init(&fan_slidave_filter, fan_filter_buff, MOTOR_FILTER_COMMON_SIZE);
    //左驱动轮
    sliding_average_filter_init(&wheel_motor_left_slidave_filter, wheel_motor_left_filter_buff, MOTOR_FILTER_COMMON_SIZE);
    //右驱动轮
    sliding_average_filter_init(&wheel_motor_right_slidave_filter, wheel_motor_right_filter_buff, MOTOR_FILTER_COMMON_SIZE);
    //升降杆
    sliding_average_filter_init(&up_elevator_slidave_filter, up_elevator_filter_buff, MOTOR_FILTER_COMMON_SIZE);
    // 污水泵
    sliding_average_filter_init(&sewage_water_pump_slidave_filter, sewage_water_pump_filter_buff, MOTOR_FILTER_COMMON_SIZE);
    // 清水泵
    sliding_average_filter_init(&clean_water_pump_slidave_filter, clean_water_pump_buff, MOTOR_FILTER_COMMON_SIZE);
}

int fal_motor_init(void) {
    /* init */
    motor_init();
    /* filter init */
    moter_filter_init();
    /* fal motor thread create */
    motorHandle_t = osThreadNew(clean_motor_run, NULL, &motor_run_attributes);
    /* fal motor update status thread create */
    motor_update_status_Handle_t = osThreadNew(clean_update_status_run, NULL, &motor_update_status_attributes);
    return 0;
}
FAL_MODULE_INIT(fal_motor_init);

void roller_tube_ctrl1(void) {
    switch (clean_info.clean_module_ctrl[ROLLER_TUBE].attr) {
        case CTRL_RULLER_TUBE_ANGLE_72:
            ruller_tube_run(ruller_tube_rpm, clean_info.ruller_tube_72_tim);
            clean_info.clean_module_ctrl[ROLLER_TUBE].attr = CTRL_RULLER_TUBE_NULL;
            break;
        case CTRL_RULLER_TUBE_RUN:
            device_ioctl(clean_info.clean_module_ctrl[ROLLER_TUBE].motor_handle, MOTOR_CMD_SET_RPM,
                         &clean_info.clean_module_ctrl[ROLLER_TUBE].rpm);
            break;
        default:
            break;
    }
}

void elevator_ctrl(void) {
    LIFT_MOTOR_E lift_ctrl_state = clean_info.clean_module_ctrl[UP_DOWM_PUSH_ROD].attr;
    if (clean_info.last_elevator_status != lift_ctrl_state) {
        log_i("elevator control = %d", lift_ctrl_state);
    }
    switch (lift_ctrl_state) {
        case DOWN_STATE:  //拖地
            ctrl_elevator_down();
            break;

        case UP_STATE:
            ctrl_elevator_up();
            break;

        case SEWAGE_STATE:  //排污
            ctrl_elevator_sewage();
            break;

        default:
            break;
    }
    clean_info.last_elevator_status = lift_ctrl_state;
}

/* 返回恒功率转速 */
static int32_t roller_brush_constant_power_rpm() {
    static int32_t  roller_brush_rpm_ori            = 0;
    static int32_t  roller_brush_rpm_limit          = 0;
    static int32_t  last_roller_brush_rpm_limit     = 0;
    static uint32_t roller_bursh_over_power_count   = 0;
    static uint32_t roller_bursh_normal_power_count = 0;

    BMS_DATA           battery                     = {0};
    MOTOR_CURRENT_INFO motor_current_info          = {0};
    float              motor_current_filterd_value = 0.0;
    motor_current_info.filtered_current_value      = &motor_current_filterd_value;
    float current_ma                               = 0.0f;  // 实际电流，单位毫安

    // 转速的值被修改，重新初始化
    if (roller_brush_rpm_ori != clean_info.clean_module_rpm[ROLLER_BRUSH] ||
        roller_brush_rpm_limit != clean_info.clean_module_ctrl[ROLLER_BRUSH].rpm) {
        roller_brush_rpm_ori            = clean_info.clean_module_rpm[ROLLER_BRUSH];
        roller_brush_rpm_limit          = clean_info.clean_module_rpm[ROLLER_BRUSH];
        roller_bursh_over_power_count   = 0;
        roller_bursh_normal_power_count = 0;
        log_d("limit_roller_brush_rpm init:%d", roller_brush_rpm_limit);
        return roller_brush_rpm_limit;
    }

    // 原挡位小于可控最低挡位则不跑降档逻辑
    if (roller_brush_rpm_ori <= ROLLER_BRUSH_LIMIT_LOWER_RPM) {
        return roller_brush_rpm_ori;
    }

    // 计算电流
    device_ioctl(roller_brush_handle, MOTOR_CMD_GET_CURRENT_FILTERED_VALUE, &motor_current_info);
    current_ma = motor_current_filterd_value * ROLLER_BRUSH_AD_TO_CURRENT_MA;
    if (current_ma >= motor_attr[MOTOR_SECURITY_ROLLER_BRUSH].motor_table.small_abnormal_current) {
        // 每2秒检测
        if (++roller_bursh_over_power_count >= 2000 / MOTOR_CTRL_THREAD_YIELD_TIME) {
            // 策略：1. 超过过流电流2倍，降档至30%. 2.超过过流电流1.5倍，降档至50%. 3.超过过流电流但不满足以上条件，逐一降档
            if (current_ma >= motor_attr[MOTOR_SECURITY_ROLLER_BRUSH].motor_table.small_abnormal_current * 2) {
                roller_brush_rpm_limit *= 0.3;
            } else if (current_ma >= motor_attr[MOTOR_SECURITY_ROLLER_BRUSH].motor_table.small_abnormal_current * 1.5) {
                roller_brush_rpm_limit *= 0.5;
            } else {
                roller_brush_rpm_limit -= 10;
            }
            roller_bursh_over_power_count = 0;

            if (roller_brush_rpm_limit < ROLLER_BRUSH_LIMIT_LOWER_RPM) {
                roller_brush_rpm_limit = ROLLER_BRUSH_LIMIT_LOWER_RPM;
            }

            if (last_roller_brush_rpm_limit != roller_brush_rpm_limit && roller_brush_rpm_limit == ROLLER_BRUSH_LIMIT_LOWER_RPM) {
                log_i("roller brush gear low to %d reset det time", ROLLER_BRUSH_LIMIT_LOWER_RPM);
                reset_motor_det_ts(MOTOR_SECURITY_ROLLER_BRUSH);
                clear_current_det_cnt_state(MOTOR_SECURITY_ROLLER_BRUSH, 0);
            }

            log_e("roller brush is over current:%.2f. reduce rpm:%d", motor_current_filterd_value, roller_brush_rpm_limit);
        }
    } else if (motor_current_filterd_value <= motor_attr[MOTOR_SECURITY_ROLLER_BRUSH].motor_table.small_abnormal_current * 0.85) {
        // 功率小于limit的85%保持2s则逐渐恢复转速
        if (++roller_bursh_normal_power_count >= 2000 / MOTOR_CTRL_THREAD_YIELD_TIME) {
            if (roller_brush_rpm_limit < roller_brush_rpm_ori) {
                roller_brush_rpm_limit += 10;
                log_e("roller brush revert to normal rpm:%d", roller_brush_rpm_limit);
            }
            roller_bursh_normal_power_count = 0;
        }
        roller_bursh_over_power_count = 0;
    }
    last_roller_brush_rpm_limit = roller_brush_rpm_limit;

    return roller_brush_rpm_limit;
}

/*********控制某个清洁组件********/
int sigle_module_ctrl(uint8_t index) {
    int32_t side_rush_0rpm = 0;
    int32_t motorNum       = 0;

    if (index >= CLEAN_MODULE_TYPE_MAX) {
        log_e("module number greater than CLEAN_MODULE_TYPE_MAX");
        return -1;
    }
    switch (index) {
        case ROLLER_TUBE:  //目前滚筒和升降电机属于特殊控制，需要通过attr判断控制属性，后续若有其他组建需要特殊控制则需加一个case
            roller_tube_ctrl1();
            break;

        case UP_DOWM_PUSH_ROD:
            elevator_ctrl();
            break;

        case FAN_MOTOR:
            // 改到驱动层进行控电
            /*
            if (clean_info.clean_module_ctrl[index].rpm) {
                device_ioctl(fan_motor_handle, MOTOR_CMD_START, &motorNum);
                osDelay(50);
            }
            */
            device_ioctl(fan_motor_handle, MOTOR_CMD_SET_RPM, (void *) &clean_info.clean_module_ctrl[index].rpm);
            /*
            if (!clean_info.clean_module_ctrl[index].rpm) {
                osDelay(50);
                device_ioctl(fan_motor_handle, MOTOR_CMD_STOP, &motorNum);
            }
            */
            break;

        default:
            device_ioctl(clean_info.clean_module_ctrl[index].motor_handle, MOTOR_CMD_SET_RPM, &clean_info.clean_module_ctrl[index].rpm);
            log_d("index = %d, rpm = %d----", index, clean_info.clean_module_ctrl[index].rpm);
            break;
    }
    return 0;
}

void clean_module_ctrl(void) {
    //循环控制所有电机
    for (uint8_t i = 0; i < CLEAN_MODULE_TYPE_MAX; i++) {
        if (!clean_info.clean_module_ctrl[i].rpm && clean_info.clean_module_ctrl[i].is_control) {
            continue;
        }

        //中扫电机恒功率需要实时更新
        if (i == ROLLER_BRUSH) {
            clean_info.clean_module_ctrl[i].rpm = roller_brush_constant_power_rpm();
        }

        sigle_module_ctrl(i);
        clean_info.clean_module_ctrl[i].is_control = true;
    }
}

void carpet_detect(void) {
    static uint8_t carpet_type = CARPET_TOUGH_GROUND;
    static uint8_t carpet_someone;
    static uint8_t soft_ground_count                          = {0};
    static uint8_t touch_ground_count                         = 0;
    static uint8_t carpet_state_cnt[CARPET_SENSOR_ID_MAX]     = {0};  // 检测到地毯计数
    static uint8_t not_carpet_state_cnt[CARPET_SENSOR_ID_MAX] = {0};  // 未检测到地毯计数
    uint8_t        carpet_state[CARPET_SENSOR_ID_MAX]         = {0};

    get_carpet_state(carpet_state);

    for (uint8_t i = 0; i < CARPET_SENSOR_ID_MAX; i++) {
        if (carpet_state[i]) {
            carpet_state_cnt[i]++;
            not_carpet_state_cnt[i] = 0;
        } else {
            not_carpet_state_cnt[i]++;
            carpet_state_cnt[i] = 0;
        }

        if (carpet_state_cnt[i] >= CARPET_PREVENT_SHARK) {
            carpet_state_cnt[i] = CARPET_PREVENT_SHARK;
            if (clean_info.carpet_sensor_state[i] == 0) {
                clean_info.carpet_sensor_state[i] = 1;
                log_i("carpet_sensor:%d det soft groud!", i + 1);
            }
        } else if (not_carpet_state_cnt[i] >= CARPET_PREVENT_SHARK) {
            not_carpet_state_cnt[i] = CARPET_PREVENT_SHARK;
            if (clean_info.carpet_sensor_state[i] == 1) {
                clean_info.carpet_sensor_state[i] = 0;
                log_i("carpet_sensor:%d det hard groud!", i + 1);
            }
        }
    }

    return;
}

void carpet_test(void) {
    ctrl_elevator_down();
    while (1) {
        carpet_detect();
        if (clean_info.carpet_state_last != clean_info.carpet_state) {
            if (CARPET_TOUGH_GROUND != clean_info.carpet_state) {  //若检测到地毯则关闭水路，增大吸尘力度
                log_i("happen carpet ground, add fan motor rpm, up elevator motor.");
                if ((clean_info.carpet_state == CARPET_SOFT_GROUND2) || (clean_info.carpet_state == CARPET_SOFT_GROUND3)) {
                    if (UP_STATE != get_elevator_state()) {
                        security_stop_ms(1000);
                        log_i("stop!!!!!!!!!!!!!!!!!!!");
                    }
                }
                ctrl_elevator_up();
                clean_info.carpet_state_last = clean_info.carpet_state;

            } else if (CARPET_TOUGH_GROUND == clean_info.carpet_state) {  //检测为硬地面,则恢复风机吸力
                ctrl_elevator_down();
                clean_info.carpet_state_last = clean_info.carpet_state;
                log_i("release carpet ground, add fan motor rpm, up elevator motor.");
            }
        }
        osDelay(50);
    }
}

void motor_param_update(clean_ctrl_component_t *comp_info) {
    /*update motor parameter */
    clean_ctrl_component_info = comp_info;
    if (NULL != clean_ctrl_component_info) {
        clean_ctrl_component_info->component_attr[FAN_MOTOR].config_stoped_tims_ms = 8000;
        clean_info.ctrl_info_init_success_flag                                     = 1;
    }
}

int motor_param_init(void) {
    /* set motor config init */
    memset(clean_info.clean_module_ctrl, 0, sizeof(clean_module_ctrl_t) * CLEAN_MODULE_TYPE_MAX);

    clean_info.clean_module_ctrl[UP_DOWM_PUSH_ROD].motor_handle  = -1;  //推杆电机不支持直接通过device_ioctl控制
    clean_info.clean_module_ctrl[SIDE_BRUSH].motor_handle        = side_brush_handle;
    clean_info.clean_module_ctrl[ROLLER_BRUSH].motor_handle      = roller_brush_handle;
    clean_info.clean_module_ctrl[ROLLER_TUBE].motor_handle       = roller_tube_handle;
    clean_info.clean_module_ctrl[CLEAN_WATER_PUMP].motor_handle  = clean_water_pump_handle;
    clean_info.clean_module_ctrl[SEWAGE_WATER_PUMP].motor_handle = sewage_water_pump_handle;
    clean_info.clean_module_ctrl[FAN_MOTOR].motor_handle         = fan_motor_handle;

    // 滚筒默认处于抬高状态
    clean_info.clean_module_ctrl[UP_DOWM_PUSH_ROD].attr = UP_STATE;

    return 0;
}

void roller_tube_run_72(int tim) {
    clean_info.ruller_tube_72_tim = tim;
    return;
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), roller_tube_run_72, roller_tube_run_72, roller_tube_run_72);

void carpet_update_cb(const void *msgin) {
    for (uint8_t i = 0; i < CARPET_SENSOR_ID_MAX; i++) {
        carpet_public_callback(i, clean_info.carpet_sensor_state[i]);
    }
    return;
}

void clean_motor_run(void *argument) {
    uint32_t carpet_det_ts = 0;
    osDelay(2000);       //开机时等待button线程完全启动之后再恢复升降电机状态
    ctrl_elevator_up();  //开机第一次，找到升降电机位置（在拖地或者抬起位置）
    ctrl_elevator_up();  //开机第二次，恢复到抬起位置
    motor_param_init();
    while (1) {
        for (uint8_t i = 0; i < CLEAN_MODULE_TYPE_MAX; i++) {
            // 是否超时
            if (is_timeout(clean_ctrl_component_info->component_attr[i].last_recv_time, 3000) &&
                !is_floats_equl(clean_ctrl_component_info->component_attr[i].level, 0.0f)) {
                clean_ctrl_component_info->component_attr[i].level          = 0.0f;
                clean_ctrl_component_info->component_attr[i].stoped_time_ms = osKernelGetTickCount();
                log_w("recv task timeout, stop comp:%d", i);
            }

            // 控制强度
            if (!is_floats_equl(clean_ctrl_component_info->last_component_attr[i].level, clean_ctrl_component_info->component_attr[i].level)) {
                clean_info.clean_module_rpm[i] = (int8_t)(100 * clean_ctrl_component_info->component_attr[i].level);

                // 升降电机逻辑特殊处理
                // 1 上位，滚筒放下，可拖地 , 0 中位, 缩回 , 0.5 下位，排污水
                if (i == UP_DOWM_PUSH_ROD) {
                    if (is_floats_equl(clean_ctrl_component_info->component_attr[i].level, 1.0)) {
                        clean_info.clean_module_ctrl[UP_DOWM_PUSH_ROD].attr = DOWN_STATE;
                    } else if (is_floats_equl(clean_ctrl_component_info->component_attr[i].level, 0.0)) {
                        clean_info.clean_module_ctrl[UP_DOWM_PUSH_ROD].attr = UP_STATE;
                    } else if (is_floats_equl(clean_ctrl_component_info->component_attr[i].level, 0.5)) {
                        clean_info.clean_module_ctrl[UP_DOWM_PUSH_ROD].attr = SEWAGE_STATE;
                    }
                }

                clean_info.clean_module_ctrl[i].rpm                     = clean_info.clean_module_rpm[i];
                clean_info.clean_module_ctrl[i].is_control              = false;
                clean_ctrl_component_info->last_component_attr[i].level = clean_ctrl_component_info->component_attr[i].level;
                // 当组件档位发生变化时，就需要重置检测时间
                reset_motor_det_ts(i);
            }

            // 控制转动角度
            if (clean_ctrl_component_info->last_component_attr[i].control_time != clean_ctrl_component_info->component_attr[i].control_time) {
                if (i == ROLLER_TUBE) {
                    if (clean_ctrl_component_info->component_attr[i].control_time > 0) {
                        clean_info.ruller_tube_72_tim        = clean_ctrl_component_info->component_attr[i].control_time;
                        clean_info.clean_module_ctrl[i].attr = CTRL_RULLER_TUBE_ANGLE_72;
                        ruller_tube_rpm                      = clean_info.clean_module_rpm[i];

                    } else {
                        clean_info.ruller_tube_72_tim        = -1;
                        clean_info.clean_module_ctrl[i].attr = CTRL_RULLER_TUBE_RUN;
                    }
                }
                clean_ctrl_component_info->last_component_attr[i].control_time = clean_ctrl_component_info->component_attr[i].control_time;
            }
        }

        // 地毯检测
        if (is_timeout(carpet_det_ts, CARPET_DET_TIME)) {
            carpet_det_ts = osKernelGetTickCount();
            carpet_detect();
        }

        for (uint8_t i = 0; i < CARPET_SENSOR_ID_MAX; i++) {
            if (clean_info.carpet_sensor_state[i] != clean_info.carpet_sensor_state_last[i]) {
                carpet_public_callback(i, clean_info.carpet_sensor_state[i]);
                clean_info.carpet_sensor_state_last[i] = clean_info.carpet_sensor_state[i];
                osDelay(100);
            }
        }

        clean_module_ctrl();

        osDelay(MOTOR_CTRL_THREAD_YIELD_TIME);
    }
}

void change_motor_rpm(uint8_t index, uint8_t rpm) {
    switch (index) {
        case FAN_MOTOR:
            fan_rpm                                     = rpm;
            clean_info.clean_module_ctrl[FAN_MOTOR].rpm = rpm;
            break;

        case SIDE_BRUSH:
            side_rush_rpm = rpm;
            break;

        case ROLLER_BRUSH:
            ruller_rush_rpm = rpm;
            break;

        case ROLLER_TUBE:
            ruller_tube_rpm = rpm;
            break;

        default:
            break;
    }
    log_i("change index[%d] rpm[%d]!!!", index, rpm);
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), change_motor_rpm, change_motor_rpm, change_motor_rpm);

int get_moudle_motor_rmp(CLEAN_MODULE_TYPE_E clean_module) {
    return clean_info.clean_module_ctrl[clean_module].rpm;
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), get_moudle_motor_rmp, get_moudle_motor_rmp,
                 get_moudle_motor_rmp);

int get_moudle_task_raw_rmp(CLEAN_MODULE_TYPE_E clean_module) {
    return clean_info.clean_module_rpm[clean_module];
}