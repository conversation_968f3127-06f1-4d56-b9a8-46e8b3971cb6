/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:
 ** Author:         简永翔
 ** Version:        V0.0.1
 ** Date:           2021-3-25
 ** Description:	充电业务
 ** Others:
 ** Function List:
 ** History:        2021-11 简永翔 创建
 ** <time>          <author>    <version >    <desc>
 ** 2021-3-25       简永翔						1.0         创建文件
 ******************************************************************/

/*****************************************************************
 * 包含头文件
 ******************************************************************/
#include "fal_charge.h"
#include "fal.h"
#include "fal_security.h"
#include "devices.h"
#include "define.h"
#include <stdarg.h>
#include <stdio.h>
#include <string.h>
#include "ir_simuart.h"
#include "shell.h"
#include "crc16.h"
#include "fal_led.h"
#include "fal_clean.h"
#include "fal_button.h"
#include "define_bms.h"
#include "utils_tick.h"
#include "adc_reference.h"
#define LOG_TAG "fal_charge"
#include "log.h"
#include "string.h"
#include "pubsub.h"
/**
 * @addtogroup Robot_FAL
 * @{
 */

/**
 * @defgroup
 *
 * @brief
 * \n
 * @{
 */

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 私有宏定义
 ******************************************************************/
#define IR_38K_RESEND_COUNT 4
/*****************************************************************
 * 私有结构体/共用体/枚举定义
 ******************************************************************/

/*****************************************************************
 * 全局变量定义
 ******************************************************************/
const osThreadAttr_t charge_attributes = {.name = "charge", .priority = (osPriority_t) osPriorityNormal, .stack_size = 384 * 4};

Charge_Date_Typedef charge_cb;
/*****************************************************************
 * 私有全局变量定义
 ******************************************************************/
static uint8_t charge_feedback_data         = 0;
static uint8_t auto_charge_data_buff[2]     = {0};
static uint8_t auto_charge_ir_txbuff[10]    = {0};
static uint8_t auto_charge_ir_rxbuff[2][10] = {0};
static uint8_t bluetooth_addr_buff[10]      = {1, 2, 3, 4, 5, 6, 1, 2, 3, 4};

static uint8_t  update_timeout  = 50;
static uint8_t  ir_rand         = 0xA0;
static uint8_t  g_ir_ptr[2]     = {0};
static uint8_t  ir_handshake_ok = 0;
static uint8_t  ir_check_ok[2]  = {0};
static uint32_t rx_reset_ts     = 0;

static uint32_t charge_io_high_ts         = 0;
static uint32_t auto_charge_running_delay = 0;
// c3在桩上时每隔1秒会收到桩端ir的压桩信号 该变量用于桩断电情况
// 若本体在桩上经过该超时时间后还未收到压桩信号 则判断桩已经断电或ir被遮挡 把本体置为已脱桩状态
static int     get_electrode_down_ir_overtime = ELEC_PRESS_OVERTIME;  //超时时间为3000ms
static uint8_t auto_charge_start              = 0;
static uint8_t auto_charge_pile               = 0;

static uint16_t pile_id                        = 0;
static uint32_t uros_charge_feedback_time      = 0;
static bool     uros_charge_feedback_send_flag = false;
static bool     bluetooth_get_flag             = false;
static bool     bluetooth_pub_flag             = false;
static bool     sidebrush_run_flag             = false;
static bool     motor_error_led_clear_flag     = false;
/*****************************************************************
 * 外部变量声明（如果全局变量没有在其它的H文件声明，引用时需在此处声明，
 *如果已在其它H文件声明，则只需包含此H文件即可）
 ******************************************************************/
extern int ir_rx1_handle;
extern int ir_rx2_handle;
extern int ir_tx1_handle;
extern int wheel_handle;
extern int battery_handle;
extern int charge_en_handle;
extern int side_brush_handle;
extern int charge_voltage_handle;
extern int switch_charge_handle;

extern security_t wheel;
/*****************************************************************
 * 私有函数原型声明
 ******************************************************************/
static void charge_run(void *argument);

static void ir1_data_callback(uint8_t data);
static void ir2_data_callback(uint8_t data);

static void ir_send_message(uint8_t head, uint16_t pile_id);
static void ir_send_selfcheck(uint8_t head);
static void set_charge_feedback(CHARGE_FEEDBACK_E feedback);
uint8_t     get_charge_feedback(void);
/*****************************************************************
 * 函数定义
 ******************************************************************/

/*****************************************************************
 * 函数定义
 *注意，编写函数需首先定义所有的局部变量等，不允许在
 *函数的中间出现新的变量的定义。
 ******************************************************************/
int fal_charge_init(void) {
    device_ioctl(ir_rx1_handle, SIMUART_CALLBACK, (void *) &ir1_data_callback);
    device_ioctl(ir_rx2_handle, SIMUART_CALLBACK, (void *) &ir2_data_callback);

    osThreadNew(charge_run, NULL, &charge_attributes);
    return 0;
}
FAL_MODULE_INIT(fal_charge_init);

void charge_run(void *argument) {
    uint32_t charge_loc_update_ts = 0;
    uint32_t ir_check_ts          = 0;
    // uint32_t mac_send_ts          = 0;
    uint32_t ir_handshake_ts        = 0;
    uint32_t feed_back_send_ts      = 0;
    uint32_t auto_charge_packing_ts = 0;
    uint32_t charge_led_ts          = 0;
    uint32_t sidebrush_run_count    = 0;

    uint8_t ir_send_start  = IR_38K_RESEND_COUNT;
    uint8_t ir_check_start = 1;

    BMS_DATA battery = {0};

    while (1) {
        if (auto_charge_start) {
            auto_charge_start = 0;
            /*收到对桩请求命令开启红外位置更新，120s内电极压下，视为对桩成功*/
            auto_charge_pile          = 1;
            auto_charge_running_delay = osKernelGetTickCount();
            log_i("38K tx, pile_id = 0x%2x, ir_start:%d, ir_rand:0x%2x", pile_id, ir_send_start, ir_rand);
            auto_charge_data_buff[0] = 0;
            auto_charge_data_buff[1] = 0;
            charge_feedback_data     = 0;
            /*200ms内判断是否收到握手响应*/
            ir_handshake_ts = osKernelGetTickCount();
            ir_handshake_ok = 0;
            ir_send_message(0x55, pile_id);
        }

        if ((!is_timeout(ir_handshake_ts, 200)) && (ir_handshake_ok == 1)) {
            ir_send_message(0x56, pile_id);
            ir_handshake_ok = 0;
        }
        /*等待新工作站适配后打开*/
        if (is_timeout(uros_charge_feedback_time, 5000) && charge_feedback_data != CHARGE_ELEC_UNKNOWN) {
            set_charge_feedback(CHARGE_ELEC_UNKNOWN);
            fal_charge_control_en(false);
            uros_charge_feedback_send_flag = true;
            bluetooth_get_flag             = false;
        }
        /* 发布充电反馈 */
        if (uros_charge_feedback_send_flag) {
            uros_charge_feedback_send_flag = false;
            static uint8_t data_last       = 0;
            if (charge_feedback_data != data_last || is_timeout(feed_back_send_ts, 2000)) {
                log_i("pub charge_feedback: %d", charge_feedback_data);
                if (charge_feedback_data == AUTO_CHARGE_ELEC_PRESS) {
                    mcu_motor_speed_vw(-40, 0);
                    osDelay(500);
                    mcu_motor_speed_vw(0, 0);

                    // 原地抱死1秒再释放驱动轮
                    osDelay(1000);
                    device_ioctl(wheel_handle, MOTOR_CMD_BRAKE_DISABLE, NULL);

                    security_stop_ms(2000);
                }
                if (charge_cb.charge_feedback_pub) {
                    charge_cb.charge_feedback_pub(&charge_feedback_data);
                }
                if (charge_feedback_data != CHARGE_ELEC_UNKNOWN) {
                    SetSewageStatus(SEWAGE_SWITCH_CLOSE);
                }
                data_last = charge_feedback_data;
            }
            feed_back_send_ts = osKernelGetTickCount();
        }

        /* 发布蓝牙MAC和PIN */
        // if (bluetooth_pub_flag && fal_pmu_get_sta() == PMU_STA_RUNNING) {
        if (bluetooth_pub_flag) {
            log_d("pub bluetooth address");
            bluetooth_pub_flag = 0;
            if (charge_cb.bluetooth_addr_pub) {
                charge_cb.bluetooth_addr_pub(bluetooth_addr_buff);
            }
        }

        if (!is_timeout(auto_charge_running_delay, 120000) && auto_charge_pile) {
            if (is_timeout(charge_loc_update_ts, update_timeout)) {
                charge_loc_update_ts = osKernelGetTickCount();

                static uint8_t ir_buff[2];
                ir_buff[0] = auto_charge_data_buff[0];
                ir_buff[1] = auto_charge_data_buff[1];
                log_d("uros_ir_message :0x%2x, 0x%2x", ir_buff[0], ir_buff[1]);
                if (charge_cb.ir_position_pub) {
                    charge_cb.ir_position_pub(ir_buff);
                }
                memset(auto_charge_data_buff, 0, sizeof(auto_charge_data_buff));
                ir_send_start = 0;

                // 灯光闪烁控制
                cycle_led_t charge_led_info;
                charge_led_info.cycle      = 200;
                charge_led_info.duty_cycle = 0.5;

                device_ioctl(battery_handle, BMS_GET_DATA, &battery);
                // 是否电量低
                if (battery.bms_percentage < LOW_BATTERY_POWER) {
                    priority_control_set(&led_control, INFO_LVL + INFO_LVL, led_red_cycle, &charge_led_info, "charge");
                } else {
                    priority_control_set(&led_control, INFO_LVL + INFO_LVL, led_blue_cycle, &charge_led_info, "charge");
                }
            }
        }

        /*拉低后2s后拉高*/
        if (is_timeout(charge_io_high_ts, 2000)) {
            device_ioctl(charge_en_handle, GPIO_ACTIVE_HIGH, NULL);
        }

        /*开机红外自检*/
#if 0
        if ((osUint64KernelGetTickCount() > ir_check_ts) && ir_check_start) {
            ir_check_ts = osUint64KernelGetTickCount() + 1000;
            ir_send_selfcheck(ir_rand);

            if (ir_check_ok[0] && ir_check_ok[1]) {
                ir_check_ts = 0xFFFFFFFFFFFFFFFF;
                log_i("[OK] Robot selftest: IR");
            } else {
                log_w("[ERROR] Robot selftest: IR");
            }
        }
#endif
        if (is_timeout(charge_led_ts, 1000)) {
            charge_led_ts = osKernelGetTickCount();
            device_ioctl(battery_handle, BMS_GET_DATA, &battery);
            // 是否电量低
            if (battery.bms_percentage < LOW_BATTERY_POWER) {
                // 红色
                // 是否正在充电
                if (battery.bms_status == POWER_SUPPLY_STATUS_CHARGING) {
                    // 红色呼吸
                    priority_control_set(&led_control, WARN_LVL, led_red_breathe, NULL, "low_power_charging");
                } else {
                    priority_control_set(&led_control, WARN_LVL, led_red, NULL, "low_power");
                }
            } else {
                // 蓝色
                // 是否正在充电
                if (battery.bms_status == POWER_SUPPLY_STATUS_CHARGING) {
                    // 蓝色呼吸
                    priority_control_set(&led_control, WARN_LVL, led_blue_breathe, NULL, "normal_power_charging");
                } else {
                    priority_control_set(&led_control, WARN_LVL, led_blue, NULL, "normal_power");
                }
            }
        }

        if (motor_error_led_clear_flag) {
            motor_error_led_clear_flag = false;
            clear_motor_led_state();
        }

        if (is_timeout(rx_reset_ts, 15)) {
            memset(g_ir_ptr, 0, sizeof(g_ir_ptr));
        }

        int run_rpm  = 40;
        int stop_rpm = 0;
        if (!READ_BIT(wheel.state, EMERG_CMD) && sidebrush_run_flag) {
            sidebrush_run_flag  = 0;
            sidebrush_run_count = 1000;
            device_ioctl(side_brush_handle, MOTOR_CMD_SET_RPM, (void *) &run_rpm);
            log_i("side_brush run");
        } else if (sidebrush_run_count) {
            sidebrush_run_count--;
            if (sidebrush_run_count == 0) {
                device_ioctl(side_brush_handle, MOTOR_CMD_SET_RPM, (void *) &stop_rpm);
                log_i("side_brush stop");
            }
        }
        //处理本体在桩上充电桩断电后驱动轮不会锁死的问题
        //若本体在桩上经过3s后还未收到压桩信号 则判断桩已经断电或ir被遮挡 把本体置为已脱桩状态
        if (get_charge_feedback() == AUTO_CHARGE_ELEC_PRESS || get_charge_feedback() == PUSH_CHARGE_ELEC_PRESS) {
            get_electrode_down_ir_overtime -= 2;
            if (get_electrode_down_ir_overtime <= 0) {
                //判断充电桩已经断电
                set_charge_feedback(CHARGE_ELEC_UP);
                enable_wheel_brake();
                fal_charge_control_en(false);
                uros_charge_feedback_time      = osKernelGetTickCount();
                ir_rand                        = 0xA0;
                get_electrode_down_ir_overtime = ELEC_PRESS_OVERTIME;
            }
        }
        osDelay(2);
    }
}

void fal_charge_control_en(bool open) {
    if (open) {
        device_ioctl(charge_en_handle, GPIO_ACTIVE_HIGH, NULL);
    } else {
        device_ioctl(charge_en_handle, GPIO_ACTIVE_LOW, NULL);
        charge_io_high_ts = osKernelGetTickCount();
    }
    log_i("charge_en %d", open);
}

static void ir_update_time(uint8_t time) {
    update_timeout = time;
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), ir_update_time, ir_update_time, ir_update_time);

static void ir_send_message(uint8_t head, uint16_t pile_id) {
    auto_charge_ir_txbuff[0] = head;
    auto_charge_ir_txbuff[1] = (uint8_t)(pile_id >> 8);
    auto_charge_ir_txbuff[2] = (uint8_t) pile_id;
    auto_charge_ir_txbuff[3] = ir_rand;
    auto_charge_ir_txbuff[4] = (uint8_t) calculate_crc16(auto_charge_ir_txbuff, 4);

    device_write(ir_tx1_handle, auto_charge_ir_txbuff, 5);
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), ir_send_message, ir_send_message, ir_send_message);

static void ir_send_selfcheck(uint8_t head) {
    auto_charge_ir_txbuff[0] = head;
    auto_charge_ir_txbuff[1] = SEND_LOC_SEND_EN_ACK_CMD;
    auto_charge_ir_txbuff[2] = ~auto_charge_ir_txbuff[1];

    device_write(ir_tx1_handle, auto_charge_ir_txbuff, 3);
}

uint8_t fal_charge_pull_in(ir_38k_send_cmd_enum_t cmd) {
    if (cmd == SEND_ELECTRODE_DOWN_CMD) {
        /* 压桩状态为弹起或未知才需要更新设置，否则不用重复设置。*/
        if (charge_feedback_data == CHARGE_ELEC_UP || charge_feedback_data == CHARGE_ELEC_UNKNOWN) {
            /*自动对桩压下，，500ms后关闭位置更新*/

            if (!is_timeout(auto_charge_running_delay, 120000)) {
                auto_charge_pile = 0;
                set_charge_feedback(AUTO_CHARGE_ELEC_PRESS);
                // device_ioctl(wheel_handle, MOTOR_CMD_BRAKE_DISABLE, NULL);
            } else {
                set_charge_feedback(PUSH_CHARGE_ELEC_PRESS);
                device_ioctl(wheel_handle, MOTOR_CMD_BRAKE_DISABLE, NULL);
            }
            sidebrush_run_flag         = true;
            motor_error_led_clear_flag = true;
            if (READ_BIT(wheel.state, EMERG_CMD)) {
                net_release_emerg_handle();
            }
        }
        get_electrode_down_ir_overtime = ELEC_PRESS_OVERTIME;
    } else if (cmd == SEND_ELECTRODE_UP_CMD) {
        set_charge_feedback(CHARGE_ELEC_UP);
        enable_wheel_brake();
        fal_charge_control_en(false);
    }

    uros_charge_feedback_time = osKernelGetTickCount();

    ir_rand = 0xA0;
    return 0;
}

void rx_data_parse(const uint8_t rx_index, const uint8_t data) {
    uint8_t *ir_buff = auto_charge_ir_rxbuff[rx_index];
    uint8_t *ir_ptr  = &g_ir_ptr[rx_index];

    log_v("rx%d[%d]: %2x", rx_index, *ir_ptr, data);
    rx_reset_ts          = osKernelGetTickCount();
    ir_buff[(*ir_ptr)++] = data;

    if (ir_buff[0] != ir_rand) {
        *ir_ptr = 0;
    }

    if (ir_buff[1] == 0xFE || ir_buff[1] == 0xEE || ir_buff[1] == 0xAE || ir_buff[1] == 0xAF || ir_buff[1] == 0xBF) {
        //接受到红外信号
        *ir_ptr                         = 0;
        ir_check_ok[rx_index]           = 1;
        auto_charge_data_buff[rx_index] = ir_buff[1];
        ir_buff[1]                      = 0;
    } else if (ir_buff[1] == SEND_RFCOMM_MAC_CMD || ir_buff[1] == SEND_BLE_MAC_CMD) {
        if (*ir_ptr >= 9) {
            *ir_ptr           = 0;
            uint8_t check_sum = 0;
            for (int i = 0; i < 8; i++) {
                check_sum += ir_buff[i];
            }
            if (check_sum == ir_buff[8]) {
                log_d("rx%d mac:%02X %02X %02X %02X %02X %02X", rx_index, ir_buff[2], ir_buff[3], ir_buff[4], ir_buff[5], ir_buff[6],
                      ir_buff[7]);

                if (bluetooth_pub_flag != 1) {
                    memcpy(bluetooth_addr_buff, &ir_buff[2], 6);
                    if (ir_buff[1] == SEND_RFCOMM_MAC_CMD) {
                        bluetooth_addr_buff[6] = 9;
                        bluetooth_addr_buff[7] = 5;
                        bluetooth_addr_buff[8] = 2;
                        bluetooth_addr_buff[9] = 7;
                    } else if (ir_buff[1] == SEND_BLE_MAC_CMD) {
                        bluetooth_addr_buff[6] = 0;
                        bluetooth_addr_buff[7] = 0;
                        bluetooth_addr_buff[8] = 0;
                        bluetooth_addr_buff[9] = 0;
                    }

                    bluetooth_get_flag = 1;
                    bluetooth_pub_flag = 1;
                }
                uros_charge_feedback_time = osKernelGetTickCount();
            }
            ir_buff[1] = 0;
        }
    } else if (ir_buff[1] == SEND_ELECTRODE_DOWN_CMD || ir_buff[1] == SEND_ELECTRODE_UP_CMD) {
        if (*ir_ptr >= 3) {
            *ir_ptr = 0;
            if (ir_buff[1] + ir_buff[2] == 255) {
                //电极压下或弹开
                fal_charge_pull_in(ir_buff[1]);
            }
            ir_buff[1] = 0;
        }
    } else if (ir_buff[1] == SEND_LOC_SEND_EN_ACK_CMD) {
        if (*ir_ptr >= 3) {
            *ir_ptr = 0;
            if (ir_buff[1] + ir_buff[2] == 255) {
                //握手成功
                ir_check_ok[rx_index] = 1;
                ir_handshake_ok       = 1;
            }
            ir_buff[1] = 0;
        }
    } else if (*ir_ptr >= 2) {
        *ir_ptr = 0;
    }
}

static void ir1_data_callback(uint8_t data) {
    rx_data_parse(1, data);
}

static void ir2_data_callback(uint8_t data) {
    rx_data_parse(0, data);
}

void charge_pub_cb_set(pal_data_pub pub_cb, CHARGE_CB_E cmd) {
    switch (cmd) {
        case IR_MESSAGE:
            charge_cb.ir_message_pub = pub_cb;
            break;

        case CHARGE_FEEDBACK:
            charge_cb.charge_feedback_pub = pub_cb;
            break;

        case BLUETOOTH_ADDR:
            charge_cb.bluetooth_addr_pub = pub_cb;
            break;

        case IR_POSITION:
            charge_cb.ir_position_pub = pub_cb;
            break;
        default:
            break;
    }
}

void get_charger_voltage_in(void) {
    uint32_t charger_voltage_adc = 0;
    device_ioctl(switch_charge_handle, GPIO_ACTIVE_LOW, NULL);
    osDelay(200);
    device_ioctl(charge_voltage_handle, CMD_READ_RAW_ADC_VALUE, &charger_voltage_adc);
    device_ioctl(switch_charge_handle, GPIO_ACTIVE_HIGH, NULL);
    float charge_voltage = (float) (charger_voltage_adc * 3.3 / 4096) * 11;
    log_w("adc : %d, charge_voltage:%.2f", charger_voltage_adc, charge_voltage);
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), get_charger_voltage_in, get_charger_voltage_in,
                 get_charger_voltage_in);

void set_charge_feedback(CHARGE_FEEDBACK_E feedback) {
    charge_feedback_data           = feedback;
    uros_charge_feedback_send_flag = true;
}

uint8_t get_charge_feedback(void) {
    return charge_feedback_data;
}

void auto_charge_event_start(void) {
    auto_charge_start = 1;
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), auto_charge_event_start, auto_charge_event_start,
                 auto_charge_event_start);

void auto_charge_event_end(void) {
    //强制结束自动对桩
    auto_charge_running_delay = 0xffffffff;
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), auto_charge_event_end, auto_charge_event_end,
                 auto_charge_event_end);

void pile_id_set(uint16_t data) {
    pile_id = data;
}

void bluetooth_pub_set(bool flag) {
    if (bluetooth_get_flag) {
        bluetooth_pub_flag = flag;
    } else {
        bluetooth_pub_flag = false;
    }
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), bluetooth_pub_set, bluetooth_pub_set, bluetooth_pub_set);

#ifdef __cplusplus
}
#endif

/* @} Robot_PAL_UROS */
/* @} Robot_PAL */
