/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:
 ** Author:         陈寿义
 ** Version:        V0.0.1
 ** Date:           2023-2-1
 ** Description:
 ** Others:
 ** Function List:
 ** History:        2023-02 陈寿义 创建
 ** <adce>          <author>    <version >    <desc>
 ** 2023-2-1       陈寿义	     0.0.1         创建文件
 ******************************************************************/

/*****************************************************************
 * 包含头文件
 ******************************************************************/
#include "devices.h"
#include "define.h"
#include "adc_core.h"
#include "tim_core.h"
#include "gpio_core.h"
#include <stddef.h>
#include <string.h>
#include "cmsis_os.h"
#include "hal.h"
#include "tof_cliff.h"
#include "delay.h"
#include "adc.h"
#include "mem_pool.h"
#define LOG_TAG "tof_cliff"
#include "log.h"
#ifdef USING_SEGGER
#include "SEGGER_RTT.h"
#endif
#include "shell.h"
#include <string.h>
#include "define_cliff.h"
/**
 * @addtogroup Robot_DEVICES
 * @{
 */

/**
 * @defgroup Robot_GPIO_CORE  - GPIO_CORE
 *
 * @brief  \n
 * \n
 * @{
 */

#ifdef __cplusplus
extern "C" {
#endif
/*****************************************************************
 * 私有宏定义
 ******************************************************************/
#define QUEUE_MAX 3
#define CLIFF_CNT 1
/*****************************************************************
 * 私有结构体/共用体/枚举定义
 ******************************************************************/
struct tof_cliff_args {
    struct list_struct adc_list;  //数据回调函数链表
    osThreadId_t       tof_cliff_thread;
    bool               enable_thread;
};

struct cliff_adc_bus_node {
    struct list_struct node;
    struct ca_bus *    tof_cliff_bus;  //断崖的adc总线
    uint8_t            cliff_count;
    uint8_t            cliff_status;  //检测断崖状态 0：非断崖   1：断崖
    char               dev_name[16];  //设备名称
    int32_t            value;
    int32_t            cliff_value[QUEUE_MAX];  //缓存滤波数组
    uint8_t            queue;
    uint16_t           v_diff;  //过滤数据
};

/*****************************************************************
 * 全局变量定义
 ******************************************************************/
static char cliff_dev_name[16];  //断崖设备名称（调试使用）
/*****************************************************************
 * 私有全局变量定义
 ******************************************************************/
osThreadAttr_t tof_cliff_attributes = {.name = "tof_cliff", .priority = (osPriority_t) osPriorityNormal, .stack_size = 256 * 4};
/*****************************************************************
 * 外部变量声明
 ******************************************************************/

/*****************************************************************
 * 私有函数原型声明
 ******************************************************************/
void tof_cliff_run(void *argument);
/*****************************************************************
 * 函数定义
 ******************************************************************/
int tof_cmpfunc(const void *a, const void *b) {
    return (*(int *) a - *(int *) b);
}

int32_t tof_cliff_open(const struct ca_device *dev, int32_t flags) {
    return 0;
}

int32_t tof_cliff_close(const struct ca_device *dev) {
    if (NULL == dev) {
        return -1;
    }
    struct tof_cliff_args *p_cliff_args = NULL;
    p_cliff_args                        = (struct tof_cliff_args *) dev->device_args;
    if (p_cliff_args->tof_cliff_thread) {
        osThreadSuspend(p_cliff_args->tof_cliff_thread);
    }
    p_cliff_args->enable_thread = false;
    return 0;
}

/*****************************************************************/
/**
 * Function:       gpio_read
 * Description:
 * Calls:
 * Called By:
 * @param[in]
 * @param[out]
 * @return
 *  - 0 表示成功
 *  - -1 表示失败
 *
 * @author:
 * @date
 *
 * @par Modification History
 * @par Author:
 * @par Date:
 * @par Description:
 *
 * @see
 *
 */
/******************************************************************/

int32_t tof_cliff_set_adc(const struct ca_device *dev, adc_sensor_obj_st *cliff_sensor_obj) {
    if (NULL == dev || NULL == cliff_sensor_obj) {
        return -1;
    }
    struct bus_info *          adc_bus    = NULL;
    struct ca_bus *            bus        = NULL;
    struct cliff_adc_bus_node *p_callback = NULL;
    struct tof_cliff_args *    cliff_attr = (struct tof_cliff_args *) dev->device_args;
    p_callback                            = mem_block_alloc(sizeof(struct cliff_adc_bus_node));
    memset(p_callback, 0, sizeof(struct cliff_adc_bus_node));
    adc_bus = cliff_sensor_obj->bus_com;
    if (NULL == p_callback) {
        return -1;
    }
    if (bus_find_name(adc_bus, &bus)) {
        return -1;
    }
    p_callback->tof_cliff_bus = bus;
    memcpy(p_callback->dev_name, cliff_sensor_obj->dev_name, strlen((char *) cliff_sensor_obj->dev_name));
    list_add_tail(&p_callback->node, &cliff_attr->adc_list);
    return 0;
}

int32_t tof_cliff_get_adc(const struct ca_device *dev, adc_sensor_obj_st *cliff_sensor_obj) {
    if (NULL == dev || NULL == cliff_sensor_obj) {
        return -1;
    }

    if (NULL == cliff_sensor_obj->bus_com) {
        return -1;
    }

    struct list_struct *       p_list     = NULL;
    struct cliff_adc_bus_node *p_callback = NULL;
    struct tof_cliff_args *    cliff_args = (struct tof_cliff_args *) dev->device_args;

    p_list = &cliff_args->adc_list;
    while (list_is_last(p_list, &cliff_args->adc_list) != 1) {
        p_callback = (struct cliff_adc_bus_node *) p_list->next;
        if (NULL != p_callback->tof_cliff_bus) {
            if (0 == strcmp((char *) p_callback->dev_name, (char *) cliff_sensor_obj->dev_name)) {
                return p_callback->value;
            }
        }
        p_list = p_list->next;
    }

    return -1;
}

int32_t tof_cliff_get_state(const struct ca_device *dev, adc_sensor_obj_st *cliff_sensor_obj) {
    if (NULL == dev || NULL == cliff_sensor_obj) {
        return -1;
    }

    if (NULL == cliff_sensor_obj->bus_com) {
        return -1;
    }

    struct list_struct *       p_list     = NULL;
    struct cliff_adc_bus_node *p_callback = NULL;
    struct tof_cliff_args *    cliff_args = (struct tof_cliff_args *) dev->device_args;

    p_list = &cliff_args->adc_list;
    while (list_is_last(p_list, &cliff_args->adc_list) != 1) {
        p_callback = (struct cliff_adc_bus_node *) p_list->next;
        if (NULL != p_callback->tof_cliff_bus) {
            if (0 == strcmp((char *) p_callback->dev_name, (char *) cliff_sensor_obj->dev_name)) {
                return p_callback->cliff_status;
            }
        }
        p_list = p_list->next;
    }

    return -1;
}

int32_t tof_cliff_get_status(const struct ca_device *dev) {
    if (NULL == dev) {
        return -1;
    }
    struct tof_cliff_args *    cliff_args = NULL;
    struct list_struct *       p_list     = NULL;
    struct cliff_adc_bus_node *p_callback = NULL;
    cliff_args                            = (struct tof_cliff_args *) dev->device_args;

    p_list = &cliff_args->adc_list;
    int i  = 0;
    while (list_is_last(p_list, &cliff_args->adc_list) != 1) {
        p_callback = (struct cliff_adc_bus_node *) p_list->next;
        if (NULL != p_callback->tof_cliff_bus && (0 == strncmp((char *) p_callback->dev_name, "cliff", 5))) {
            i |= p_callback->cliff_status;
        }
        p_list = p_list->next;
    }
    return i;
}

int32_t tof_cliff_read(const struct ca_device *dev, void *buffer, uint32_t len) {
    if ((NULL == dev) || (NULL == dev->bus) || (NULL == dev->bus->handler) || (NULL == buffer)) {
        return -1;
    }

    return 0;
}

bool check_tof_cliff(struct cliff_adc_bus_node *p_callback) {
    int cliff_buff[CLIFF_TYPE_CHECK_BUFF_SIZE] = {0};
    int min_avg                                = 0;
    int max_avg                                = 0;
    int temp                                   = 0;
    int buffer_max_size                        = CLIFF_TYPE_CHECK_BUFF_SIZE;
    int buffer_half_size                       = 0;

    if (CLIFF_TYPE_CHECK_BUFF_SIZE % 2 != 0) {
        buffer_max_size = CLIFF_TYPE_CHECK_BUFF_SIZE - 1;
    }

    buffer_half_size = buffer_max_size / 2;

    for (int i = 0; i < buffer_max_size;) {
        if (adc_read(p_callback->tof_cliff_bus, p_callback->tof_cliff_bus->bus_addr, &cliff_buff[i]) == 0) {
            i++;
        }
        osDelay(1);
    }
    qsort(cliff_buff, buffer_max_size, sizeof(int), tof_cmpfunc);

    // 求最小值平均
    for (int i = 0; i < buffer_half_size; i++) {
        temp += cliff_buff[i];
    }
    min_avg = temp / buffer_half_size;
    temp    = 0;

    // 求最大值平均
    for (int i = buffer_half_size; i < buffer_max_size; i++) {
        temp += cliff_buff[i];
    }
    max_avg = temp / buffer_half_size;
    temp    = 0;

    log_i("check tof cliff type, %s max_avg:%d,min_avg:%d,max_avg - min_avg:%d", p_callback->dev_name, max_avg, min_avg, max_avg - min_avg);

    if (max_avg - min_avg <= CLIFF_TOF_LOWER_THRES && max_avg > TOF_CLIFF_HIGH_LEVEL_ADC && min_avg > TOF_CLIFF_HIGH_LEVEL_ADC) {
        return true;
    } else {
        return false;
    }
}

bool is_tof_cliff(const struct ca_device *dev) {
    struct tof_cliff_args *    cliff_args = (struct tof_cliff_args *) dev->device_args;
    struct list_struct *       p_list     = &cliff_args->adc_list;
    struct cliff_adc_bus_node *p_callback = (struct cliff_adc_bus_node *) p_list->next;
    bool                       is_tof     = false;

    if (cliff_args->enable_thread) {
        osThreadSuspend(cliff_args->tof_cliff_thread);
    }

    // 判断断崖类型
    while (list_is_last(p_list, &cliff_args->adc_list) != 1) {
        p_callback = (struct cliff_adc_bus_node *) p_list->next;
        if (strstr(p_callback->dev_name, "cliff") != NULL) {
            if (p_callback->tof_cliff_bus && check_tof_cliff(p_callback)) {
                is_tof = true;
                break;
            }
        }
        p_list = p_list->next;
    }

    log_i("check tof ,res:%s", is_tof ? "is tof" : "not tof");

    if (cliff_args->enable_thread) {
        osThreadResume(cliff_args->tof_cliff_thread);
    }

    return is_tof;
}

/*****************************************************************/
/**
 * Function:       gpio_write
 * Description:
 * Calls:
 * Called By:
 * @param[in]
 * @param[out]
 * @return
 *  - 0 表示成功
 *  - -1 表示失败
 *
 * @author:
 * @date
 *
 * @par Modification History
 * @par Author:
 * @par Date:
 * @par Description:
 *
 * @see
 *
 */
/******************************************************************/
int32_t tof_cliff_write(const struct ca_device *dev, void *buffer, uint32_t len) {
    int32_t ret = -1;

    if ((NULL == dev) || (NULL == dev->bus) || (NULL == dev->bus->handler) || (NULL == buffer)) {
        return -1;
    }

    return ret;
}

int32_t tof_cliff_ioctl(const struct ca_device *dev, uint32_t cmd, void *arg) {
    int32_t ret = -1;
    if (NULL == dev) {
        return -1;
    }

    struct tof_cliff_args *cliff_args = (struct tof_cliff_args *) dev->device_args;
    switch (cmd) {
        case CLIFF_CMD_SET_DATA:
            tof_cliff_set_adc(dev, (adc_sensor_obj_st *) arg);
            break;
        case CLIFF_CMD_GET_VALUE:
            return tof_cliff_get_adc(dev, (adc_sensor_obj_st *) arg);
            break;
        case CLIFF_CMD_GET_STATE:
            return tof_cliff_get_state(dev, (adc_sensor_obj_st *) arg);
            break;
        case CLIFF_CMD_IS_TYPE:
            return is_tof_cliff(dev);
        case CLIFF_CMD_ENABLE:
            if (!cliff_args->tof_cliff_thread) {
                cliff_args->tof_cliff_thread = osThreadNew(tof_cliff_run, (void *) dev, &tof_cliff_attributes);
            } else {
                osThreadResume(cliff_args->tof_cliff_thread);
            }
            cliff_args->enable_thread = true;
            log_i("enable tof cliff thread");
            break;
        case CLIFF_CMD_DISABLE:
            if (cliff_args->tof_cliff_thread) {
                osThreadSuspend(cliff_args->tof_cliff_thread);
            }
            log_i("disable tof cliff thread");
            cliff_args->enable_thread = false;
            break;
        default:
            break;
    }

    return 0;
}

void tof_cliff_run(void *argument) {
    struct ca_device *     dev        = NULL;
    struct tof_cliff_args *cliff_args = NULL;

    struct list_struct *       p_list     = NULL;
    struct cliff_adc_bus_node *p_callback = NULL;

    double dis      = 0.0;
    char   name[16] = {0};

    dev        = (struct ca_device *) argument;
    cliff_args = (struct tof_cliff_args *) dev->device_args;
    while (1) {
        p_list = &cliff_args->adc_list;
        while (list_is_last(p_list, &cliff_args->adc_list) != 1) {
            p_callback = (struct cliff_adc_bus_node *) p_list->next;
            if (NULL != p_callback->tof_cliff_bus && strstr(p_callback->dev_name, "cliff")) {
                // 获取ADC值
                p_callback->value = 0;
                adc_read(p_callback->tof_cliff_bus, p_callback->tof_cliff_bus->bus_addr, &(p_callback->value));
                if (p_callback->queue < QUEUE_MAX) {
                    p_callback->cliff_value[p_callback->queue] = p_callback->value;
                    p_callback->queue++;
                } else {
                    p_callback->queue = 0;
                    //对数据排序求中位数
                    qsort(p_callback->cliff_value, QUEUE_MAX, sizeof(int), tof_cmpfunc);
                    if (QUEUE_MAX % 2 != 0) {
                        p_callback->v_diff = p_callback->cliff_value[((QUEUE_MAX + 1) / 2) - 1];
                    } else {
                        p_callback->v_diff = (p_callback->cliff_value[(QUEUE_MAX / 2) - 1] + p_callback->cliff_value[(QUEUE_MAX / 2)]) / 2;
                    }
                    if (strcmp((char *) p_callback->dev_name, cliff_dev_name) == 0) {
                        log_d("%s,read:[%d]", p_callback->dev_name, p_callback->v_diff);
                    }
                    if (p_callback->v_diff < TOF_CLIFF_HIGH_LEVEL_ADC) {
                        p_callback->cliff_status = 1;
                    } else {
                        if (p_callback->cliff_status == 1) {
                            p_callback->cliff_status = 0;
                        }
                    }
                }
            }
            p_list = p_list->next;
        }
        osDelay(1);
    }
}

void choose_tof_cliff_debug(const char *cliff) {
    memset(cliff_dev_name, 0, strlen(cliff_dev_name));
    strncpy(cliff_dev_name, cliff, strlen(cliff));
    log_i("You choose cliff_dev is %s", cliff_dev_name);
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), choose_tof_cliff_debug, choose_tof_cliff_debug,
                 choose_tof_cliff_debug);

int32_t tof_cliff_driver_init(struct ca_device *dev) {
    if (NULL == dev) {
        return -1;
    }
    memset(&dev->ops, 0, sizeof(struct ca_device_ops));

    dev->ops.open  = tof_cliff_open;
    dev->ops.close = tof_cliff_close;
    dev->ops.read  = tof_cliff_read;
    dev->ops.write = tof_cliff_write;
    dev->ops.ioctl = tof_cliff_ioctl;

    struct tof_cliff_args *tof_cliff_args = NULL;
    tof_cliff_args                        = (struct tof_cliff_args *) mem_block_alloc(sizeof(struct tof_cliff_args));
    memset(tof_cliff_args, 0, sizeof(struct tof_cliff_args));

    list_head_init(&tof_cliff_args->adc_list);
    dev->device_args = (void *) tof_cliff_args;
    return 0;
}

DEVICE_DRIVER_INIT(tof_cliff, tim, tof_cliff_driver_init);

#ifdef __cplusplus
}
#endif

/* @} Robot_I2C_CORE */
/* @} Robot_DEVICES */
