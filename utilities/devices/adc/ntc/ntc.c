/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2021-2022.
 ** File name:
 ** Author:         liutelin
 ** Version:        V0.0.1
 ** Date:           2023-4-14
 ** Description:
 ** Others:
 ** Function List:
 ** History:        2023-04 liutelin 创建
 ** <adce>          <author>    <version >    <desc>
 ** 2023-4-14       liutelin	0.0.1         创建文件
 ******************************************************************/

/*****************************************************************
 * 包含头文件
 ******************************************************************/
#include "devices.h"
#include "define.h"
#include "adc_core.h"
#include <stddef.h>
#include <string.h>
#include <math.h>
#include "cmsis_os.h"
#include "hal.h"
#include "adc.h"
#include "mem_pool.h"
#define LOG_TAG "ntc"
#include "log.h"
#ifdef USING_SEGGER
#include "SEGGER_RTT.h"
#endif
#include "ntc.h"
#include "digital_filter.h"

/**
 * @addtogroup Robot_DEVICES
 * @{
 */

/**
 * @defgroup Robot_GPIO_CORE  - GPIO_CORE
 *
 * @brief  \n
 * \n
 * @{
 */

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 私有宏定义
 ******************************************************************/
#define FILTER_BUFFER_SIZE 40

/*****************************************************************
 * 私有结构体/共用体/枚举定义
 ******************************************************************/
struct ntc_args {
    struct list_struct ntc_list;
    osTimerId_t        adc_filter_timer;
    int32_t            adc_ntc_value;
};

/*****************************************************************
 * 全局变量定义
 ******************************************************************/

/*****************************************************************
 * 私有全局变量定义
 ******************************************************************/

/*****************************************************************
 * 外部变量声明
 ******************************************************************/

/*****************************************************************
 * 私有函数原型声明
 ******************************************************************/

/*****************************************************************
 * 函数定义
 ******************************************************************/
int32_t ntc_open(const struct ca_device *dev, int32_t flags) {
    return 0;
}

int32_t ntc_close(const struct ca_device *dev) {
    return 0;
}

int32_t ntc_read(const struct ca_device *dev, void *buffer, uint32_t len) {
    return 0;
}

int32_t ntc_write(const struct ca_device *dev, void *buffer, uint32_t len) {
    return 0;
}

int32_t ntc_ioctl(const struct ca_device *dev, uint32_t cmd, void *arg) {
    if (dev == NULL || dev->bus == NULL) {
        return -1;
    }
    struct ntc_args *p_args = (struct ntc_args *) dev->device_args;

    switch (cmd) {
        case NTC_CMD_READ_RAW_ADC_VALUE:
            if (arg != NULL) {
                *(int32_t *) arg = p_args->adc_ntc_value;
            }
            break;
        default:
            break;
    }
    return 0;
}

void ntc_adc_value_filter(struct ca_device *dev) {
    struct ntc_args *p_args                             = (struct ntc_args *) dev->device_args;
    uint16_t         adc_value                          = 0;
    static int       adc_ntc_buffer[FILTER_BUFFER_SIZE] = {0};
    static uint32_t  buffer_index                       = 0;

    if (adc_read(dev->bus, dev->bus->bus_addr, &adc_value) == 0) {
        p_args->adc_ntc_value = moveing_average_filter(adc_ntc_buffer, FILTER_BUFFER_SIZE, adc_value, buffer_index);
        buffer_index++;
    }
}

void ntc_handle_callback(void *argument) {
    ntc_adc_value_filter((struct ca_device *) argument);
}

int32_t ntc_driver_init(struct ca_device *dev) {
    static struct ntc_args *p_args = NULL;

    if (NULL == dev) {
        return -1;
    }

    memset(&dev->ops, 0, sizeof(struct ca_device_ops));

    dev->ops.open  = ntc_open;
    dev->ops.close = ntc_close;
    dev->ops.read  = ntc_read;
    dev->ops.write = ntc_write;
    dev->ops.ioctl = ntc_ioctl;

    p_args = (struct ntc_args *) mem_block_alloc(sizeof(struct ntc_args));
    if (NULL == p_args) {
        return -1;
    }
    memset(p_args, 0, sizeof(struct ntc_args));

    list_head_init(&p_args->ntc_list);
    dev->device_args         = p_args;
    p_args->adc_filter_timer = osTimerNew(ntc_handle_callback, osTimerPeriodic, dev, NULL);
    osTimerStart(p_args->adc_filter_timer, 100);

    return 0;
}
DEVICE_DRIVER_INIT(ntc, adc1, ntc_driver_init);

#ifdef __cplusplus
}
#endif

/* @} Robot_I2C_CORE */
/* @} Robot_DEVICES */
