/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:
 ** Author:         曾曼云
 ** Version:        V0.0.1
 ** Date:           2021-9-25
 ** Description:
 ** Others:
 ** Function List:
 ** History:        2021-09 曾曼云 创建
 ** <adce>          <author>    <version >    <desc>
 ** 2021-9-25       曾曼云	     0.0.1         创建文件
 ******************************************************************/

/*****************************************************************
 * 包含头文件
 ******************************************************************/
#include "devices.h"
#include "define.h"
#include "adc_core.h"
#include "tim_core.h"
#include "gpio_core.h"
#include <stddef.h>
#include <string.h>
#include "cmsis_os.h"
#include "hal.h"
#include "delay.h"
#include "adc.h"
#include "mem_pool.h"
#define LOG_TAG "adc_reference"
#include "log.h"
#ifdef USING_SEGGER
#include "SEGGER_RTT.h"
#endif
#include "shell.h"
#include "adc_reference.h"
/**
 * @addtogroup Robot_DEVICES
 * @{
 */

/**
 * @defgroup Robot_GPIO_CORE  - GPIO_CORE
 *
 * @brief  \n
 * \n
 * @{
 */

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 私有宏定义
 ******************************************************************/

/*****************************************************************
 * 私有结构体/共用体/枚举定义
 ******************************************************************/

/*****************************************************************
 * 全局变量定义
 ******************************************************************/
#define ADC_BASE_RAW_VALUE     (3103)
#define ADC_BASE_VOLTAGE_VALUE (2500)
/*****************************************************************
 * 私有全局变量定义
 ******************************************************************/

/*****************************************************************
 * 外部变量声明
 ******************************************************************/

/*****************************************************************
 * 私有函数原型声明
 ******************************************************************/

/*****************************************************************
 * 函数定义
 ******************************************************************/

static void User_Delay(uint32_t time_ms) {
    if (osKernelGetState() == osKernelRunning) {
        osDelay(time_ms);
    } else {
        HAL_Delay(time_ms);
    }
}

int32_t adc_reference_open(const struct ca_device *dev, int32_t flags) {
    return 0;
}

int32_t adc_reference_close(const struct ca_device *dev) {
    return 0;
}

int32_t adc_reference_read(const struct ca_device *dev, void *buffer, uint32_t len) {
    if (dev == NULL || dev->bus == NULL) {
        return -1;
    }
    uint16_t adc_value = 0;

    if (adc_read(dev->bus, dev->bus->bus_addr, &adc_value) == 0) {
        log_d("adc referenc adc value: %u", adc_value);
    } else {
        log_e("adc referenc conv error");
    }

    return (adc_value * 3300) / 4096;
}

int32_t adc_reference_write(const struct ca_device *dev, void *buffer, uint32_t len) {
    return 0;
}

int32_t adc_reference_ioctl(const struct ca_device *dev, uint32_t cmd, void *arg) {
    if (dev == NULL || dev->bus == NULL) {
        return -1;
    }
    uint16_t adc_value = 0;

    adc_read(dev->bus, dev->bus->bus_addr, &adc_value);

    switch (cmd) {
        case CMD_READ_RAW_ADC_VALUE:
            if (arg != NULL) {
                *(uint32_t *) (arg) = adc_value;
            }
            break;
        case CMD_READ_RAW_VOLTAGE_VALUE:
            if (arg != NULL) {
                *(float *) (arg) = (adc_value * 3300) / 4096 / 1000.0f;
            }
            break;
        case CMD_GET_RAW_ADC_VALUE_OFFSET:
            if (arg != NULL) {
                *(int32_t *) (arg) = adc_value - ADC_BASE_RAW_VALUE;
            }
            break;
        case CMD_GET_RAW_VOLTAGE_VALUE_OFFSET:
            if (arg != NULL) {
                *(float *) (arg) = ((adc_value * 3300) / 4096.0f - ADC_BASE_VOLTAGE_VALUE) / 1000.0f;
            }
            break;
        default:

            break;
    }
    return adc_value;
}
const osThreadAttr_t adc_reference_attributes = {.name = "adc_reference", .priority = (osPriority_t) osPriorityNormal, .stack_size = 256 * 3};

void adc_reference_run(void *argument) {
    if (!argument) {
        return;
    }
    struct ca_device *dev   = (struct ca_device *) argument;
    uint16_t          value = 0;
    while (1) {
        adc_read(dev->bus, dev->bus->bus_addr, &value);
        // printf("adc_reference=%f\r\n", value/100.0f);
        // log_d("adc reference : %u, offset: %d", value, value-3103);
        User_Delay(50);
    }
}

int32_t adc_reference_driver_init(struct ca_device *dev) {
    if (NULL == dev) {
        return -1;
    }

    memset(&dev->ops, 0, sizeof(struct ca_device_ops));

    dev->ops.open  = adc_reference_open;
    dev->ops.close = adc_reference_close;
    dev->ops.read  = adc_reference_read;
    dev->ops.write = adc_reference_write;
    dev->ops.ioctl = adc_reference_ioctl;

    // osThreadNew(adc_reference_run, dev, &adc_reference_attributes);
    return 0;
}

DEVICE_DRIVER_INIT(adc_reference, adc1, adc_reference_driver_init);

extern int adc_reference_handle;
void       read_adc_reference(void) {
    int32_t len = 0;
    // len = device_read(adc_reference_handle, NULL, len);
    // log_d("adc reference voltage : %lu", len);
    device_ioctl(adc_reference_handle, CMD_READ_RAW_ADC_VALUE, &len);
    log_d("adc reference adc raw value: %d", len);
    float value = 0;
    device_ioctl(adc_reference_handle, CMD_READ_RAW_VOLTAGE_VALUE, &value);
    log_d("adc reference adc raw voltage value: %f", value);
    device_ioctl(adc_reference_handle, CMD_GET_RAW_ADC_VALUE_OFFSET, &len);
    log_d("adc reference adc raw offset: %d", len);
    device_ioctl(adc_reference_handle, CMD_GET_RAW_VOLTAGE_VALUE_OFFSET, &value);
    log_d("adc reference adc raw voltage offset: %f", value);
}

SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), read_adc_reference, read_adc_reference, read_adc_reference);

int32_t get_adc_reference_raw_offset(void) {
    int32_t value = 0;
    device_ioctl(adc_reference_handle, CMD_GET_RAW_ADC_VALUE_OFFSET, &value);
    return value;
}

#ifdef __cplusplus
}
#endif

/* @} Robot_I2C_CORE */
/* @} Robot_DEVICES */
