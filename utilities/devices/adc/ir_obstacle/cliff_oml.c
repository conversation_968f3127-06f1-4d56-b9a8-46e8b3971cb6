/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:
 ** Author:         曾曼云
 ** Version:        V0.0.1
 ** Date:           2021-9-25
 ** Description:
 ** Others:
 ** Function List:
 ** History:        2021-09 曾曼云 创建
 ** <adce>          <author>    <version >    <desc>
 ** 2021-9-25       曾曼云	     0.0.1         创建文件
 ******************************************************************/

/*****************************************************************
 * 包含头文件
 ******************************************************************/
#include "devices.h"
#include "define.h"
#include "adc_core.h"
#include "tim_core.h"
#include "gpio_core.h"
#include <stddef.h>
#include <string.h>
#include "cmsis_os.h"
#include "hal.h"
#include "cliff_oml.h"
#include "delay.h"
#include "adc.h"
#include "mem_pool.h"
#define LOG_TAG "cliff_oml"
#include "log.h"
#ifdef USING_SEGGER
#include "SEGGER_RTT.h"
#endif
#include "shell.h"
#include <string.h>
#include "define_cliff.h"
/**
 * @addtogroup Robot_DEVICES
 * @{
 */

/**
 * @defgroup Robot_GPIO_CORE  - GPIO_CORE
 *
 * @brief  \n
 * \n
 * @{
 */

#ifdef __cplusplus
extern "C" {
#endif
uint8_t cliff_capture_Cnt = 0;
int32_t time1             = 0;
int32_t time2             = 0;
/*****************************************************************
 * 私有宏定义
 ******************************************************************/
#define QUEUE_MAX 5
/*****************************************************************
 * 私有结构体/共用体/枚举定义
 ******************************************************************/
typedef enum { CLIFF_NO, CLIFF_YES, CLIFF_YES_TO_NO } CLIFF_STATUS;
typedef StaticTask_t osStaticThreadDef_t;
struct cliff_oml_args {
    uint8_t            cliff_collect_flag;  //开始采集信号标记位
    struct list_struct adc_list;            //数据回调函数链表
    osSemaphoreId_t    cliff_sem_id;        //信号量ID
    osThreadId_t       cliff_oml_thread;
    uint64_t           cliff_timestamp;
};

struct cliff_adc_bus_node {
    struct list_struct node;
    struct ca_bus *    cliff_oml_bus;  //断崖的adc总线
    int                ret1;
    int                ret2;
    uint8_t            cliff_count;
    uint8_t            cliff_status;           //检测断崖状态 0：非断崖   1：断崖
    uint8_t            last_pub_cliff_status;  //上一次断崖发布状态 0：非断崖   1：断崖
    uint8_t            pub_cliff_status;       //发布状态 0：非断崖   1：断崖
    char               dev_name[16];           //设备名称
    uint8_t            adc_value_thres;        // cliff峰峰值状态更新阈值(0~255)
    uint16_t           adc_value_top_thres;    // cliff峰峰值状态更新阈值上限(0~65535)
    uint16_t           adc_value_low_thres;    // cliff峰峰值状态更新阈值下限(0~65535)
    int32_t            value;
    uint8_t            dirction_flag;           // 1:后采样点减前采样点， 0:前采样点减后采样点
    uint8_t            filter_type;             //滤波类型
    int32_t            cliff_value[QUEUE_MAX];  //缓存滤波数组
    uint8_t            queue;
    uint16_t           threshold;  //(0 ~65535)
    int64_t            v_diff;     //过滤数据
    uint8_t            cliff_cnt;
    uint8_t            realese_cnt;
    uint8_t            debounce_num;
};

/*****************************************************************
 * 全局变量定义
 ******************************************************************/
static char cliff_dev_name[16];  //断崖设备名称（调试使用）
static bool enable_ir_cliff = true;
/*****************************************************************
 * 私有全局变量定义
 ******************************************************************/
osThreadAttr_t cliff_oml_attributes     = {.name = "cliff_oml", .priority = (osPriority_t) osPriorityNormal, .stack_size = 256 * 4};
osThreadAttr_t cliff_oml_pub_attributes = {.name = "cliff_oml_pub", .priority = (osPriority_t) osPriorityNormal, .stack_size = 256 * 4};

const osMutexAttr_t mutex_cliff_oml_attr = {
    "cliff_oml_mutex",                      // human readable mutex name
    osMutexRecursive | osMutexPrioInherit,  // attr_bits
    NULL,                                   // memory for control block
    0U                                      // size for control block
};
osMutexId_t cliff_oml_mutex;
/*****************************************************************
 * 外部变量声明
 ******************************************************************/

/*****************************************************************
 * 私有函数原型声明
 ******************************************************************/

/*****************************************************************
 * 函数定义
 ******************************************************************/
int cmpfunc(const void *a, const void *b) {
    return (*(int *) a - *(int *) b);
}

int32_t cliff_oml_open(const struct ca_device *dev, int32_t flags) {
    return 0;
}

int32_t cliff_oml_close(const struct ca_device *dev) {
    if (NULL == dev) {
        return -1;
    }
    struct cliff_oml_args *p_cliff_args = NULL;
    p_cliff_args                        = (struct cliff_oml_args *) dev->device_args;
    osThreadSuspend(p_cliff_args->cliff_oml_thread);
    return 0;
}

/*****************************************************************/
/**
 * Function:       gpio_read
 * Description:
 * Calls:
 * Called By:
 * @param[in]
 * @param[out]
 * @return
 *  - 0 表示成功
 *  - -1 表示失败
 *
 * @author:
 * @date
 *
 * @par Modification History
 * @par Author:
 * @par Date:
 * @par Description:
 *
 * @see
 *
 */
/******************************************************************/

int32_t cliff_set_adc(const struct ca_device *dev, adc_sensor_obj_st *cliff_sensor_obj) {
    if (NULL == dev || NULL == cliff_sensor_obj) {
        return -1;
    }
    struct bus_info *          adc_bus    = NULL;
    struct ca_bus *            bus        = NULL;
    struct cliff_adc_bus_node *p_callback = NULL;
    struct cliff_oml_args *    cliff_attr = (struct cliff_oml_args *) dev->device_args;
    p_callback                            = mem_block_alloc(sizeof(struct cliff_adc_bus_node));
    memset(p_callback, 0, sizeof(struct cliff_adc_bus_node));
    adc_bus = cliff_sensor_obj->bus_com;
    if (NULL == p_callback) {
        return -1;
    }
    if (bus_find_name(adc_bus, &bus)) {
        return -1;
    }
    p_callback->cliff_oml_bus       = bus;
    p_callback->adc_value_thres     = cliff_sensor_obj->adc_value_thres;
    p_callback->adc_value_top_thres = cliff_sensor_obj->adc_value_top_thres;
    p_callback->adc_value_low_thres = cliff_sensor_obj->adc_value_low_thres;
    p_callback->dirction_flag       = cliff_sensor_obj->dirction_flag;
    p_callback->filter_type         = cliff_sensor_obj->filter_type;
    p_callback->debounce_num        = cliff_sensor_obj->debounce_num;
    if (!p_callback->debounce_num) {
        p_callback->debounce_num = 1;
    }
    memcpy(p_callback->cliff_oml_bus, bus, sizeof(struct ca_bus));
    memcpy(p_callback->dev_name, cliff_sensor_obj->dev_name, strlen((char *) cliff_sensor_obj->dev_name));
    list_add_tail(&p_callback->node, &cliff_attr->adc_list);
    return 0;
}

int32_t cliff_get_adc(const struct ca_device *dev, adc_sensor_obj_st *cliff_sensor_obj) {
    if (NULL == dev || NULL == cliff_sensor_obj) {
        return -1;
    }

    if (NULL == cliff_sensor_obj->bus_com) {
        return -1;
    }

    struct list_struct *       p_list     = NULL;
    struct cliff_adc_bus_node *p_callback = NULL;
    struct cliff_oml_args *    cliff_args = (struct cliff_oml_args *) dev->device_args;

    p_list = &cliff_args->adc_list;
    while (list_is_last(p_list, &cliff_args->adc_list) != 1) {
        p_callback = (struct cliff_adc_bus_node *) p_list->next;
        if (NULL != p_callback->cliff_oml_bus) {
            if (0 == strcmp((char *) p_callback->dev_name, (char *) cliff_sensor_obj->dev_name)) {
                return p_callback->value;
            }
        }
        p_list = p_list->next;
    }

    return -1;
}

int32_t cliff_get_state(const struct ca_device *dev, adc_sensor_obj_st *cliff_sensor_obj) {
    if (NULL == dev || NULL == cliff_sensor_obj) {
        return -1;
    }

    if (NULL == cliff_sensor_obj->bus_com) {
        return -1;
    }

    struct list_struct *       p_list     = NULL;
    struct cliff_adc_bus_node *p_callback = NULL;
    struct cliff_oml_args *    cliff_args = (struct cliff_oml_args *) dev->device_args;

    p_list = &cliff_args->adc_list;
    while (list_is_last(p_list, &cliff_args->adc_list) != 1) {
        p_callback = (struct cliff_adc_bus_node *) p_list->next;
        if (NULL != p_callback->cliff_oml_bus) {
            if (0 == strcmp((char *) p_callback->dev_name, (char *) cliff_sensor_obj->dev_name)) {
                return p_callback->cliff_status;
            }
        }
        p_list = p_list->next;
    }

    return -1;
}

int32_t cliff_get_status(const struct ca_device *dev) {
    if (NULL == dev) {
        return -1;
    }
    struct cliff_oml_args *    cliff_args = NULL;
    struct list_struct *       p_list     = NULL;
    struct cliff_adc_bus_node *p_callback = NULL;
    cliff_args                            = (struct cliff_oml_args *) dev->device_args;

    p_list = &cliff_args->adc_list;
    int i  = 0;
    while (list_is_last(p_list, &cliff_args->adc_list) != 1) {
        p_callback = (struct cliff_adc_bus_node *) p_list->next;
        if (NULL != p_callback->cliff_oml_bus && (0 == strncmp((char *) p_callback->dev_name, "cliff", 5))) {
            i |= p_callback->cliff_status;
        }
        p_list = p_list->next;
    }
    return i;
}

int32_t cliff_oml_read(const struct ca_device *dev, void *buffer, uint32_t len) {
    int32_t ret    = -1;
    uint8_t status = 0;
    if ((NULL == dev) || (NULL == dev->bus) || (NULL == dev->bus->handler) || (NULL == buffer)) {
        return -1;
    }
    status = cliff_get_status(dev);
    // buffer为1当前有断崖   为0当前无断崖
    memcpy(buffer, (void *) &status, sizeof(uint8_t));

    return ret;
}

/*****************************************************************/
/**
 * Function:       gpio_write
 * Description:
 * Calls:
 * Called By:
 * @param[in]
 * @param[out]
 * @return
 *  - 0 表示成功
 *  - -1 表示失败
 *
 * @author:
 * @date
 *
 * @par Modification History
 * @par Author:
 * @par Date:
 * @par Description:
 *
 * @see
 *
 */
/******************************************************************/
int32_t cliff_oml_write(const struct ca_device *dev, void *buffer, uint32_t len) {
    int32_t ret = -1;

    if ((NULL == dev) || (NULL == dev->bus) || (NULL == dev->bus->handler) || (NULL == buffer)) {
        return -1;
    }

    return ret;
}

bool check_ir_cliff(struct cliff_adc_bus_node *p_callback) {
    int      cliff_buff[CLIFF_TYPE_CHECK_BUFF_SIZE] = {0};
    int      min_avg                                = 0;
    int      max_avg                                = 0;
    int      temp                                   = 0;
    int      buffer_max_size                        = CLIFF_TYPE_CHECK_BUFF_SIZE;
    int      buffer_half_size                       = 0;
    uint16_t adc_res                                = 0;

    if (CLIFF_TYPE_CHECK_BUFF_SIZE % 2 != 0) {
        buffer_max_size = CLIFF_TYPE_CHECK_BUFF_SIZE - 1;
    }

    buffer_half_size = buffer_max_size / 2;

    for (int i = 0; i < buffer_max_size;) {
        // todo: 添加重试次数限制
        if (adc_read(p_callback->cliff_oml_bus, p_callback->cliff_oml_bus->bus_addr, &adc_res) == 0) {
            cliff_buff[i] = adc_res;
            i++;
        }

        osDelay(1);
    }
    qsort(cliff_buff, buffer_max_size, sizeof(int), cmpfunc);

    // 求最小值平均
    for (int i = 0; i < buffer_half_size; i++) {
        temp += cliff_buff[i];
    }
    min_avg = temp / buffer_half_size;
    temp    = 0;

    // 求最大值平均
    for (int i = buffer_half_size; i < buffer_max_size; i++) {
        temp += cliff_buff[i];
    }
    max_avg = temp / buffer_half_size;
    temp    = 0;

    log_i("check ir cliff type, max_avg:%d,min_avg:%d,max_avg - min_avg:%d", max_avg, min_avg, max_avg - min_avg);

    if (max_avg - min_avg >= CLIFF_IR_LOWER_THRES) {
        return true;
    } else {
        return false;
    }
}

bool is_ir_cliff(const struct ca_device *dev) {
    struct cliff_oml_args *    cliff_args = (struct cliff_oml_args *) dev->device_args;
    struct list_struct *       p_list     = &cliff_args->adc_list;
    struct cliff_adc_bus_node *p_callback = (struct cliff_adc_bus_node *) p_list->next;
    bool                       is_ir      = false;

    osThreadSuspend(cliff_args->cliff_oml_thread);

    // 判断断崖类型
    while (strstr(p_callback->dev_name, "cliff") == NULL) {
        p_callback = (struct cliff_adc_bus_node *) p_list->next;
        p_list     = p_list->next;
    }

    if (p_callback->cliff_oml_bus) {
        is_ir = check_ir_cliff(p_callback);
    } else {
        log_e("cliff_oml_bus error!");
    }

    osThreadResume(cliff_args->cliff_oml_thread);

    return is_ir;
}

int32_t cliff_oml_ioctl(const struct ca_device *dev, uint32_t cmd, void *arg) {
    int32_t ret = -1;
    if (NULL == dev) {
        return -1;
    }

    switch (cmd) {
        case CLIFF_CMD_SET_DATA:
            cliff_set_adc(dev, (adc_sensor_obj_st *) arg);
            break;
        case CLIFF_CMD_GET_VALUE:
            return cliff_get_adc(dev, (adc_sensor_obj_st *) arg);
            break;
        case CLIFF_CMD_GET_STATE:
            return cliff_get_state(dev, (adc_sensor_obj_st *) arg);
            break;
        case CLIFF_CMD_IS_TYPE:
            return is_ir_cliff(dev);
            break;
        case CLIFF_CMD_DISABLE:
            log_i("disable ir cliff");
            enable_ir_cliff = false;
            break;
        case CLIFF_CMD_ENABLE:
            log_i("enable ir cliff");
            enable_ir_cliff = true;
            break;
        default:
            break;
    }

    return 0;
}

void cliff_oml_oc_delay_elapsed_callback(const struct ca_device *dev, void *handler) {
    struct ca_bus *        cliff_oml_adc = NULL;
    struct cliff_oml_args *cliff_args    = NULL;

    struct list_struct *       p_list     = NULL;
    struct cliff_adc_bus_node *p_callback = NULL;
    cliff_args                            = (struct cliff_oml_args *) dev->device_args;
    time1                                 = Get_sys_time_us();
    uint16_t adc_res                      = 0;
    switch (cliff_capture_Cnt) {
        case 0:
            p_list = &cliff_args->adc_list;
            while (list_is_last(p_list, &cliff_args->adc_list) != 1) {
                p_callback = (struct cliff_adc_bus_node *) p_list->next;
                if (NULL != p_callback->cliff_oml_bus && (enable_ir_cliff || !strstr(p_callback->dev_name, "cliff"))) {
                    adc_res = 0;
                    adc_read(p_callback->cliff_oml_bus, p_callback->cliff_oml_bus->bus_addr, &adc_res);
                    p_callback->ret1 = adc_res;
                }
                p_list = p_list->next;
            }
            cliff_capture_Cnt++;
            tim_set_compare(dev->bus, 1800);
            break;
        case 1:
            p_list = &cliff_args->adc_list;
            while (list_is_last(p_list, &cliff_args->adc_list) != 1) {
                p_callback = (struct cliff_adc_bus_node *) p_list->next;
                if (NULL != p_callback->cliff_oml_bus && (enable_ir_cliff || !strstr(p_callback->dev_name, "cliff"))) {
                    adc_res = 0;
                    adc_read(p_callback->cliff_oml_bus, p_callback->cliff_oml_bus->bus_addr, &adc_res);
                    p_callback->ret2 = adc_res;
                }
                p_list = p_list->next;
            }
            cliff_capture_Cnt = 0;
            tim_set_compare(dev->bus, 800);
            osSemaphoreRelease(cliff_args->cliff_sem_id);
            break;
    }
    time2 = Get_sys_time_us();
}

void cliff_oml_run(void *argument) {
    struct ca_bus *        cliff_oml_adc = NULL;
    struct ca_device *     dev           = NULL;
    struct cliff_oml_args *cliff_args    = NULL;

    struct list_struct *       p_list     = NULL;
    struct cliff_adc_bus_node *p_callback = NULL;
    double                     v_diff     = 0;

    dev        = (struct ca_device *) argument;
    cliff_args = (struct cliff_oml_args *) dev->device_args;
    while (1) {
        if (osSemaphoreAcquire(cliff_args->cliff_sem_id, 20) == osOK) {
            p_list = &cliff_args->adc_list;
            while (list_is_last(p_list, &cliff_args->adc_list) != 1) {
                p_callback = (struct cliff_adc_bus_node *) p_list->next;
                if (NULL != p_callback->cliff_oml_bus) {
                    // 过滤掉0值,对于接地的GND信号无法处理
                    if (p_callback->ret2 <= 0 || p_callback->ret1 <= 0) {
                        p_list = p_list->next;
                        continue;
                    }
                    if (p_callback->dirction_flag == PWM_ADC_DIRETION_FRONT_REDUCE_AFTER) {
                        p_callback->v_diff = (p_callback->ret1 - p_callback->ret2) * 3.3f / 4096;
                        p_callback->value  = (p_callback->ret1 - p_callback->ret2) > 0 ? (p_callback->ret1 - p_callback->ret2) : 0;
                    } else {
                        p_callback->v_diff = (p_callback->ret2 - p_callback->ret1) * 3.3f / 4096;
                        p_callback->value  = (p_callback->ret2 - p_callback->ret1) > 0 ? (p_callback->ret2 - p_callback->ret1) : 0;
                    }

                    if (p_callback->value >= 0) {
                        if (strncmp(p_callback->dev_name, cliff_dev_name, strlen(cliff_dev_name)) == 0) {
                            log_d("%s>>>>>>> behind[%d] front[%d]", p_callback->dev_name, p_callback->ret2, p_callback->ret1);
                        }
                        if (p_callback->filter_type == LIMIT_ADC_TRANS_THRESHOLD) {
                            if (p_callback->v_diff >= p_callback->adc_value_thres) {
                                p_callback->cliff_status = 0;  //未检测到断崖
                                p_callback->cliff_count  = 0;
                            } else {
                                p_callback->cliff_count++;
                                if (p_callback->cliff_count > DEBOUNCING_LEVEL) {  //防抖
                                    p_callback->cliff_status = 1;                  //检测到断崖
                                }
                            }
                        } else if (p_callback->filter_type == LIMIT_ADC_TRANS_SCHMIDT) {
                            // 当边刷干扰时，采集到的两个点都大于3000,需要抛弃
                            if (p_callback->ret1 > 3000 && p_callback->ret2 > 3000) {
                                p_list = p_list->next;
                                continue;
                            }

                            if (strncmp(p_callback->dev_name, cliff_dev_name, strlen(cliff_dev_name)) == 0) {
                                log_d("%s,%d,%d,%d", p_callback->dev_name, p_callback->value, p_callback->cliff_cnt, p_callback->cliff_status);
                            }

                            if (p_callback->value <= p_callback->adc_value_low_thres) {
                                p_callback->realese_cnt = 0;
                                p_callback->cliff_cnt++;
                            } else {
                                p_callback->cliff_cnt = 0;
                                p_callback->realese_cnt++;
                            }

                            if (p_callback->cliff_cnt >= p_callback->debounce_num) {
                                p_callback->cliff_cnt    = p_callback->debounce_num;
                                p_callback->cliff_status = 1;
                            } else if (p_callback->realese_cnt >= p_callback->debounce_num) {
                                p_callback->realese_cnt  = p_callback->debounce_num;
                                p_callback->cliff_status = 0;
                            }
                        }
                    }
                    p_callback->v_diff = 0;
                }
                p_list = p_list->next;
            }
        }
    }
}

void choose_cliff(const char *cliff) {
    memset(cliff_dev_name, 0, strlen(cliff_dev_name));
    strncpy(cliff_dev_name, cliff, strlen(cliff));
    log_i("You choose cliff_dev is %s", cliff_dev_name);
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), choose_cliff, choose_cliff, choose_cliff);

int32_t cliff_oml_driver_init(struct ca_device *dev) {
    if (NULL == dev) {
        return -1;
    }

    memset(&dev->ops, 0, sizeof(struct ca_device_ops));

    dev->ops.open  = cliff_oml_open;
    dev->ops.close = cliff_oml_close;
    dev->ops.read  = cliff_oml_read;
    dev->ops.write = cliff_oml_write;
    dev->ops.ioctl = cliff_oml_ioctl;

    struct ca_bus *bus = NULL;
    if (bus_find_name(dev->device_args, &bus)) {
        return -1;
    }
    tim_pwm_start(bus, 0.5, 0);

    struct cliff_oml_args *cliff_oml_args = NULL;
    cliff_oml_args                        = (struct cliff_oml_args *) mem_block_alloc(sizeof(struct cliff_oml_args));
    memset(cliff_oml_args, 0, sizeof(struct cliff_oml_args));

    // cliff_oml_args->cliff_oml_bus = bus;
    list_head_init(&cliff_oml_args->adc_list);
    cliff_oml_args->cliff_sem_id = osSemaphoreNew(1, 0U, NULL);
    dev->device_args             = (void *) cliff_oml_args;
    tim_oc_start_it(dev->bus);
    tim_oc_delay_elapsed_register_callback(dev, dev->bus->handler, dev->bus->bus_addr, cliff_oml_oc_delay_elapsed_callback);
    cliff_oml_args->cliff_oml_thread = osThreadNew(cliff_oml_run, (void *) dev, &cliff_oml_attributes);
    return 0;
}

DEVICE_DRIVER_INIT(cliff_oml, tim, cliff_oml_driver_init);

#ifdef __cplusplus
}
#endif

/* @} Robot_I2C_CORE */
/* @} Robot_DEVICES */
