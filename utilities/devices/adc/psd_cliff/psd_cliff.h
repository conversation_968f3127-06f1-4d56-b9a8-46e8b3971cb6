/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:
 ** Author:         陈寿义
 ** Version:        V0.0.1
 ** Date:           2023-2-1
 ** Description:
 ** Others:
 ** Function List:
 ** History:        2023-02 陈寿义 创建
 ** <adc>          <author>    <version >    <desc>
 ** 2023-2-1       陈寿义    0.0.1         创建文件
 ******************************************************************/

#ifndef _psd_cliff_H
#define _psd_cliff_H

/*****************************************************************
 * 包含头文件
 ******************************************************************/
#include "main.h"
#include "devices.h"

/*****************************************************************
 * 宏定义
 ******************************************************************/

/**
 * @ingroup Robot_GPIO_CORE
 *
 * @brief \n
 * \n
 * @{
 */

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 宏定义
 ******************************************************************/
#define CLIFF_TYPE_CHECK_BUFF_SIZE 100
#define CLIFF_TOF_LOWER_THRES      500  // ADC最高值减最低值阈值小于500为TOF

#define PSD_DEFAULT_CLIFF_VOL 0.55  // 默认断崖触发电压
/*****************************************************************
 * 结构定义
 ******************************************************************/
/*****************************************************************
 * 全局变量声明
 ******************************************************************/

/*****************************************************************
 * 函数原型声明
 ******************************************************************/
int32_t psd_cliff_read(const struct ca_device *dev, void *buffer, uint32_t len);
int32_t psd_cliff_write(const struct ca_device *dev, void *buffer, uint32_t len);
int32_t psd_cliff_ioctl(const struct ca_device *dev, uint32_t cmd, void *arg);

int32_t psd_cliff_driver_init(struct ca_device *dev);
/*****************************************************************
 * 函数说明
 ******************************************************************/

#ifdef __cplusplus
}
#endif

/* @} Robot_I2C_CORE */

#endif
