/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:
 ** Author:         huangjianxian
 ** Version:        V0.0.1
 ** Date:           2025-05-07
 ** Description:    AW9523 I2C IO Expander chip driver.
 ** Others:
 ** Function List:
 ** History:        2025-05-07    huangjianxian    0.0.1        创建文件
 ******************************************************************/

#include "aw9523.h"
#include "i2c_core.h"
#include <stddef.h>
#include <string.h>
#include "log.h"
#include "pal_log.h"

#undef LOG_TAG
#define LOG_TAG "AW9523"

#define DATA_LEN(data) sizeof(data) / sizeof(uint8_t)

#ifdef __cplusplus
extern "C" {
#endif

// Delay Func
static void User_Delay(uint32_t time_ms) {
    if (osKernelGetState() == osKernelRunning) {
        osDelay(time_ms);
    } else {
        HAL_Delay(time_ms);
    }
}

static int32_t aw9523_write_reg(const struct ca_device *dev, uint8_t reg, uint8_t value) {
    if (dev == NULL || dev->bus == NULL) {
        log_e("Device or bus is NULL");
        return -1;
    }
    if (i2c_write_one_byte(dev->bus, reg, &value) != 0) {
        log_e("AW9523 write reg 0x%02X failed", reg);
        i2c_reinit(dev->bus);
        return -1;
    }
    return 0;
}

static int32_t aw9523_read_reg(const struct ca_device *dev, uint8_t reg, uint8_t *value) {
    if (dev == NULL || dev->bus == NULL || value == NULL) {
        log_e("Device, bus or value pointer is NULL");
        return -1;
    }
    if (i2c_read_one_byte(dev->bus, reg, value) != 0) {
        log_e("AW9523 read reg 0x%02X failed", reg);
        i2c_reinit(dev->bus);
        return -1;
    }
    return 0;
}

static int32_t aw9523_software_reset(const struct ca_device *dev) {
    return aw9523_write_reg(dev, AW9523_REG_SOFT_RESET, 0x00);
}

static int32_t aw9523_set_pin_direction(const struct ca_device *dev, uint8_t pin_num, uint8_t direction) {
    uint8_t reg_addr;
    uint8_t current_config;
    uint8_t bit_pos;

    if (pin_num > 15) {
        log_e("Invalid pin number: %d", pin_num);
        return -1;
    }

    if (pin_num < 8) {  // P0.0 - P0.7
        reg_addr = AW9523_REG_CONFIG_P0;
        bit_pos  = pin_num;
    } else {  // P1.0 - P1.7
        reg_addr = AW9523_REG_CONFIG_P1;
        bit_pos  = pin_num - 8;
    }

    if (aw9523_read_reg(dev, reg_addr, &current_config) != 0) {
        return -1;
    }

    if (direction == AW9523_IO_INPUT) {
        current_config |= (1 << bit_pos);
    } else {
        current_config &= ~(1 << bit_pos);
    }

    return aw9523_write_reg(dev, reg_addr, current_config);
}

static int32_t aw9523_get_pin_level(const struct ca_device *dev, uint8_t pin_num, uint8_t *level) {
    uint8_t reg_addr;
    uint8_t port_values;
    uint8_t bit_pos;

    if (pin_num > 15 || level == NULL) {
        log_e("Invalid pin number or level pointer is NULL");
        return -1;
    }

    if (pin_num < 8) {  // P0.0 - P0.7
        reg_addr = AW9523_REG_INPUT_P0;
        bit_pos  = pin_num;
    } else {  // P1.0 - P1.7
        reg_addr = AW9523_REG_INPUT_P1;
        bit_pos  = pin_num - 8;
    }

    if (aw9523_read_reg(dev, reg_addr, &port_values) != 0) {
        return -1;
    }

    *level = (port_values >> bit_pos) & 0x01;
    return 0;
}

static int32_t aw9523_set_pin_level(const struct ca_device *dev, uint8_t pin_num, uint8_t level) {
    uint8_t reg_addr;
    uint8_t current_output;
    uint8_t bit_pos;

    if (pin_num > 15) {
        log_e("Invalid pin number: %d", pin_num);
        return -1;
    }

    if (pin_num < 8) {  // P0.0 - P0.7
        reg_addr = AW9523_REG_OUTPUT_P0;
        bit_pos  = pin_num;
    } else {  // P1.0 - P1.7
        reg_addr = AW9523_REG_OUTPUT_P1;
        bit_pos  = pin_num - 8;
    }

    if (aw9523_read_reg(dev, reg_addr, &current_output) != 0) {
        return -1;
    }

    if (level == AW9523_IO_HIGH) {
        current_output |= (1 << bit_pos);
    } else {
        current_output &= ~(1 << bit_pos);
    }

    return aw9523_write_reg(dev, reg_addr, current_output);
}

int32_t aw9523_open(const struct ca_device *dev, int32_t flags) {
    return 0;
}

int32_t aw9523_close(const struct ca_device *dev) {
    return 0;
}

int32_t aw9523_read(const struct ca_device *dev, void *buffer, uint32_t size) {
    log_w("aw9523_read is not implemented, use ioctl");
    return -1;
}

int32_t aw9523_write(const struct ca_device *dev, void *buffer, uint32_t size) {
    log_w("aw9523_write is not implemented, use ioctl");
    return -1;
}

int32_t aw9523_ioctl(const struct ca_device *dev, uint32_t cmd, void *arg) {
    int32_t          ret = 0;
    aw9523_pin_op_t *pin_op;

    if (NULL == dev) {
        log_e("Device is NULL");
        return -1;
    }

    switch (cmd) {
        case AW9523_CMD_SOFT_RESET:
            ret = aw9523_software_reset(dev);
            break;
        case AW9523_CMD_SET_PIN_DIR:
            if (arg == NULL) {
                log_e("Argument for AW9523_CMD_SET_PIN_DIR is NULL");
                return -1;
            }
            pin_op = (aw9523_pin_op_t *) arg;
            ret    = aw9523_set_pin_direction(dev, pin_op->pin_num, pin_op->direction);
            break;
        case AW9523_CMD_GET_PIN_LEVEL:
            if (arg == NULL) {
                log_e("Argument for AW9523_CMD_GET_PIN_LEVEL is NULL");
                return -1;
            }
            pin_op = (aw9523_pin_op_t *) arg;
            ret    = aw9523_get_pin_level(dev, pin_op->pin_num, &pin_op->level);
            break;
        case AW9523_CMD_SET_PIN_LEVEL:
            if (arg == NULL) {
                log_e("Argument for AW9523_CMD_SET_PIN_LEVEL is NULL");
                return -1;
            }
            pin_op = (aw9523_pin_op_t *) arg;
            ret    = aw9523_set_pin_level(dev, pin_op->pin_num, pin_op->level);
            break;
        default:
            log_w("Unknown IOCTL command: %u", cmd);
            ret = -1;
            break;
    }
    return ret;
}

int32_t aw9523_init(struct ca_device *dev) {
    uint8_t chip_id = 0;

    if (NULL == dev || NULL == dev->bus) {
        log_e("Device or bus is NULL for AW9523 init");
        return -1;
    }

    // Set the I2C slave address for the bus object
    // The bus_addr in ca_bus is the 7-bit address
    dev->bus->bus_addr = AW9523_I2C_ADDR;

    memset(&dev->ops, 0, sizeof(struct ca_device_ops));
    dev->ops.open  = aw9523_open;
    dev->ops.close = aw9523_close;
    dev->ops.read  = aw9523_read;
    dev->ops.write = aw9523_write;
    dev->ops.ioctl = aw9523_ioctl;

    if (aw9523_software_reset(dev) != 0) {
        log_e("AW9523 software reset failed");
        return -1;
    }

    User_Delay(5);

    if (aw9523_read_reg(dev, AW9523_REG_ID, &chip_id) != 0) {
        log_e("Failed to read AW9523 Chip ID");
        return -1;
    }
    if (chip_id != 0x23) {  // AW9523B ID is 0x23
        log_e("AW9523 Chip ID mismatch: expected 0x23, got 0x%02X", chip_id);
        return -1;
    }
    log_i("AW9523 Chip ID: 0x%02X", chip_id);

    // Configure Global Control Register (GCR) - Enable chip
    // Bit 0 (Chip_EN): 1 = enable, 0 = disable. Default is 0x00 (disabled).
    // For AW9523B, GCR (0x11) bit0 is CHIP_EN. Set to 1 to enable.
    // Other bits are for IMax, keep them default (0) for GPIO mode.
    if (aw9523_write_reg(dev, AW9523_REG_GCR, 0x01) != 0) {
        log_e("AW9523 GCR write failed (enable chip)");
        return -1;
    }

    // Configure Port LED Mode Select Registers (P0_LED_MODE, P1_LED_MODE) to GPIO mode
    // For GPIO mode, all bits should be 1.
    // Register 0x12 (P0_LED_MODE): Set to 0xFF for P0.0-P0.7 as GPIO
    // Register 0x13 (P1_LED_MODE): Set to 0xFF for P1.0-P1.7 as GPIO
    if (aw9523_write_reg(dev, AW9523_REG_LED_MODE_SWITCH0, 0xFF) != 0) {
        log_e("AW9523 P0_LED_MODE write failed");
        return -1;
    }
    if (aw9523_write_reg(dev, AW9523_REG_LED_MODE_SWITCH1, 0xFF) != 0) {
        log_e("AW9523 P1_LED_MODE write failed");
        return -1;
    }

    // Default IO directions are output after reset. User can reconfigure as needed via ioctl.
    log_i("AW9523 initialized successfully, all pins in GPIO output mode by default.");

    return 0;
}

int32_t aw9523_deinit(struct ca_device *dev) {
    if (dev != NULL) {
        aw9523_software_reset(dev);
    }
    log_i("AW9523 de-initialized.");
    return 0;
}

DEVICE_DRIVER_INIT(aw9523, i2c, aw9523_init);

#ifdef __cplusplus
}
#endif
