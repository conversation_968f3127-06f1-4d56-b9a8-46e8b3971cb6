/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:
 ** Author:         huangjianxian
 ** Version:        V0.0.1
 ** Date:           2025-05-07
 ** Description:    AW9523 I2C IO Expander chip driver header.
 ** Others:
 ** Function List:
 ** History:        2025-05-07    huangjianxian    0.0.1        创建文件
 ******************************************************************/

#ifndef _AW9523_H
#define _AW9523_H

#include "main.h"
#include "devices.h"
#include "define.h"
#include "define_aw9523.h"

#ifdef __cplusplus
extern "C" {
#endif

int32_t aw9523_open(const struct ca_device *dev, int32_t flags);
int32_t aw9523_close(const struct ca_device *dev);
int32_t aw9523_read(const struct ca_device *dev, void *buffer, uint32_t size);
int32_t aw9523_write(const struct ca_device *dev, void *buffer, uint32_t size);
int32_t aw9523_ioctl(const struct ca_device *dev, uint32_t cmd, void *arg);
int32_t aw9523_init(struct ca_device *dev);
int32_t aw9523_deinit(struct ca_device *dev);

#ifdef __cplusplus
}
#endif

#endif /* _AW9523_H */
