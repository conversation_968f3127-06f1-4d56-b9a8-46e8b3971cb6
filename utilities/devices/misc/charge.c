#include <stdint.h>
#include <stddef.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>
#include "delay.h"
#include "devices.h"
#include "charge.h"
#include "mem_pool.h"
#include "shell.h"
#include "gpio_core.h"
#include "adc_core.h"
#include "tim_core.h"
#include "dac_core.h"
#include "define.h"
#include "define_charge.h"
#include "define_button.h"
#ifdef USING_SEGGER
#include "SEGGER_RTT.h"
#endif
#define LOG_TAG "charge"
#include "log.h"
#include "digital_filter.h"

struct charge_args {
    osThreadId_t       task_bt_Handle;                    //任务处理线程
    charge_bus_t       charge_bus;                        //充电驱动依赖的总线
    osSemaphoreId_t    charge_semphore;                   //信号量
    osMutexId_t        charge_mutex;                      //互斥锁
    struct list_struct charge_list;                       //按键链表头
    osThreadId_t       charge_thread;                     //处理线程id
    int32_t            ac_fail_det_value;                 // AC输入检测值
    int32_t            hw_port_det_value;                 //输出硬件保护检测值
    int32_t            elec_sec_det_value;                //市电检测值
    int32_t            relay_value;                       //继电器输出值
    int32_t            power_chip_value;                  //主芯片输出值
    float              adc_pw_v_mult;                     //主功率电压采集系数
    float              adc_bat_i_mult;                    //主功率电流采样（电池电流采样）系数
    float              adc_bat_v_mult;                    //主功率电压采样（电池电流采样）系数
    float              adc_pw_temp_mult;                  //主功率温度采样系数
    int32_t (*adc_reference_raw_offset_value_get)(void);  //获取原始adc校准偏差值函数
    int32_t adc_bat_v_value;                              // adc电池电压值
    int32_t adc_bat_i_value;                              // adc电池电流值
    int32_t adc_pw_temp_value;                            // adc充电器温度值
    int32_t adc_pw_v_value;                               // adc充电器输出值
    int32_t adc_reference_offset_value;                   // adc参考电压值
};

/*****************************************************************
 * 全局变量定义
 ******************************************************************/
const osMutexAttr_t mutex_charge_attr = {
    "charge_mutex",                         // human readable mutex name
    osMutexRecursive | osMutexPrioInherit,  // attr_bits
    NULL,                                   // memory for control block
    0U                                      // size for control block
};

osThreadAttr_t charge_thread_attributes = {.name = "charge_thread", .priority = (osPriority_t) osPriorityNormal, .stack_size = 384 * 4};
/*****************************************************************
 * 私有结构体/共用体/枚举定义
 ******************************************************************/
static BUTTON_ATTACH_ATTR_T ac_fail_attach_attr  = {0};
static BUTTON_ATTR_T        ac_fail_attr         = {0};
static BUTTON_ATTACH_ATTR_T hw_prot_attach_attr  = {0};
static BUTTON_ATTR_T        hw_prot_attr         = {0};
static BUTTON_ATTACH_ATTR_T elec_sec_attach_attr = {0};
static BUTTON_ATTR_T        elec_sec_attr        = {0};
static struct charge_args * p_charge_args        = NULL;

/*****************************************************************
 * 外部变量声明（如果全局变量没有在其它的H文件声明，引用时需在此处声明，
 *如果已在其它H文件声明，则只需包含此H文件即可）
 ******************************************************************/
extern int button_handle;

/*****************************************************************
 * 私有函数原型声明
 ******************************************************************/
int charge_ac_fail_det_callback(uint8_t index, BUTTON_STATUS_TYPE type) {
    switch (type) {
        case BUTTON_PRESS_DOWN:
        case BUTTON_LONG_PRESS_START:
            p_charge_args->ac_fail_det_value = 1;
            log_i("ac fail press");
            break;
        case BUTTON_PRESS_UP:
            p_charge_args->ac_fail_det_value = 0;
            log_i("ac fail up");
            break;
        default:
            break;
    }
    return 0;
}

int charge_hw_prot_det_callback(uint8_t index, BUTTON_STATUS_TYPE type) {
    switch (type) {
        case BUTTON_PRESS_DOWN:
        case BUTTON_LONG_PRESS_START:
            p_charge_args->hw_port_det_value = 1;
            log_i("hw prot press");
            break;
        case BUTTON_PRESS_UP:
            p_charge_args->hw_port_det_value = 0;
            log_i("hw prot up");
            break;
        default:
            break;
    }
    return 0;
}

int charge_elec_sec_det_callback(uint8_t index, BUTTON_STATUS_TYPE type) {
    switch (type) {
        case BUTTON_PRESS_DOWN:
        case BUTTON_LONG_PRESS_START:
            p_charge_args->elec_sec_det_value = 1;
            log_i("elec sec press");
            break;
        case BUTTON_PRESS_UP:
            p_charge_args->elec_sec_det_value = 0;
            log_i("elec sec up");
            break;
        default:
            break;
    }
    return 0;
}

int charge_input_det_attr_init(const struct ca_device *dev) {
    charge_bus_info_t *p_bus_info    = NULL;
    charge_attr_t *    p_charge_attr = NULL;

    if ((NULL == dev) || (NULL == dev->bus) || (NULL == dev->device_args)) {
        return -1;
    }

    p_charge_attr = (charge_attr_t *) dev->device_args;
    p_bus_info    = p_charge_attr->charge_bus_info;
    if (NULL == p_bus_info) {
        return -1;
    }

    ac_fail_attr.gpio_info         = &p_bus_info->gpio_ac_fail_det;
    ac_fail_attr.index             = 0;
    ac_fail_attr.trigger_condition = GPIO_TRIGGER_LOW;
    ac_fail_attr.period            = 1000;

    ac_fail_attr.long_press_hold_type                  = BUTTON_LONG_PRESS_HOLD_SINGLE_TRIGGER;
    ac_fail_attach_attr.gpio_info                      = &p_bus_info->gpio_ac_fail_det;
    ac_fail_attach_attr.attach.button_press_up         = 1;
    ac_fail_attach_attr.attach.button_long_press_start = 1;
    ac_fail_attach_attr.button_callback                = charge_ac_fail_det_callback;

    device_ioctl(button_handle, BUTTON_CMD_SET_ADD_BUTTON, (void *) &ac_fail_attr);
    device_ioctl(button_handle, BUTTON_CMD_SET_ADD_ATTACH, (void *) &ac_fail_attach_attr);

    hw_prot_attr.gpio_info            = &p_bus_info->gpio_hw_protect_det;
    hw_prot_attr.index                = 1;
    hw_prot_attr.trigger_condition    = GPIO_TRIGGER_LOW;
    hw_prot_attr.period               = 1000;
    hw_prot_attr.long_press_hold_type = BUTTON_LONG_PRESS_HOLD_SINGLE_TRIGGER;

    hw_prot_attach_attr.gpio_info                      = &p_bus_info->gpio_hw_protect_det;
    hw_prot_attach_attr.attach.button_press_up         = 1;
    hw_prot_attach_attr.attach.button_long_press_start = 1;
    hw_prot_attach_attr.button_callback                = charge_hw_prot_det_callback;

    device_ioctl(button_handle, BUTTON_CMD_SET_ADD_BUTTON, (void *) &hw_prot_attr);
    device_ioctl(button_handle, BUTTON_CMD_SET_ADD_ATTACH, (void *) &hw_prot_attach_attr);

    elec_sec_attr.gpio_info            = &p_bus_info->gpio_elec_sec_det;
    elec_sec_attr.index                = 2;
    elec_sec_attr.trigger_condition    = GPIO_TRIGGER_LOW;
    elec_sec_attr.period               = 1000;
    elec_sec_attr.long_press_hold_type = BUTTON_LONG_PRESS_HOLD_SINGLE_TRIGGER;

    elec_sec_attach_attr.gpio_info                      = &p_bus_info->gpio_elec_sec_det;
    elec_sec_attach_attr.attach.button_press_up         = 1;
    elec_sec_attach_attr.attach.button_long_press_start = 1;
    elec_sec_attach_attr.button_callback                = charge_elec_sec_det_callback;

    device_ioctl(button_handle, BUTTON_CMD_SET_ADD_BUTTON, (void *) &elec_sec_attr);
    device_ioctl(button_handle, BUTTON_CMD_SET_ADD_ATTACH, (void *) &elec_sec_attach_attr);

    return 0;
}

int32_t charge_set_power_voltage(const struct ca_device *dev, uint32_t value) {
    struct charge_args *p_args = NULL;
    if (dev == NULL || dev->device_args == NULL)
        return -1;

    p_args             = (struct charge_args *) dev->device_args;
    int32_t adc_offset = 0;
    if (p_args->adc_reference_raw_offset_value_get && value != 0) {
        adc_offset = p_args->adc_reference_offset_value;
    }

    uint32_t vdda    = 2500 * 4096 / (3103 + adc_offset);
    uint32_t dac_val = ((value) *4095) / vdda;  // 3300 - adc_offset;
    dac_write(p_args->charge_bus.dac_pw_v, p_args->charge_bus.dac_pw_v->bus_addr, DAC_ALIGN_12B_R, dac_val);
    log_d("%s adc_offset: %d, value: %lu, dac_val: %lu", __FUNCTION__, adc_offset, value, dac_val);
    return 0;
}

int32_t charge_set_power_current(const struct ca_device *dev, uint32_t value) {
    struct charge_args *p_args = NULL;
    if (dev == NULL || dev->device_args == NULL)
        return -1;

    p_args             = (struct charge_args *) dev->device_args;
    int32_t adc_offset = 0;
    if (p_args->adc_reference_raw_offset_value_get && value != 0) {
        adc_offset = p_args->adc_reference_offset_value;
    }

    uint32_t vdda    = 2500 * 4096 / (3103 + adc_offset);
    uint32_t dac_val = ((value) *4095) / vdda;  // 3300 - adc_offset;
    dac_write(p_args->charge_bus.dac_pw_i, p_args->charge_bus.dac_pw_i->bus_addr, DAC_ALIGN_12B_R, dac_val);
    log_d("%s adc_offset: %d, value: %lu, dac_val: %lu", __FUNCTION__, adc_offset, value, dac_val);
    return 0;
}

int32_t charge_get_power_voltage(const struct ca_device *dev) {
    int32_t             adc_value = 0;
    struct charge_args *p_args    = NULL;
    if (dev == NULL || dev->device_args == NULL)
        return -1;
    p_args = (struct charge_args *) dev->device_args;
    // adc_value = adc_read(p_args->charge_bus.adc_pw_v, p_args->charge_bus.adc_pw_v->bus_addr);
    adc_value          = p_args->adc_pw_v_value;
    int32_t adc_offset = 0;
    if (p_args->adc_reference_raw_offset_value_get) {
        adc_offset = p_args->adc_reference_offset_value;
    }
    log_d("%s adc value:%ld, adc_offset: %ld", __FUNCTION__, adc_value, adc_offset);
    return (((adc_value - adc_offset) * 3300) / 4096) * p_args->adc_pw_v_mult;
}

int32_t charge_get_bat_voltage(const struct ca_device *dev) {
    int32_t             adc_value = 0;
    struct charge_args *p_args    = NULL;
    if (dev == NULL || dev->device_args == NULL)
        return -1;

    p_args = (struct charge_args *) dev->device_args;
    // adc_value = adc_read(p_args->charge_bus.adc_bat_v, p_args->charge_bus.adc_bat_v->bus_addr);
    adc_value          = p_args->adc_bat_v_value;
    int32_t adc_offset = 0;
    if (p_args->adc_reference_raw_offset_value_get) {
        adc_offset = p_args->adc_reference_offset_value;
    }
    log_d("%s adc_value :%lu, adc_offset: %ld , adc_v_mult: %f", __FUNCTION__, adc_value, adc_offset, p_args->adc_bat_v_mult);
    return (int32_t)(((adc_value - adc_offset) * 3300) / 4096) * p_args->adc_bat_v_mult;
}

int32_t charge_get_bat_current(const struct ca_device *dev) {
    int32_t             adc_value = 0;
    struct charge_args *p_args    = NULL;
    if (dev == NULL || dev->device_args == NULL)
        return -1;

    p_args = (struct charge_args *) dev->device_args;
    // adc_value = adc_read(p_args->charge_bus.adc_bat_i, p_args->charge_bus.adc_bat_i->bus_addr);
    adc_value          = p_args->adc_bat_i_value;
    int32_t adc_offset = 0;
    if (p_args->adc_reference_raw_offset_value_get) {
        adc_offset = p_args->adc_reference_offset_value;
    }
    log_d("%s adc value :%lu, adc_offset: %ld, adc_i_mult: %f", __FUNCTION__, adc_value, adc_offset, p_args->adc_bat_i_mult);
    return (int32_t)(((adc_value - adc_offset) * 3300) / 4096) * p_args->adc_bat_i_mult;
}

int32_t charge_get_power_temp_voltage(const struct ca_device *dev) {
    int32_t             adc_value = 0;
    struct charge_args *p_args    = NULL;
    if (dev == NULL || dev->device_args == NULL)
        return -1;

    p_args = (struct charge_args *) dev->device_args;
    // adc_value = adc_read(p_args->charge_bus.adc_pw_temp, p_args->charge_bus.adc_pw_temp->bus_addr);
    adc_value          = p_args->adc_pw_temp_value;
    int32_t adc_offset = 0;
    if (p_args->adc_reference_raw_offset_value_get) {
        adc_offset = p_args->adc_reference_offset_value;
    }
    log_d("%s adc_value :%lu,  adc_offset: %ld, adc_temp_mult: %f", __FUNCTION__, adc_value, adc_offset, p_args->adc_pw_temp_mult);
    return (int32_t)(((adc_value - adc_offset) * 3300) / 4096) * p_args->adc_pw_temp_mult;
}

int32_t charge_set_realy_status(const struct ca_device *dev, uint8_t value) {
    struct charge_args *p_args = NULL;
    if (dev == NULL || dev->device_args == NULL)
        return -1;

    p_args = (struct charge_args *) dev->device_args;
    gpio_ioctl(p_args->charge_bus.gpio_relay, (value ? GPIO_ACTIVE_HIGH : GPIO_ACTIVE_LOW), NULL);
    return 0;
}

int32_t charge_set_power_chip_status(const struct ca_device *dev, uint8_t value) {
    struct charge_args *p_args = NULL;
    if (dev == NULL || dev->device_args == NULL)
        return -1;

    p_args = (struct charge_args *) dev->device_args;
    gpio_ioctl(p_args->charge_bus.gpio_pw_off, (value ? GPIO_ACTIVE_HIGH : GPIO_ACTIVE_LOW), NULL);
    return 0;
}

int32_t charge_get_ac_fail_det_status(const struct ca_device *dev) {
    struct charge_args *p_args = NULL;
    if (dev == NULL || dev->device_args == NULL)
        return -1;

    p_args = (struct charge_args *) dev->device_args;
    return p_args->ac_fail_det_value;
}

int32_t charge_get_hw_protect_det_status(const struct ca_device *dev) {
    struct charge_args *p_args = NULL;
    if (dev == NULL || dev->device_args == NULL)
        return -1;

    p_args = (struct charge_args *) dev->device_args;
    return p_args->hw_port_det_value;
}

int32_t charge_get_elec_sec_det_status(const struct ca_device *dev) {
    struct charge_args *p_args = NULL;
    if (dev == NULL || dev->device_args == NULL)
        return -1;

    p_args = (struct charge_args *) dev->device_args;
    return p_args->elec_sec_det_value;
}

int32_t charge_open(const struct ca_device *dev, int32_t flags) {
    return 0;
}

int32_t charge_close(const struct ca_device *dev) {
    return 0;
}

int32_t charge_read(const struct ca_device *dev, void *buffer, uint32_t size) {
    return 0;
}

int32_t charge_write(const struct ca_device *dev, void *buffer, uint32_t size) {
    if ((NULL == dev) || (NULL == dev->bus) || (NULL == dev->bus->handler) || (NULL == buffer)) {
        return -1;
    }

    return 0;
}

int32_t charge_ioctl(const struct ca_device *dev, uint32_t cmd, void *arg) {
    struct charge_args *p_args = NULL;
    struct list_struct *p_list = NULL;
    charge_bus_info_t * p_bus  = NULL;
    p_args                     = (struct charge_args *) dev->device_args;

    if (NULL == dev) {
        return -1;
    }
    // log_i("%s write :%ld", __FUNCTION__, cmd);
    if (osMutexAcquire(p_args->charge_mutex, 0) == osOK) {
        switch (cmd) {
            case CHARGE_CMD_SET_PW_V:
                charge_set_power_voltage(dev, *(uint32_t *) arg);
                break;
            case CHARGE_CMD_SET_PW_I:
                charge_set_power_current(dev, *(uint32_t *) arg);
                break;
            case CHARGE_CMD_GET_PW_V:
                *(int32_t *) arg = charge_get_power_voltage(dev);
                break;
            case CHARGE_CMD_GET_BAT_V:
                *(int32_t *) arg = charge_get_bat_voltage(dev);
                break;
            case CHARGE_CMD_GET_BAT_I:
                *(int32_t *) arg = charge_get_bat_current(dev);
                break;
            case CHARGE_CMD_GET_PW_TEMP_V:
                *(int32_t *) arg = charge_get_power_temp_voltage(dev);
                break;
            case CHARGE_CMD_SET_PW_ON:
                if (p_args->power_chip_value)
                    charge_set_power_chip_status(dev, 1);
                else
                    charge_set_power_chip_status(dev, 0);
                break;
            case CHARGE_CMD_SET_PW_OFF:
                if (p_args->power_chip_value)
                    charge_set_power_chip_status(dev, 0);
                else
                    charge_set_power_chip_status(dev, 1);
                break;
            case CHARGE_CMD_SET_RELAY_ON:
                if (p_args->relay_value)
                    charge_set_realy_status(dev, 1);
                else
                    charge_set_realy_status(dev, 0);
                break;
            case CHARGE_CMD_SET_RELAY_OFF:
                if (p_args->relay_value)
                    charge_set_realy_status(dev, 0);
                else
                    charge_set_realy_status(dev, 1);
                break;
            case CHARGE_CMD_GET_AC_FAIL_VALUE:
                *(int32_t *) arg = charge_get_ac_fail_det_status(dev);
                break;
            case CHARGE_CMD_GET_HW_PR0T_VALUE:
                *(int32_t *) arg = charge_get_hw_protect_det_status(dev);
                break;
            case CHARGE_CMD_GET_ELEC_SEC_VALUE:
                *(int32_t *) arg = charge_get_elec_sec_det_status(dev);
                break;
            default:
                break;
        }
        osMutexRelease(p_args->charge_mutex);
    }
    return 0;
}

/*
 * brief : the task of the adc sampling
 * param : void
 * return: void
 */
static void charge_run(void *argument) {
    struct ca_device *  dev    = (struct ca_device *) argument;
    struct charge_args *p_args = (struct charge_args *) dev->device_args;
#define FILTER_BUFFER_SIZE 40
    uint16_t     adc_value                                = 0;
    int32_t      adc_offset                               = 0;
    int          pw_v_buffer[FILTER_BUFFER_SIZE]          = {0};
    int          bat_v_buffer[FILTER_BUFFER_SIZE]         = {0};
    int          bat_i_buffer[FILTER_BUFFER_SIZE]         = {0};
    int          pw_temp_buffer[FILTER_BUFFER_SIZE]       = {0};
    int          adc_reference_buffer[FILTER_BUFFER_SIZE] = {0};
    unsigned int index[5]                                 = {0};
    while (1) {
        if (adc_read(p_args->charge_bus.adc_pw_v, p_args->charge_bus.adc_pw_v->bus_addr, &adc_value) == 0) {
            p_args->adc_pw_v_value = moveing_average_filter(pw_v_buffer, FILTER_BUFFER_SIZE, adc_value, index[0]);
            index[0]++;
        }

        if (adc_read(p_args->charge_bus.adc_bat_v, p_args->charge_bus.adc_bat_v->bus_addr, &adc_value) == 0) {
            p_args->adc_bat_v_value = moveing_average_filter(bat_v_buffer, FILTER_BUFFER_SIZE, adc_value, index[1]);
            index[1]++;
        }

        if (adc_read(p_args->charge_bus.adc_bat_i, p_args->charge_bus.adc_bat_i->bus_addr, &adc_value) == 0) {
            p_args->adc_bat_i_value = moveing_average_filter(bat_i_buffer, FILTER_BUFFER_SIZE, adc_value, index[2]);
            index[2]++;
        }

        if (adc_read(p_args->charge_bus.adc_pw_temp, p_args->charge_bus.adc_pw_temp->bus_addr, &adc_value) == 0) {
            p_args->adc_pw_temp_value = moveing_average_filter(pw_temp_buffer, FILTER_BUFFER_SIZE, adc_value, index[3]);
            index[3]++;
        }

        adc_offset                         = p_args->adc_reference_raw_offset_value_get();
        p_args->adc_reference_offset_value = moveing_average_filter(adc_reference_buffer, FILTER_BUFFER_SIZE, adc_offset, index[4]);
        index[4]++;

        osDelay(50);
    }
}

/**
 * Function:       gpio_charge_init
 * Description:    初始化 gpio_charge
 * Calls:
 * Called By:
 * @param[in]
 * @param[out]
 * @return
 *  - 0 表示成功
 *  - 1 表示失败
 *
 * @author:
 * @date
 *
 * @par Modification History
 * @par Author:
 * @par Date:
 * @par Description:
 *
 * @see
 *
 */
int32_t charge_init(struct ca_device *dev) {
    charge_bus_info_t *p_bus_info    = NULL;
    charge_attr_t *    p_charge_attr = NULL;

    if ((NULL == dev) || (NULL == dev->bus) || (NULL == dev->device_args)) {
        return -1;
    }

    p_charge_attr = (charge_attr_t *) dev->device_args;
    p_bus_info    = p_charge_attr->charge_bus_info;
    if (NULL == p_bus_info) {
        return -1;
    }

    memset(&dev->ops, 0, sizeof(struct ca_device_ops));
    dev->ops.open  = charge_open;
    dev->ops.close = charge_close;
    dev->ops.read  = charge_read;
    dev->ops.write = charge_write;
    dev->ops.ioctl = charge_ioctl;

    p_charge_args = (struct charge_args *) mem_block_alloc(sizeof(struct charge_args));
    if (NULL == p_charge_args) {
        return -1;
    }
    memset(p_charge_args, 0, sizeof(struct charge_args));

    if (bus_find_name(&p_bus_info->dac_pw_v, &p_charge_args->charge_bus.dac_pw_v)) {
        return -1;
    }
    if (bus_find_name(&p_bus_info->dac_pw_i, &p_charge_args->charge_bus.dac_pw_i)) {
        return -1;
    }
    if (bus_find_name(&p_bus_info->adc_pw_v, &p_charge_args->charge_bus.adc_pw_v)) {
        return -1;
    }
    if (bus_find_name(&p_bus_info->adc_bat_i, &p_charge_args->charge_bus.adc_bat_i)) {
        return -1;
    }
    if (bus_find_name(&p_bus_info->adc_bat_v, &p_charge_args->charge_bus.adc_bat_v)) {
        return -1;
    }
    if (bus_find_name(&p_bus_info->adc_pw_temp, &p_charge_args->charge_bus.adc_pw_temp)) {
        return -1;
    }
    if (bus_find_name(&p_bus_info->gpio_relay, &p_charge_args->charge_bus.gpio_relay)) {
        return -1;
    }
    if (bus_find_name(&p_bus_info->gpio_pw_off, &p_charge_args->charge_bus.gpio_pw_off)) {
        return -1;
    }
    if (bus_find_name(&p_bus_info->gpio_ac_fail_det, &p_charge_args->charge_bus.gpio_ac_fail_det)) {
        return -1;
    }
    if (bus_find_name(&p_bus_info->gpio_hw_protect_det, &p_charge_args->charge_bus.gpio_hw_protect_det)) {
        return -1;
    }
    if (bus_find_name(&p_bus_info->gpio_elec_sec_det, &p_charge_args->charge_bus.gpio_elec_sec_det)) {
        return -1;
    }
    charge_input_det_attr_init(dev);
    p_charge_args->relay_value                        = p_charge_attr->relay_valid_value;
    p_charge_args->power_chip_value                   = p_charge_attr->power_chip_valid_value;
    p_charge_args->adc_pw_v_mult                      = p_charge_attr->adc_pw_v_mult;
    p_charge_args->adc_bat_v_mult                     = p_charge_attr->adc_bat_v_mult;
    p_charge_args->adc_bat_i_mult                     = p_charge_attr->adc_bat_i_mult;
    p_charge_args->adc_pw_temp_mult                   = p_charge_attr->adc_pw_temp_mult;
    p_charge_args->adc_reference_raw_offset_value_get = p_charge_attr->adc_raw_offset_obtain;
    dev->device_args                                  = (void *) p_charge_args;
    p_charge_args->charge_mutex                       = osMutexNew(&mutex_charge_attr);
    list_head_init(&p_charge_args->charge_list);
    osThreadNew(charge_run, (void *) dev, &charge_thread_attributes);
    return 0;
}
DEVICE_DRIVER_INIT(charge, adc, charge_init);
/**
 * Function:       gpio_charge_deinit
 * Description:    去初始化 gpio_charge
 * Calls:
 * Called By:
 * @param[in]
 * @param[out]
 * @return
 *  - 0 表示成功
 *  - 1 表示失败
 *
 * @author:
 * @date
 *
 * @par Modification History
 * @par Author:
 * @par Date:
 * @par Description:
 *
 * @see
 *
 */
int32_t charge_deinit(struct ca_device *dev) {
    return 0;
}

extern int bat_charger_handle;
//设置电压电流
void bat_charge_set(uint8_t cmd, int32_t *pvalue) {
    int32_t *data = pvalue;

    switch (cmd) {
        case CHARGE_CMD_SET_PW_V:
        case CHARGE_CMD_SET_PW_I:
            device_ioctl(bat_charger_handle, cmd, (void *) data);
            // log_d("cmd :%d, write :%ld", cmd, *data);
            break;
        case CHARGE_CMD_GET_PW_V:
        case CHARGE_CMD_GET_BAT_V:
        case CHARGE_CMD_GET_BAT_I:
        case CHARGE_CMD_GET_PW_TEMP_V:
            device_ioctl(bat_charger_handle, cmd, (void *) data);
            // log_d("cmd :%d, read :%ld", cmd, *data);
            break;
        case CHARGE_CMD_SET_PW_ON:
        case CHARGE_CMD_SET_PW_OFF:
        case CHARGE_CMD_SET_RELAY_ON:
        case CHARGE_CMD_SET_RELAY_OFF:
            device_ioctl(bat_charger_handle, cmd, (void *) data);
            // log_d("cmd :%d, write :%ld", cmd, *data);
            break;
        case CHARGE_CMD_GET_AC_FAIL_VALUE:
        case CHARGE_CMD_GET_HW_PR0T_VALUE:
        case CHARGE_CMD_GET_ELEC_SEC_VALUE:
            device_ioctl(bat_charger_handle, cmd, (void *) data);
            // log_d("cmd :%d, read :%ld", cmd, *data);
            break;
        default:
            log_i("help : bat_charge_set  <itum>  [value]");
            log_i("itum:               value :");
            log_i("[0] 充电器电压设置           0-3300");
            log_i("[1] 充电器电流设置           0-3300");
            log_i("[2] 充电器电压获取                 ");
            log_i("[3] 电池电压获取");
            log_i("[4] 电池电流获取");
            log_i("[5] 温度传感器获取");
            log_i("[6] 继电器打开");
            log_i("[7] 继电器关闭");
            log_i("[8] 芯片供电打开");
            log_i("[9] 芯片供电关闭");
            log_i("[10] AC市电输入状态获取");
            log_i("[11] 硬件保护状态获取");
            break;
    }
    return;
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), bat_charge_set, bat_charge_set, bat_charge_set);
