/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:
 ** Author:         huangjianxian
 ** Version:        V0.0.1
 ** Date:           2025-05-07
 ** Description:    AW9523 I2C IO Expander chip definitions.
 ** Others:
 ** Function List:
 ** History:        2025-05-07    huangjianxian    0.0.1        创建文件
 ******************************************************************/

#ifndef _DEFINE_AW9523_H
#define _DEFINE_AW9523_H

#include "devices.h"

#ifdef __cplusplus
extern "C" {
#endif

// AW9523 does not require specific attributes like a separate enable GPIO in LIMSCG03_attr_t
// as chip enable is handled via I2C register.
// typedef struct {
//     struct bus_info gpio_enable;
// } AW9523_attr_t;

typedef enum {
    AW9523_CMD_SET_PIN_DIR,    // Arg: aw9523_pin_op_t* (pin_num and direction are input)
    AW9523_CMD_GET_PIN_LEVEL,  // Arg: aw9523_pin_op_t* (pin_num is input, level is output)
    AW9523_CMD_SET_PIN_LEVEL,  // Arg: aw9523_pin_op_t* (pin_num and level are input)
    AW9523_CMD_SOFT_RESET,     // Arg: NULL
} AW9523_CMD;

typedef struct {
    uint8_t pin_num;    // Pin number: 0-7 for P0.0-P0.7, 8-15 for P1.0-P1.7
    uint8_t direction;  // 0 for output, 1 for input (used in AW9523_CMD_SET_PIN_DIR)
    uint8_t level;      // 0 for low, 1 for high (used in SET/GET_PIN_LEVEL)
} aw9523_pin_op_t;

#ifdef __cplusplus
}
#endif

// AW9523 I2C ADDR (7-bit address)
#define AW9523_I2C_ADDR (0x5A)

// AW9523 Register Addresses
#define AW9523_REG_INPUT_P0         (0x00)  // Input Port 0 Register
#define AW9523_REG_INPUT_P1         (0x01)  // Input Port 1 Register
#define AW9523_REG_OUTPUT_P0        (0x02)  // Output Port 0 Register
#define AW9523_REG_OUTPUT_P1        (0x03)  // Output Port 1 Register
#define AW9523_REG_CONFIG_P0        (0x04)  // Configure Port 0 Register (0:Output, 1:Input)
#define AW9523_REG_CONFIG_P1        (0x05)  // Configure Port 1 Register (0:Output, 1:Input)
#define AW9523_REG_INT_P0           (0x06)  // Interrupt Enable Port 0 Register (Not used in this driver)
#define AW9523_REG_INT_P1           (0x07)  // Interrupt Enable Port 1 Register (Not used in this driver)
#define AW9523_REG_ID               (0x10)  // ID Register (Read Only, 0x23)
#define AW9523_REG_GCR              (0x11)  // Global Control Register
#define AW9523_REG_LED_MODE_SWITCH0 (0x12)  // P0 Port LED Mode Switch Register
#define AW9523_REG_LED_MODE_SWITCH1 (0x13)  // P1 Port LED Mode Switch Register

#define AW9523_REG_SOFT_RESET (0x7F)  // Software Reset Register

// GCR Register bits
#define AW9523_GCR_P0_MODE_GPIO      (0x00)  // Bit 0: 0 for GPIO mode
#define AW9523_GCR_P1_MODE_GPIO      (0x00)  // Bit 4: 0 for GPIO mode
#define AW9523_GCR_DEFAULT_GPIO_MODE (0x00)  // P0 and P1 in GPIO mode

// IO Direction
#define AW9523_IO_OUTPUT (0)
#define AW9523_IO_INPUT  (1)

// IO Level
#define AW9523_IO_LOW  (0)
#define AW9523_IO_HIGH (1)

#endif  // _DEFINE_AW9523_H
