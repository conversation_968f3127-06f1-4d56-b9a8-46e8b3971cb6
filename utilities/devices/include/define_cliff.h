/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:
 ** Author:         chenshouyi
 ** Version:        V0.0.1
 ** Date:           2023-01-13
 ** Description:
 ** Others:
 ** Function List:
 ** History:        2023-01 chenshouyi 创建
 ** <time>          <author>    <version >    <desc>
 ** 2023-01-13      chenshouyi    0.0.1        创建文件
 ******************************************************************/

#ifndef _DEFINE_CLIFF_H
#define _DEFINE_CLIFF_H

typedef enum {
    CLIFF_CMD_SET_DATA,   //新增断崖传感器
    CLIFF_CMD_GET_VALUE,  //获取ADC值
    CLIFF_CMD_GET_STATE,  //获取断崖状态
    CLIFF_CMD_ENABLE,     //启动断崖检测
    CLIFF_CMD_IS_TYPE,    //判断类型
    CLIFF_CMD_DISABLE,
    CLIFF_CMD_SET_VOL_THRES,  //设置断崖电压阈值
    CLIFF_CMD_MAX
} CLIFF_CMD_TYPE;

#endif