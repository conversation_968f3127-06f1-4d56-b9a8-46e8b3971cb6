/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:			 bldcm.c (brushless direct current motor 无刷直流电机)
 ** Author:         刘春阳
 ** Version:        V0.0.1
 ** Date:           2022-5-26
 ** Description:
 ** Others:
 ** Function List:
 ** History:        2022-05 刘春阳 创建
 ** <time>          <author>    <version >    <desc>
 ** 2021-3-25       sulikang    0.0.1         创建文件
 ******************************************************************/

/*****************************************************************
 * 包含头文件
 ******************************************************************/
#include "gpio_silicon.h"
#include "mem_pool.h"
#include <stddef.h>
#include <string.h>
#include <stdlib.h>
#include "delay.h"
#include <math.h>
#include "shell.h"
#include "gpio_core.h"
#include "adc_core.h"
#include "tim_core.h"
#include "define.h"
#include "define_silicon.h"
#ifdef USING_SEGGER
#include "SEGGER_RTT.h"
#endif
#define LOG_TAG "gpio_silicon"
#include "log.h"
/**
 * @addtogroup Robot_DEVICES
 * @{
 */

/**
 * @defgroup Robot_silicon  - 可控硅
 *
 * @brief  \n
 * \n
 * @{
 */

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 私有宏定义
 ******************************************************************/
#define AC_POWER_HALF_PERIOD     200  // 50Hz:50us * 200 = 10ms 60Hz:50us * 166 = 8.3ms
#define AC_FREQ_CALC_FILTER_NUMS 10
/*****************************************************************
 * 私有结构体/共用体/枚举定义
 ******************************************************************/
typedef struct {
    struct ca_bus *gpio_vcc;  // IO控制
    struct ca_bus *adc;       // 电流采样
} GPIO_BUS;

typedef struct silicon_node {
    struct list_struct  node;         //链表节点信息
    SILICON_INFO_BUS    gpio_info;    //按键PIN脚
    GPIO_BUS            gpio_bus;     //按键PIN脚bus信息
    uint8_t             gpio_en;      //使能
    SILICON_CTRL_MODULE ctrl_module;  //可控硅控制模块
    uint8_t             sw_gear;      //组件档位
    uint8_t             min_gear;     //最小档位
    uint8_t             max_gear;     //最大档位
    uint16_t            slowStartInterval;  //档位缓启动间隔周期（slowStartInterval * 50us）即同一个档位执行后间隔slowStartInterval后才会执行下一个档位
    uint8_t             open_state;           //被控pin触发电平
    uint16_t *          current;              //电流
    SILICON_PARAM       silicon_param;        //可控硅参数
} SILICON_NODE_ATTR_T, *SILICON_NODE_ATTR_P;  //按键链表节点信息

typedef struct {
    struct ca_bus *gpio_zero;  //零点检测
    struct ca_bus *gpio_tim;   //定时器
} SILICON_BUS;

struct silicon_args {
    SILICON_DATA       silicon_data;      //可控硅数据
    SILICON_MODE       silicon_mode;      //可控硅模式
    CYCLE_MODE         cycle_mode;        //周波模式(看过零信号是触发半波还是全波)
    osThreadId_t       task_bt_Handle;    //任务处理线程
    SILICON_BUS        silicon_bus;       //总线
    osSemaphoreId_t    silicon_semphore;  //信号量
    osMutexId_t        silicon_mutex;     //互斥锁
    struct list_struct silicon_list;      //按键链表头
    osThreadId_t       silicon_thread;    //处理线程id
    GPIO_EXTI_TYPE     silicon_EXIT;      //边沿触发类型
};
/*****************************************************************
 * 全局变量定义
 ******************************************************************/
const osMutexAttr_t mutex_silicon_attr = {
    "silicon_mutex",                        // human readable mutex name
    osMutexRecursive | osMutexPrioInherit,  // attr_bits
    NULL,                                   // memory for control block
    0U                                      // size for control block
};
osThreadAttr_t silicon_thread_attributes = {.name = "silicon_thread", .priority = (osPriority_t) osPriorityNormal, .stack_size = 256 * 4};
/*****************************************************************
 * 私有全局变量定义
 ******************************************************************/
static uint32_t exti_interval     = 0;
static uint32_t elapsed_interval  = 0;
static uint32_t slowStartInterval = 0;

// 计算交流电频率
static bool    ac_freq_calculated       = false;
static uint8_t ac_freq_calc_filter_nums = AC_FREQ_CALC_FILTER_NUMS;
uint16_t       ac_freq_calculate_ticks  = 0;
float          ac_frequency             = 0;  //交流电频率
uint32_t       ac_half_period_nums      = 0;  //交流电半周期定时器tick数
/*****************************************************************
 * 外部变量声明
 ******************************************************************/

/*****************************************************************
 * 私有函数原型声明
 ******************************************************************/
static void ac_freq_init(void);
/*****************************************************************
 * 函数定义
 ******************************************************************/
static void ac_freq_init(void) {
    //计算交流电频率
    static float sum_ac_frequency  = 0.0;
    float        temp_ac_frequency = 0.0;

    if (ac_freq_calculate_ticks != 0) {
        temp_ac_frequency = 1000 / ((ac_freq_calculate_ticks * 50) / 1000);
        sum_ac_frequency += temp_ac_frequency;
        ac_freq_calc_filter_nums--;
        ac_freq_calculate_ticks = 0;
    }

    if (ac_freq_calc_filter_nums == 0) {
        ac_frequency        = sum_ac_frequency / AC_FREQ_CALC_FILTER_NUMS;
        ac_half_period_nums = (1000 / ac_frequency / 2) * 1000 / 50;  // 交流电半周期定时器tick数,一个tick为50us
        ac_freq_calculated  = true;
    }
}

void silicon_exti_callback(const struct ca_device *dev, uint16_t gpio_pin) {
    struct silicon_args *p_args = NULL;
    struct list_struct * p_list = NULL;
    struct silicon_node *p_node = NULL;
    if ((NULL == dev) || (NULL == dev->device_args)) {
        return;
    }

    p_args = (struct silicon_args *) dev->device_args;
    if ((NULL != p_args->silicon_bus.gpio_zero) && (p_args->silicon_bus.gpio_zero->bus_addr == gpio_pin)) {
        p_list = &p_args->silicon_list;
        exti_interval++;
        //有外部中断时，开启定时器，同时清进入定时器中断的次数
        elapsed_interval = 0;
        // 计算交流电频率
        if (!ac_freq_calculated) {
            ac_freq_init();
        }

        while (list_is_last(p_list, &p_args->silicon_list) != 1) {
            p_node = (struct silicon_node *) p_list->next;
            if (p_args->silicon_mode == zero_cross_trigger) {
                //使能设置
                if (p_node->gpio_en == 1) {
                    p_node->silicon_param.cycle_wave_num = 10 - p_node->sw_gear;
                    if (p_args->cycle_mode == full_wave) {
                        exti_interval = exti_interval / 2;
                    }
                    if (exti_interval % p_node->silicon_param.cycle_wave_num == 0) {
                        gpio_ioctl(p_node->gpio_bus.gpio_vcc, (p_node->open_state ? GPIO_ACTIVE_HIGH : GPIO_ACTIVE_LOW), NULL);
                    }
                    if (exti_interval % 10 == 0) {
                        gpio_ioctl(p_node->gpio_bus.gpio_vcc, (p_node->open_state ? GPIO_ACTIVE_LOW : GPIO_ACTIVE_HIGH), NULL);
                        exti_interval = 0;
                    }
                } else {
                    gpio_ioctl(p_node->gpio_bus.gpio_vcc, (p_node->open_state ? GPIO_ACTIVE_LOW : GPIO_ACTIVE_HIGH), NULL);
                }
            } else if (p_args->silicon_mode == phase_shift_trigger) {
                tim_set_counter(p_args->silicon_bus.gpio_tim, 0);
                tim_period_elapsed_start(p_args->silicon_bus.gpio_tim);
            }
            p_list = p_list->next;
        }
    }
}

void silicon_elapsed_callback(const struct ca_device *dev, void *handler) {
    struct silicon_args *p_args = NULL;
    struct list_struct * p_list = NULL;
    struct silicon_node *p_node = NULL;
    if ((NULL == dev) || (NULL == dev->device_args)) {
        return;
    }

    p_args = (struct silicon_args *) dev->device_args;
    if ((NULL != p_args->silicon_bus.gpio_tim) && (p_args->silicon_bus.gpio_tim->handler == handler)) {
        p_list = &p_args->silicon_list;
        elapsed_interval++;
        if (!ac_freq_calculated) {
            ac_freq_calculate_ticks++;
        }
        while (list_is_last(p_list, &p_args->silicon_list) != 1) {
            p_node = (struct silicon_node *) p_list->next;
            if (NULL != p_node->gpio_bus.gpio_vcc) {
                //使能设置
                if (p_node->gpio_en == 1) {
                    exti_interval = 0;
                    //参数设置
                    p_node->silicon_param.zero_ctrl_time  = 1;
                    p_node->silicon_param.zero_delay_time = p_node->sw_gear;

                    //控制部分
                    if (elapsed_interval == p_node->silicon_param.zero_delay_time) {
                        gpio_ioctl(p_node->gpio_bus.gpio_vcc, (p_node->open_state ? GPIO_ACTIVE_HIGH : GPIO_ACTIVE_LOW), NULL);
                    } else if (elapsed_interval == (p_node->silicon_param.zero_delay_time + p_node->silicon_param.zero_ctrl_time)) {
                        gpio_ioctl(p_node->gpio_bus.gpio_vcc, (p_node->open_state ? GPIO_ACTIVE_LOW : GPIO_ACTIVE_HIGH), NULL);
                    } else if (elapsed_interval == (ac_half_period_nums + p_node->silicon_param.zero_delay_time)) {
                        gpio_ioctl(p_node->gpio_bus.gpio_vcc, (p_node->open_state ? GPIO_ACTIVE_HIGH : GPIO_ACTIVE_LOW), NULL);
                    } else if (elapsed_interval ==
                               (ac_half_period_nums + p_node->silicon_param.zero_delay_time + p_node->silicon_param.zero_ctrl_time)) {
                        gpio_ioctl(p_node->gpio_bus.gpio_vcc, (p_node->open_state ? GPIO_ACTIVE_LOW : GPIO_ACTIVE_HIGH), NULL);
                        //控制输出一个脉冲之后，关闭定时器，同时清除定时器
                        tim_period_elapsed_stop(p_args->silicon_bus.gpio_tim);
                        tim_set_counter(p_args->silicon_bus.gpio_tim, 0);
                        elapsed_interval = 0;
                        if (p_node->ctrl_module == DUST_FAN_CTRL && p_node->sw_gear != p_node->max_gear) {
                            if (slowStartInterval == 0) {
                                p_node->sw_gear--;
                                slowStartInterval = p_node->slowStartInterval;
                            } else {
                                slowStartInterval--;
                            }
                        }
                    }
                } else {
                    if (p_node->ctrl_module == DUST_FAN_CTRL) {
                        p_node->sw_gear   = p_node->min_gear;
                        slowStartInterval = p_node->slowStartInterval;
                    }
                    gpio_ioctl(p_node->gpio_bus.gpio_vcc, (p_node->open_state ? GPIO_ACTIVE_LOW : GPIO_ACTIVE_HIGH), NULL);
                }
            }
            p_list = p_list->next;
        }
    }
}

//设置可控硅模式
int32_t gpio_silicon_set_mode(const struct ca_device *dev, SILICON_MODE mode) {
    struct silicon_args *p_args = NULL;

    if ((NULL == dev) || (NULL == dev->device_args)) {
        return -1;
    }

    p_args = (struct silicon_args *) dev->device_args;

    if (osMutexAcquire(p_args->silicon_mutex, 0) == osOK) {
        memcpy(&p_args->silicon_mode, &mode, sizeof(SILICON_MODE));
        osMutexRelease(p_args->silicon_mutex);
    }
    return 0;
}

int32_t gpio_silicon_open(const struct ca_device *dev, int32_t flags) {
    return 0;
}

int32_t gpio_silicon_close(const struct ca_device *dev) {
    return 0;
}

int32_t gpio_silicon_read(const struct ca_device *dev, void *buffer, uint32_t size) {
    return 0;
}

int32_t gpio_silicon_write(const struct ca_device *dev, void *buffer, uint32_t size) {
    if ((NULL == dev) || (NULL == dev->bus) || (NULL == dev->bus->handler) || (NULL == buffer)) {
        return -1;
    }

    return 0;
}

/*****************************************************************/
/**
 * Function:       gpio_silicon_ioctl
 * Description:    可控硅电机控制指令
 * Calls:
 * Called By:
 * @param[in]
 * @param[out]
 * @return
 *  - 0 表示成功
 *  - 1 表示失败
 *
 * @author:
 * @date
 *
 * @par Modification History
 * @par Author:
 * @par Date:
 * @par Description:
 *
 * @see
 *
 */
/******************************************************************/
int32_t gpio_silicon_ioctl(const struct ca_device *dev, uint32_t cmd, void *arg) {
    struct silicon_args *p_args    = NULL;
    SILICON_NODE_ATTR_T *p_silicon = NULL;
    struct list_struct * p_list    = NULL;
    struct silicon_node *p_node    = NULL;
    p_args                         = (struct silicon_args *) dev->device_args;

    if (NULL == dev) {
        return -1;
    }
    switch (cmd) {
        case SI_CMD_SET_MODE:
            gpio_silicon_set_mode(dev, *(SILICON_MODE *) arg);
            break;

        case SI_CMD_SET_ADD_CTRL:
            p_silicon = (SILICON_NODE_ATTR_T *) mem_block_alloc(sizeof(SILICON_NODE_ATTR_T));
            if (NULL == p_silicon) {
                return -1;
            }
            p_silicon->gpio_info         = *((SILICON_ATTR_P) arg)->gpio_info;
            p_silicon->gpio_en           = ((SILICON_ATTR_P) arg)->gpio_en;
            p_silicon->ctrl_module       = ((SILICON_ATTR_P) arg)->ctrl_module;
            p_silicon->sw_gear           = ((SILICON_ATTR_P) arg)->sw_gear;
            p_silicon->min_gear          = ((SILICON_ATTR_P) arg)->min_gear;
            p_silicon->max_gear          = ((SILICON_ATTR_P) arg)->max_gear;
            p_silicon->slowStartInterval = ((SILICON_ATTR_P) arg)->slowStartInterval;
            p_silicon->open_state        = ((SILICON_ATTR_P) arg)->open_state;
            p_silicon->current           = ((SILICON_ATTR_P) arg)->current;
            bus_find_name(&p_silicon->gpio_info.gpio_vcc, &p_silicon->gpio_bus.gpio_vcc);
            bus_find_name(&p_silicon->gpio_info.adc, &p_silicon->gpio_bus.adc);

            list_add_tail(&p_silicon->node, &p_args->silicon_list);
            break;
        case SI_CMD_SET_GEAR:
            p_list = &p_args->silicon_list;
            while (list_is_last(p_list, &p_args->silicon_list) != 1) {
                p_node = (struct silicon_node *) p_list->next;
                if (p_node->ctrl_module == ((SILICON_GEAR_P) arg)->ctrl_module) {
                    p_node->sw_gear = ((SILICON_GEAR_P) arg)->sw_gear;
                }
                p_list = p_list->next;
            }
            break;
        case SI_CMD_OPEN:
            //查链表
            p_list = &p_args->silicon_list;
            while (list_is_last(p_list, &p_args->silicon_list) != 1) {
                p_node = (struct silicon_node *) p_list->next;
                if ((p_node->gpio_info.gpio_vcc.bus_addr == ((SILICON_ATTR_P) arg)->gpio_info->gpio_vcc.bus_addr) &&
                    (memcmp(p_node->gpio_info.gpio_vcc.bus_name, ((SILICON_ATTR_P) arg)->gpio_info->gpio_vcc.bus_name,
                            sizeof(p_node->gpio_info.gpio_vcc.bus_name)) == 0)) {
                    p_node->gpio_en = 1;
                }
                p_list = p_list->next;
            }
            break;
        case SI_CMD_CLOSE:
            p_list = &p_args->silicon_list;
            while (list_is_last(p_list, &p_args->silicon_list) != 1) {
                p_node = (struct silicon_node *) p_list->next;
                if ((p_node->gpio_info.gpio_vcc.bus_addr == ((SILICON_ATTR_P) arg)->gpio_info->gpio_vcc.bus_addr) &&
                    (memcmp(p_node->gpio_info.gpio_vcc.bus_name, ((SILICON_ATTR_P) arg)->gpio_info->gpio_vcc.bus_name,
                            sizeof(p_node->gpio_info.gpio_vcc.bus_name)) == 0)) {
                    p_node->gpio_en = 0;
                }
                p_list = p_list->next;
            }
            gpio_ioctl(p_node->gpio_bus.gpio_vcc, (p_node->open_state ? GPIO_ACTIVE_LOW : GPIO_ACTIVE_HIGH), NULL);
            break;
        case SI_CMD_GET_GEAR:
            p_list = &p_args->silicon_list;
            while (list_is_last(p_list, &p_args->silicon_list) != 1) {
                p_node = (struct silicon_node *) p_list->next;
                if (p_node->ctrl_module == ((SILICON_GEAR_P) arg)->ctrl_module) {
                    ((SILICON_GEAR_P) arg)->sw_gear = p_node->sw_gear;
                }
                p_list = p_list->next;
            }
            break;
        case SI_CMD_GET_CURRENT_VALUE:
            p_list = &p_args->silicon_list;
            while (list_is_last(p_list, &p_args->silicon_list) != 1) {
                p_node = (struct silicon_node *) p_list->next;
                if ((p_node->gpio_info.gpio_vcc.bus_addr == ((SILICON_ATTR_P) arg)->gpio_info->gpio_vcc.bus_addr) &&
                    (memcmp(p_node->gpio_info.gpio_vcc.bus_name, ((SILICON_ATTR_P) arg)->gpio_info->gpio_vcc.bus_name,
                            sizeof(p_node->gpio_info.gpio_vcc.bus_name)) == 0)) {
                    //原来adc_read 接口在转换失败时会返回0值，此处先置0做兼容。
                    *p_node->current = 0;
                    adc_read(p_node->gpio_bus.adc, p_node->gpio_info.adc.bus_addr, &(*p_node->current));
                }
                p_list = p_list->next;
            }
            break;
        case SI_CMD_GET_AC_FREQ:
            *(float *) arg = ac_frequency;
            break;
        case SI_CMD_GET_AC_HALF_PERIOD_NUMS:
            *(uint32_t *) arg = ac_half_period_nums;
            break;
        default:
            return -1;
    }
    return 0;
}

/*****************************************************************/
/**
 * Function:       gpio_silicon_init
 * Description:    初始化 gpio_silicon
 * Calls:
 * Called By:
 * @param[in]
 * @param[out]
 * @return
 *  - 0 表示成功
 *  - 1 表示失败
 *
 * @author:
 * @date
 *
 * @par Modification History
 * @par Author:
 * @par Date:
 * @par Description:
 *
 * @see
 *
 */
/******************************************************************/
int32_t gpio_silicon_init(struct ca_device *dev) {
    struct silicon_args *p_args         = NULL;
    SILICON_IO_BUS *     p_bus          = NULL;
    SILICON_ATTR *       p_silicon_attr = NULL;

    if ((NULL == dev) || (NULL == dev->bus) || (NULL == dev->device_args)) {
        return -1;
    }

    p_silicon_attr = (SILICON_ATTR *) dev->device_args;

    p_bus = p_silicon_attr->silicon_bus;
    if (NULL == p_bus) {
        return -1;
    }
    memset(&dev->ops, 0, sizeof(struct ca_device_ops));

    dev->ops.open  = gpio_silicon_open;
    dev->ops.close = gpio_silicon_close;
    dev->ops.read  = gpio_silicon_read;
    dev->ops.write = gpio_silicon_write;
    dev->ops.ioctl = gpio_silicon_ioctl;

    p_args = (struct silicon_args *) mem_block_alloc(sizeof(struct silicon_args));
    if (NULL == p_args) {
        return -1;
    }
    memset(p_args, 0, sizeof(struct silicon_args));

    p_args->silicon_EXIT = p_silicon_attr->exti_type;
    p_args->silicon_mode = p_silicon_attr->trigger_mode;
    p_args->cycle_mode   = p_silicon_attr->cycle_mode;

    if (bus_find_name(&p_bus->gpio_zero, &p_args->silicon_bus.gpio_zero)) {
        return -1;
    }
    if (bus_find_name(&p_bus->gpio_tim, &p_args->silicon_bus.gpio_tim)) {
        return -1;
    }
    dev->device_args = (void *) p_args;
    if (p_args->silicon_mode == phase_shift_trigger) {
        tim_period_elapsed_register_callback(dev, p_args->silicon_bus.gpio_tim->handler, silicon_elapsed_callback);
    }
    gpio_exti_register_callback(dev, p_args->silicon_bus.gpio_zero->bus_addr, silicon_exti_callback);
    gpio_exti_start(p_args->silicon_bus.gpio_zero, p_args->silicon_EXIT);
    p_args->silicon_mutex = osMutexNew(&mutex_silicon_attr);
    list_head_init(&p_args->silicon_list);
    return 0;
}

/*****************************************************************/
/**
 * Function:       gpio_silicon_deinit
 * Description:    去初始化 gpio_silicon
 * Calls:
 * Called By:
 * @param[in]
 * @param[out]
 * @return
 *  - 0 表示成功
 *  - 1 表示失败
 *
 * @author:
 * @date
 *
 * @par Modification History
 * @par Author:
 * @par Date:
 * @par Description:
 *
 * @see
 *
 */
/******************************************************************/
int32_t gpio_silicon_deinit(struct ca_device *dev) {
    return 0;
}

DEVICE_DRIVER_INIT(gpio_silicon, gpio, gpio_silicon_init);

//备注;
// 1.配置外部中断的边沿检测和可控硅的模式(目前只有移相触发模式)
// 2.根据精度需求配置TIM定时器(仅移相触发模式需要配置TIM)
// 3.外部注册控制IO(需要配置IO的控制电平，使能状态，档位[精度])
#ifdef __cplusplus
}
#endif

/* @} Robot_BLDCM */
/* @} Robot_DEVICES */
