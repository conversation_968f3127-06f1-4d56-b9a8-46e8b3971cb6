/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:
 ** Author:         jianyongxiang
 ** Version:        V0.0.1
 ** Date:           2021-3-25
 ** Description:
 ** Others:
 ** Function List:
 ** History:        2021-03 jianyongxiang 创建
 ** <time>          <author>    <version >    <desc>
 ** 2021-3-25     jianyongxiang   0.0.1       创建文件
 ******************************************************************/

/*****************************************************************
 * 包含头文件
 ******************************************************************/
#include "log.h"
#include "cmsis_os.h"
#include "elog.h"
#include "shell.h"
#include "devices.h"
#include <string.h>
#include <stdlib.h>
#include <time.h>
/**
 * @addtogroup Robot_FAL
 * @{
 */

/**
 * @defgroup F_LOG 日志管理 - LOG
 *
 * @brief  \n
 * \n
 * @{
 */

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 私有宏定义
 ******************************************************************/
#define ELOG_LVL_PRINT ELOG_LVL_INFO
/*****************************************************************
 * 私有结构体/共用体/枚举定义
 ******************************************************************/

/*****************************************************************
 * 全局变量定义
 ******************************************************************/
osMutexId_t         mutex_elog      = NULL;
const osMutexAttr_t mutex_elog_attr = {
    "mutex_elog",                           // human readable mutex name
    osMutexRecursive | osMutexPrioInherit,  // attr_bits
    NULL,                                   // memory for control block
    0U                                      // size for control block
};

osThreadId_t         log_thread;
const osThreadAttr_t log_attributes = {.name = "log", .priority = (osPriority_t) osPriorityBelowNormal, .stack_size = 256 * 4};
/*****************************************************************
 * 私有全局变量定义
 ******************************************************************/
static Log_Typedef log;
/*****************************************************************
 * 外部变量声明
 ******************************************************************/
// extern struct fdb_tsdb tsdb;
/*****************************************************************
 * 私有函数原型声明
 ******************************************************************/
static void log_run(void *argument);
/*****************************************************************
 * 函数定义
 ******************************************************************/
void        elog_set_print_lvl(uint8_t level);
void        elog_start_hook(void);
extern void elog_port_output(const char *log, size_t size);
/*****************************************************************/
/**
 * Function:       log_init
 * Description:    初始化 log
 * Calls:
 * Called By:
 * @param[in]
 * @param[out]
 * @return
 *  - 0 表示成功
 *  - 1 表示失败
 *
 * @author:
 * @date
 *
 * @par Modification History
 * @par Author:
 * @par Date:
 * @par Description:
 *
 * @see
 *
 */
/******************************************************************/
int log_init(void) {
    /*添加模块处理函数*/
    mutex_elog = osMutexNew(&mutex_elog_attr);

    if (mutex_elog == NULL) {
        // log_e("Display mutex create failed.");
    } else {
        // log_i("Display mutex create ok.");
    }

    /* 初始化elog */
    elog_init();

    /* 启动elog */
    elog_start();

#ifdef ELOG_COLOR_ENABLE
    elog_set_text_color_enabled(true);
#endif

    elog_start_hook();

    log_thread = osThreadNew(log_run, NULL, &log_attributes);

    return 0;
}
DEVICE_DRIVER_INIT(log, uart, log_init);

__weak void elog_start_hook(void) {
    return;
}

SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), elog_lvl, elog_set_filter_lvl, elog_set_filter_lvl);
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), elog_tag_lvl, elog_set_filter_tag_lvl, elog_set_filter_tag_lvl);

void log_run(void *argument) {
    size_t      get_log_size = 0;
    static char poll_get_buf[ELOG_ASYNC_POLL_GET_LOG_BUF_SIZE];
    // struct fdb_blob blob;

    for (;;) {
        // app_log 暂无处理业务
        // elog_flush();
        // osDelay(50);  ///< 50 ms 对应 1000 byte 数据

#ifdef ELOG_ASYNC_LINE_OUTPUT
        get_log_size = elog_async_get_line_log(poll_get_buf, ELOG_ASYNC_POLL_GET_LOG_BUF_SIZE);
#else
        get_log_size = elog_async_get_log(poll_get_buf, ELOG_ASYNC_POLL_GET_LOG_BUF_SIZE);
#endif
        if (get_log_size) {
            elog_port_output(poll_get_buf, get_log_size);
            if (log.output_cb) {
                log.output_cb(poll_get_buf, get_log_size);
            }
        }

        osDelay(10);
    }
}

/*****************************************************************/
/**
 * Function:       log_deinit
 * Description:    去初始化 log
 * Calls:
 * Called By:
 * @param[in]
 * @param[out]
 * @return
 *  - 0 表示成功
 *  - 1 表示失败
 *
 * @author:
 * @date
 *
 * @par Modification History
 * @par Author:
 * @par Date:
 * @par Description:
 *
 * @see
 *
 */
/******************************************************************/
int log_deinit(struct ca_device *dev) {
    return 0;
}

void log_cb_set(func cb) {
    log.output_cb = cb;
}

#ifdef __cplusplus
}
#endif

/* @} F_LOG */
/* @} Robot_FAL */
