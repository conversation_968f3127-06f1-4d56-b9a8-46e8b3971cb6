/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:
 ** Author:         sulikang
 ** Version:        V0.0.1
 ** Date:           2021-3-25
 ** Description:
 ** Others:
 ** Function List:
 ** History:        2021-03 sulikang 创建
 ** <time>          <author>    <version >    <desc>
 ** 2021-3-25       sulikang    0.0.1         创建文件
 ******************************************************************/
#ifndef _FAL_LOG_H
#define _FAL_LOG_H

/*****************************************************************
 * 包含头文件
 ******************************************************************/
#include "elog.h"
#include "stdio.h"
#ifdef USING_SEGGER
#include "SEGGER_RTT.h"
#endif
/*****************************************************************
 * 宏定义
 ******************************************************************/
#define ELOG_ASYNC_POLL_GET_LOG_BUF_SIZE (ELOG_LINE_BUF_SIZE - 4)
/**
 * @ingroup F_LOG
 *
 * @brief \n
 * \n
 * @{
 */

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 结构定义
 ******************************************************************/
typedef void (*func)(char *log, size_t size);
typedef struct {
    func output_cb;
} Log_Typedef;
/*****************************************************************
 * 全局变量声明
 ******************************************************************/

/*****************************************************************
 * 函数原型声明
 ******************************************************************/

/*****************************************************************
功  能: 初始化 fal_log
返回值: 等于0表示成功，其它值表示失败原因
 *****************************************************************/
int log_init(void);

/*****************************************************************
功  能: 释放 fal_log 资源
返回值: 等于0表示成功，其它值表示失败原因
******************************************************************/
int log_deInit(void);

void log_cb_set(func cb);
/*****************************************************************
 * 函数说明
 ******************************************************************/

#ifdef __cplusplus
}
#endif

/* @} F_LOG*/

#endif
