#include "bldcm.h"
#include "devices.h"
#include "define_motor.h"
#include "tim_core.h"
#include "gpio_core.h"
#include "adc_core.h"
#include "mem_pool.h"
#include <stddef.h>
#include <string.h>
#include <stdlib.h>
#include "delay.h"
#include <math.h>
#include "pid.h"
#include "shell.h"
#ifdef USING_SEGGER
#include "SEGGER_RTT.h"
#endif

#define CwccwErrorMaxTime 100  //判断驱动轮方向io异常时间ms

typedef enum {
    BLDCM_MOTOR_LEFT,   //左轮电机
    BLDCM_MOTOR_RIGHT,  //右轮电机
    BLDCM_MOTOR_MAX     //最大支持电机数量(轴)
} BLDCM_MOTORS;

typedef struct {
    struct ca_bus *pwm;             //无刷直流电机pwm控制速度
    struct ca_bus *fg_ic;           //无刷直流电机速度反馈(输入捕获方式,三选一)
    struct ca_bus *fg_exti;         //无刷直流电机速度反馈(gpio中断方式,三选一)
    struct ca_bus *fg_etr;          //无刷直流电机速度反馈(etr方式,编码器,三选一)
    struct ca_bus *fg_encoding;     //无刷直流电机速度反馈(正交编码器方式,编码器)
    struct ca_bus *adc;             //无刷直流电机电流采样
    struct ca_bus *gpio_vcc;        //无刷直流电机电源控制
    struct ca_bus *gpio_brake;      //无刷直流电机刹车
    struct ca_bus *gpio_cw_ccw;     //无刷直流电机反转
    struct ca_bus *gpio_error_det;  //无刷直流电机异常引脚检测
} BLDCM_BUS;
struct bldcm_data_cb_node {
    struct list_struct node;
    MOTOR_DATA_CB_ARG  data_cb;
};

struct bldcm_args {
    int32_t           motor_num;                                //当前支持电机数量
    uint8_t           gpio_cw_ccw_status;                       //电机反转状态
    BLDCM_BUS         bldcm_bus[BLDCM_MOTOR_MAX];               //电机控制总线
    MOTOR_DATA        bldcm_data[BLDCM_MOTOR_MAX];              //电机数据
    MOTOR_PARAM       bldcm_param;                              //电机参数
    uint32_t          fg_interval[BLDCM_MOTOR_MAX];             //电机反馈中断间隔计数
    osThreadId_t      bldcm_thread[4];                          //处理线程id
    osMutexId_t       bldcm_mutex;                              //互斥锁
    osSemaphoreId_t   bldcm_semphore;                           //信号量
    int32_t           is_enable_current_detect;                 //是否使能电流检测，默认不使能
    int32_t           encoder_overflow_count[BLDCM_MOTOR_MAX];  //驱动轮fgtim进中断次数
    uint8_t           encoder_tim_entry_flag[BLDCM_MOTOR_MAX];  //驱动轮fgtim是否多次进入中断
    uint8_t           real_cw_ccw_status[BLDCM_MOTOR_MAX];      //左右电机反转状态
    PID_ATTR *        pid_parm;                                 // pid参数
    struct _pid       pid[BLDCM_MOTOR_MAX];                     // pid迭代参数的结构体
    MOTOR_DATA_CB_ARG data_cb;                                  //数据回调函数（一次性版本）
    uint8_t           bldcm_brakes_enable;                      //电机抱死开关
    volatile uint8_t  bldcm_disable_flag;                       // pwm失能状态
    float             bldcm_current_filtered_value[2];          // 电机滤波后电流
    uint8_t           brake_enable_state;                       //刹车脚使能状态
    uint8_t           open_state;                               //打开电机时PIN脚的状态
    uint8_t           gear[BLDCM_MOTOR_MAX];                    //电机当前控制的档位
    uint8_t           bldcm_urgent_brake_enable;  //电机急刹车使能 用于区分驱动轮减速到0速用急刹车还是缓刹车  置1为急刹车(顿挫但刹车距离短)
                                        //置0为缓刹车（不顿挫但刹车距离长）默认为0
    // struct list_struct data_cb_list;                         //数据回调函数链表
};

int32_t bldcm_get_current(const struct ca_device *dev, BLDCM_MOTORS motor, uint16_t *current);

int32_t bldcm_get_data(const struct ca_device *dev, MOTOR_DATA *data, uint32_t data_len);

void    bldcm_motor_pid_ctrl0(void *argument);
void    bldcm_motor_pid_ctrl1(void *argument);
int32_t bldcm_update_current_error(const struct ca_device *dev, uint32_t index);

int32_t bldcm_update_fg_error(const struct ca_device *dev, uint32_t index);