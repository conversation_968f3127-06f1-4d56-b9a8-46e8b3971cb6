/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:			 bldcm.c (brushless direct current motor 无刷直流电机)
 ** Author:         曾曼云
 ** Version:        V0.0.1
 ** Date:           2021-9-24
 ** Description:
 ** Others:
 ** Function List:
 ** History:        2021-09 曾曼云 创建
 ** <time>          <author>    <version >    <desc>
 ** 2021-3-25       sulikang    0.0.1         创建文件
 ******************************************************************/

/*****************************************************************
 * 包含头文件
 ******************************************************************/
#include "bldcm.h"
#include "devices.h"
#include "define_motor.h"
#include "tim_core.h"
#include "gpio_core.h"
#include "adc_core.h"
#include "mem_pool.h"
#include <stddef.h>
#include <string.h>
#include <stdlib.h>
#include "delay.h"
#define LOG_TAG "bldcm"
#include "log.h"
#include <math.h>
#include "pid.h"
#include "shell.h"
#ifdef USING_SEGGER
#include "SEGGER_RTT.h"
#endif
#include "bldcm_wheel.h"
#include "bldcm_core.h"
#include "tim.h"
/**
 * @addtogroup Robot_DEVICES
 * @{
 */

/**
 * @defgroup Robot_BLDCM  - 无刷直流电机
 *
 * @brief  \n
 * \n
 * @{
 */

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 私有宏定义
 ******************************************************************/

/*****************************************************************
 * 私有结构体/共用体/枚举定义
 ******************************************************************/
//无刷电机默认参数
const MOTOR_PARAM bldcm_default_param = {
    .motor_type    = MOTOR_TYPE_BLDCM,  //电机类型
    .motor_rpm_max = 100,               //电机最大转速
};

/*****************************************************************
 * 全局变量定义
 ******************************************************************/

/*****************************************************************
 * 私有全局变量定义
 ******************************************************************/
osThreadAttr_t bldcm_thread_attributes = {.name = "bldcm_thread", .priority = (osPriority_t) osPriorityNormal, .stack_size = 256 * 4};
#if defined(C3_WORKSTATION)
osThreadAttr_t bldcm_pub_thread_attributes = {.name = "bldcm_pub_thread", .priority = (osPriority_t) osPriorityNormal, .stack_size = 200 * 4};
#else
osThreadAttr_t bldcm_pub_thread_attributes = {.name = "bldcm_pub_thread", .priority = (osPriority_t) osPriorityNormal, .stack_size = 256 * 4};
#endif
// osThreadAttr_t bldcm_pub_thread_attributes = {.name = "bldcm_pub_thread", .priority = (osPriority_t) osPriorityNormal, .stack_size = 256 *
// 4};
osThreadAttr_t      bldcm_motor_ctrl_thread_attributes = {.name       = "bldcm_motor_ctrl_thread",
                                                     .priority   = (osPriority_t) osPriorityAboveNormal,
                                                     .stack_size = 256 * 4};
const osMutexAttr_t mutex_bldcm_attr                   = {
    "bldcm_mutex",                          // human readable mutex name
    osMutexRecursive | osMutexPrioInherit,  // attr_bits
    NULL,                                   // memory for control block
    0U                                      // size for control block
};
bool BLDCM_CORE_INIT = true;
/*****************************************************************
 * 外部变量声明
 ******************************************************************/

/*****************************************************************
 * 私有函数原型声明
 ******************************************************************/

/*****************************************************************
 * 函数定义
 ******************************************************************/
void bldcm_ic_callback(const struct ca_device *dev, void *handler) {
    // TODO:编码值
    uint8_t            index  = 0;
    struct bldcm_args *p_args = NULL;

    if ((NULL == dev) || (NULL == dev->device_args)) {
        return;
    }

    p_args = (struct bldcm_args *) dev->device_args;

    for (index = 0; index < p_args->motor_num; index++) {
        if ((NULL != p_args->bldcm_bus[index].fg_ic) && (p_args->bldcm_bus[index].fg_ic->handler == handler)) {
            p_args->fg_interval[index]++;
            break;
        }
    }
}

void bldcm_exti_callback(const struct ca_device *dev, uint16_t gpio_pin) {
    // TODO:编码值
    uint8_t            index  = 0;
    struct bldcm_args *p_args = NULL;

    if ((NULL == dev) || (NULL == dev->device_args)) {
        return;
    }

    p_args = (struct bldcm_args *) dev->device_args;

    for (index = 0; index < p_args->motor_num; index++) {
        if ((NULL != p_args->bldcm_bus[index].fg_exti) && (p_args->bldcm_bus[index].fg_exti->bus_addr == gpio_pin)) {
            p_args->fg_interval[index]++;
            break;
        }
    }
}

void bldcm_etr_reload_callback(const struct ca_device *dev, void *handler) {
    // TODO:编码值
    uint8_t            index  = 0;
    struct bldcm_args *p_args = NULL;

    if ((NULL == dev) || (NULL == dev->device_args)) {
        return;
    }

    p_args = (struct bldcm_args *) dev->device_args;

    for (index = 0; index < p_args->motor_num; index++) {
        if ((p_args->encoder_overflow_count[index] == 0) && (p_args->bldcm_bus[index].fg_etr->handler == handler))  //判断是否为第一次进中断
        {
            p_args->encoder_overflow_count[index] = 1;
            return;
        }
        if ((NULL != p_args->bldcm_bus[index].fg_etr) && (p_args->bldcm_bus[index].fg_etr->handler == handler)) {
            //编码值只能这里写，其他地方只能读
            if (p_args->real_cw_ccw_status[index] == 1)
                p_args->bldcm_data[index].encoding.encoding_num += tim_get_period(p_args->bldcm_bus[index].fg_etr);
            if (p_args->real_cw_ccw_status[index] == 0)
                p_args->bldcm_data[index].encoding.encoding_num -= tim_get_period(p_args->bldcm_bus[index].fg_etr);
            break;
        }
    }
}

void bldcm_encoding_reload_callback(const struct ca_device *dev, void *handler) {
    // TODO:编码值
    uint8_t            index  = 0;
    struct bldcm_args *p_args = NULL;

    if ((NULL == dev) || (NULL == dev->device_args)) {
        return;
    }

    p_args = (struct bldcm_args *) dev->device_args;

    for (index = 0; index < p_args->motor_num; index++) {
        if ((NULL != p_args->bldcm_bus[index].fg_encoding) && (p_args->bldcm_bus[index].fg_encoding->handler == handler)) {
            if (tim_encoder_direction(p_args->bldcm_bus[index].fg_encoding)) {
                p_args->encoder_overflow_count[index]--;
            } else {
                p_args->encoder_overflow_count[index]++;
            }
            if (p_args->encoder_tim_entry_flag[index] == 0) {
                p_args->encoder_tim_entry_flag[index] = 1;
                p_args->encoder_overflow_count[index] = 0;
            }
            // log_e("%d:encoder:%ld---h--------\n\r", index,
            //       p_args->encoder_overflow_count[index] * tim_get_period(p_args->bldcm_bus[index].fg_encoding) +
            //           tim_get_counter(p_args->bldcm_bus[index].fg_encoding));
            p_args->bldcm_data[index].encoding.encoding_num =
                p_args->encoder_overflow_count[index] * tim_get_period(p_args->bldcm_bus[index].fg_encoding) +
                tim_get_counter(p_args->bldcm_bus[index].fg_encoding);
        }
    }
}

//查找总线
int32_t bldcm_bus_find(MOTOR_BLDCM_BUS *motor_bus, BLDCM_BUS *bldcm_bus) {
    uint8_t          index      = 0;
    struct ca_bus ** pp_ca_bus  = NULL;
    struct bus_info *p_bus_info = NULL;

    if ((NULL == motor_bus) || (NULL == bldcm_bus)) {
        return -1;
    }

    memset(bldcm_bus, 0, sizeof(BLDCM_BUS));

    if (sizeof(MOTOR_BLDCM_BUS) / sizeof(struct bus_info) != sizeof(BLDCM_BUS) / sizeof(struct ca_bus *)) {
        return -1;
    }

    for (index = 0; index < sizeof(MOTOR_BLDCM_BUS) / sizeof(struct bus_info); index++) {
        p_bus_info = (struct bus_info *) ((void *) motor_bus + index * sizeof(struct bus_info));
        pp_ca_bus  = (struct ca_bus **) ((void *) bldcm_bus + index * sizeof(struct ca_bus *));

        if (0 != strlen((char *) p_bus_info->bus_name)) {
            bus_find_name(p_bus_info, pp_ca_bus);
        }
    }

    return 0;
}

int8_t bldcm_pwr_on(const struct ca_device *dev, int32_t motor);
int8_t bldcm_pwr_off(const struct ca_device *dev, int32_t motor);

#define MAX_POWER_MANAGERS 5  // 最多支持10个电源管理器
#define MAX_POWER_USERS    6  // 最多支持4个设备使用同一个电源

struct bldcm_power_manager {
    struct ca_bus gpio_vcc;      // 电源GPIO信息
    uint8_t       power_status;  // 电源状态 0-关闭 1-打开
    struct {
        const struct ca_device *dev;     // 使用该电源的设备
        BLDCM_MOTORS            motor;   // 对应的电机编号
        uint8_t                 in_use;  // 是否在使用
    } users[MAX_POWER_USERS];            // 使用该电源的设备列表
};

#ifdef USING_BLDCM_POWER_MANAGER
// 电源管理器数组,根据不同的gpio_vcc管理不同的电源
static struct bldcm_power_manager power_managers[MAX_POWER_MANAGERS];
static uint8_t                    power_manager_count = 0;

// 根据gpio_vcc查找或创建电源管理器
static struct bldcm_power_manager *find_power_manager(struct ca_bus *gpio_vcc) {
    uint8_t i;

    // 查找已存在的电源管理器
    for (i = 0; i < power_manager_count; i++) {
        if ((strcmp((const char *) power_managers[i].gpio_vcc.bus_name, (const char *) gpio_vcc->bus_name) == 0) &&
            (power_managers[i].gpio_vcc.bus_addr == gpio_vcc->bus_addr)) {
            log_d("found power manager[%d], vcc_name: %s, vcc_addr: %d", i, power_managers[i].gpio_vcc.bus_name,
                  power_managers[i].gpio_vcc.bus_addr);
            return &power_managers[i];
        }
    }

    // 创建新的电源管理器
    if (power_manager_count < MAX_POWER_MANAGERS) {
        strncpy((char *) power_managers[power_manager_count].gpio_vcc.bus_name, (char *) gpio_vcc->bus_name,
                sizeof(power_managers[power_manager_count].gpio_vcc.bus_name));
        power_managers[power_manager_count].gpio_vcc.bus_addr = gpio_vcc->bus_addr;
        power_managers[power_manager_count].power_status      = 0;
        memset(power_managers[power_manager_count].users, 0, sizeof(power_managers[power_manager_count].users));

        log_i("create new power manager[%d], vcc_name: %s, vcc_addr: %d", power_manager_count,
              power_managers[power_manager_count].gpio_vcc.bus_name, power_managers[power_manager_count].gpio_vcc.bus_addr);
        return &power_managers[power_manager_count++];
    } else {
        log_e("power manager count is max, can't create new power manager");
    }

    return NULL;
}

// 检查电源是否还有设备在使用
static uint8_t check_power_in_use(struct bldcm_power_manager *pm) {
    uint8_t i;
    for (i = 0; i < MAX_POWER_USERS; i++) {
        if (pm->users[i].in_use) {
            log_d("power manager[%d] already in use, dev: %s, motor: %d", i, pm->users[i].dev->device_name, pm->users[i].motor);
            return 1;
        }
    }
    return 0;
}

// 开启电源并记录使用者
static int32_t power_manager_on(const struct ca_device *dev, BLDCM_MOTORS motor) {
    struct bldcm_args *         p_args = dev->device_args;
    struct bldcm_power_manager *pm;
    uint8_t                     i;
    uint8_t                     was_power_off;

    if ((NULL == dev) || (NULL == dev->device_args)) {
        // log_d("%s %d want to power on, dev or dev->device_args is NULL", dev->device_name, motor);
        return -1;
    }

    if (NULL == p_args->bldcm_bus[motor].gpio_vcc) {
        log_d("%s %d want to power on, vcc ptr is NULL", dev->device_name, motor);
        return -1;
    }

    // 如果gpio_vcc的name为空，直接返回
    if (p_args->bldcm_bus[motor].gpio_vcc->bus_name[0] == '\0') {
        log_d("%s %d want to power on, vcc_name is NULL", dev->device_name, motor);
        return -1;
    }

    log_d("%s %d want to power on, vcc_name: %s, vcc_addr: %d", dev->device_name, motor, p_args->bldcm_bus[motor].gpio_vcc->bus_name,
          p_args->bldcm_bus[motor].gpio_vcc->bus_addr);

    pm = find_power_manager(p_args->bldcm_bus[motor].gpio_vcc);
    if (!pm) {
        return -1;
    }

    was_power_off = !check_power_in_use(pm);

    // 检查是否已经记录
    for (i = 0; i < MAX_POWER_USERS; i++) {
        if (pm->users[i].dev == dev && pm->users[i].motor == motor) {
            if (!pm->users[i].in_use) {
                pm->users[i].in_use = 1;

                if (was_power_off) {
                    pm->power_status = 1;
                    bldcm_pwr_on(dev, motor);

                    log_i("bldcm_pwr_on by------------------ %s %d", dev->device_name, motor);
                }
            }

            return 0;
        }
    }

    // 查找空闲位置记录新用户
    for (i = 0; i < MAX_POWER_USERS; i++) {
        if (!pm->users[i].in_use) {
            pm->users[i].dev    = dev;
            pm->users[i].motor  = motor;
            pm->users[i].in_use = 1;

            // 第一个用户时开启电源
            if (was_power_off) {
                pm->power_status = 1;
                bldcm_pwr_on(dev, motor);

                log_i("bldcm_pwr_on by------------------ %s %d", dev->device_name, motor);

                // 如果是风机，需要等待电源稳定
                if (strcmp((const char *) dev->device_name, "bldcm_fan") == 0) {
                    osDelay(50);
                }
            }
            return 0;
        }
    }

    return -1;  // 用户列表已满
}

// 关闭电源并清除使用记录
static int32_t power_manager_off(const struct ca_device *dev, BLDCM_MOTORS motor) {
    struct bldcm_args *         p_args = dev->device_args;
    struct bldcm_power_manager *pm;
    uint8_t                     i;

    if ((NULL == dev) || (NULL == dev->device_args)) {
        // log_d("%s %d want to power off, dev or dev->device_args is NULL", dev->device_name, motor);
        return -1;
    }

    if (NULL == p_args->bldcm_bus[motor].gpio_vcc) {
        log_d("%s %d want to power off, vcc ptr is NULL", dev->device_name, motor);
        return -1;
    }

    // 如果gpio_vcc的name为空，直接返回
    if (p_args->bldcm_bus[motor].gpio_vcc->bus_name[0] == '\0') {
        log_d("%s %d want to power off, vcc_name is NULL", dev->device_name, motor);
        return -1;
    }

    log_d("%s %d want to power off, vcc_name: %s, vcc_addr: %d", dev->device_name, motor, p_args->bldcm_bus[motor].gpio_vcc->bus_name,
          p_args->bldcm_bus[motor].gpio_vcc->bus_addr);

    pm = find_power_manager(p_args->bldcm_bus[motor].gpio_vcc);
    if (!pm) {
        log_d("%s %d want to power off, power manager not found", dev->device_name, motor);
        return -1;
    }

    // 查找并清除使用记录
    for (i = 0; i < MAX_POWER_USERS; i++) {
        if (pm->users[i].dev == dev && pm->users[i].motor == motor && pm->users[i].in_use) {
            pm->users[i].in_use = 0;

            // 检查是否还有其他设备在使用
            if (!check_power_in_use(pm)) {
                log_i("bldcm_pwr_off by-----------------> %s %d", dev->device_name, motor);

                // 如果是风机，需要等待pwm稳定
                if (strcmp((const char *) dev->device_name, "bldcm_fan") == 0) {
                    osDelay(50);
                }

                pm->power_status = 0;
                bldcm_pwr_off(dev, motor);

                return 1;  // 返回1表示电源被关闭
            }
            return 0;
        }
    }

    return -1;
}
#endif

int8_t bldcm_pwr_on(const struct ca_device *dev, int32_t motor) {
    uint8_t            state  = 0;
    struct bldcm_args *p_args = dev->device_args;

    if (NULL == p_args->bldcm_bus[motor].gpio_vcc) {
        log_d("open bdcm dev->bus NULL!");
        return -1;
    }

    state = p_args->open_state ? 1 : 0;
    gpio_write(p_args->bldcm_bus[motor].gpio_vcc, (void *) &state, 1);

    return 0;
}

int8_t bldcm_pwr_off(const struct ca_device *dev, int32_t motor) {
    uint8_t            state  = 0;
    struct bldcm_args *p_args = dev->device_args;

    if (NULL == p_args->bldcm_bus[motor].gpio_vcc) {
        log_d("close bdcm dev->bus NULL!");
        return -1;
    }

    state = p_args->open_state ? 0 : 1;
    gpio_write(p_args->bldcm_bus[motor].gpio_vcc, (void *) &state, 1);

    return 0;
}

int32_t bldcm_set_brake(const struct ca_device *dev, BLDCM_MOTORS motor, int32_t rpm) {
    struct bldcm_args *p_bldcm = NULL;
    p_bldcm                    = dev->device_args;
    uint8_t brake_status       = 0;
    uint8_t brake_arg          = 0;

    if ((NULL == dev) || (NULL == dev->device_args) || (NULL == p_bldcm->bldcm_bus[motor].gpio_brake) || (1 == p_bldcm->bldcm_disable_flag)) {
        return -1;
    }

    if (rpm == 0) {
        brake_status = (p_bldcm->brake_enable_state == 1) ? 1 : 0;
    } else {
        brake_status = (p_bldcm->brake_enable_state == 1) ? 0 : 1;
    }
    gpio_write(p_bldcm->bldcm_bus[motor].gpio_brake, (void *) &brake_status, sizeof(brake_status));

    return 0;
}

//设置电机转速
int32_t bldcm_set_rpm(const struct ca_device *dev, BLDCM_MOTORS motor, int32_t rpm) {
    struct bldcm_args *p_bldcm    = NULL;
    float              duty_cycle = 0;
    p_bldcm                       = dev->device_args;
    int cw_ccw_status             = 0;

    if ((NULL == dev) || (NULL == dev->device_args)) {
        return -1;
    }
    if (abs(rpm) > p_bldcm->bldcm_param.motor_rpm_max) {
        return -1;
    }
    bldcm_set_brake(dev, motor, rpm);

    if (p_bldcm->pid_parm == NULL) {
        if (rpm > 0) {
            cw_ccw_status = p_bldcm->gpio_cw_ccw_status ^ 1;

            gpio_write(p_bldcm->bldcm_bus[motor].gpio_cw_ccw, (void *) &cw_ccw_status, sizeof(cw_ccw_status));

            p_bldcm->bldcm_data[motor].open_flag = 1;
        }

        if (rpm < 0) {
            gpio_write(p_bldcm->bldcm_bus[motor].gpio_cw_ccw, (void *) &p_bldcm->gpio_cw_ccw_status, sizeof(p_bldcm->gpio_cw_ccw_status));

            p_bldcm->bldcm_data[motor].open_flag = 1;
        }

        if (rpm == 0) {
            p_bldcm->bldcm_data[motor].open_flag = 0;
        }
    }

    if (p_bldcm->bldcm_disable_flag == 1) {
        p_bldcm->bldcm_data[motor].rpm       = 0;
        p_bldcm->bldcm_data[motor].open_flag = 0;
        return 0;
    }

    //若开启pid闭环控制 则只设置期望rpm 具体控制在 实现
    if (p_bldcm->pid_parm != NULL) {
        p_bldcm->bldcm_data[motor].rpm = rpm;
        return 0;
    }

    //若未开启pid闭环控制 则直接根据rpm控制pwm
    //根据转速计算对应的占空比(实际转速≈最高转速*占空比，实际转速会随负载变化而变化)
    duty_cycle = (float) abs(rpm) / (float) p_bldcm->bldcm_param.motor_rpm_max;
    //设置占空比
    tim_pwm_set_duty_cycle(p_bldcm->bldcm_bus[motor].pwm, duty_cycle);
    //保存到数据中

    p_bldcm->bldcm_data[motor].rpm = rpm;

    return 0;
}

/**
 * Function:       speed_translate
 * Description:    速度转换，将线速度和角速度转换为下发给电机的 rpm(圈/分钟)
 */
static uint8_t motor_speed_translate(MOTOR_PARAM motor_param, MOTOR_SPEED motor_vw, float *rpm_l, float *rpm_r) {
    float rpm_v, rpm_turn;
    float rpm_unit = 0;

    if ((NULL == rpm_l) || (NULL == rpm_r)) {
        return -1;
    }

    rpm_unit = motor_param.wheel_diameter * PI / 60;  ///< 2*PI*R/60 --> 1 rpm 对应的 unit mm/s

    rpm_v = motor_vw.speed_v_t / rpm_unit;  ///< 直行速度

    rpm_turn = 1.0 * motor_vw.speed_w_t / 1000 * motor_param.wheel_space / 2 / rpm_unit;  ///< 转弯速度

    *rpm_l = rpm_v + rpm_turn;
    *rpm_r = rpm_v - rpm_turn;

    return 0;
}

//设置电机速度
int32_t bldcm_set_speed(const struct ca_device *dev, MOTOR_SPEED motor_vw) {
    struct bldcm_args *p_bldcm = NULL;
    float              rpm_min, rpm_max;
    float              rpm[BLDCM_MOTOR_MAX];
    int8_t             index = 0;

    if ((NULL == dev) || (NULL == dev->device_args)) {
        return -1;
    }

    p_bldcm = (struct bldcm_args *) dev->device_args;

    //判断电机参数是否设置正确
    if (p_bldcm->bldcm_param.wheel_diameter == 0 || p_bldcm->bldcm_param.wheel_space == 0) {
        return -2;
    }

    //先计算为转速，再设置
    motor_speed_translate(p_bldcm->bldcm_param, motor_vw, &rpm[BLDCM_MOTOR_LEFT], &rpm[BLDCM_MOTOR_RIGHT]);

    rpm_min = -1 * p_bldcm->bldcm_param.motor_rpm_max;
    rpm_max = 1 * p_bldcm->bldcm_param.motor_rpm_max;

    //底层限速
    for (index = 0; index < BLDCM_MOTOR_MAX; index++) {
        rpm[index] = (rpm[index] > rpm_max) ? (rpm_max) : (rpm[index]);
        rpm[index] = rpm[index] * p_bldcm->bldcm_param.motor_reduction_ratio;
        rpm[index] *= pow(-1, index);

        //设置转速
        bldcm_set_rpm(dev, index, rpm[index]);

        //保存到数据中
        if (osMutexAcquire(p_bldcm->bldcm_mutex, 0) == osOK) {
            memcpy(&p_bldcm->bldcm_data[index].speed, &motor_vw, sizeof(MOTOR_SPEED));
            osMutexRelease(p_bldcm->bldcm_mutex);
        }
    }

    return 0;
}

//电机电流设置
int32_t bldcm_set_current(const struct ca_device *dev, int32_t enable) {
    struct bldcm_args *p_bldcm = NULL;

    if ((NULL == dev) || (NULL == dev->device_args)) {
        return -1;
    }

    if (NULL != p_bldcm) {
        p_bldcm->is_enable_current_detect = enable;
    }

    return 0;
}

//电机参数设置
int32_t bldcm_set_param(const struct ca_device *dev, MOTOR_PARAM param) {
    struct bldcm_args *p_bldcm = NULL;

    if ((NULL == dev) || (NULL == dev->device_args)) {
        return -1;
    }

    p_bldcm = (struct bldcm_args *) dev->device_args;

    if (osMutexAcquire(p_bldcm->bldcm_mutex, 0) == osOK) {
        memcpy(&p_bldcm->bldcm_param, &param, sizeof(MOTOR_PARAM));

        // TODO:此处写到flash保存

        osMutexRelease(p_bldcm->bldcm_mutex);
    }

    return 0;
}

//电机参数读取
int32_t bldcm_get_param(const struct ca_device *dev, MOTOR_PARAM *param) {
    struct bldcm_args *p_bldcm = NULL;

    if ((NULL == dev) || (NULL == dev->device_args) || (NULL == param)) {
        return -1;
    }

    p_bldcm = (struct bldcm_args *) dev->device_args;

    if (osMutexAcquire(p_bldcm->bldcm_mutex, 0) == osOK) {
        memcpy(param, &p_bldcm->bldcm_param, sizeof(MOTOR_PARAM));

        osMutexRelease(p_bldcm->bldcm_mutex);
    }

    return 0;
}

//电机数据设置
int32_t bldcm_set_data(const struct ca_device *dev, MOTOR_DATA data) {
    return 0;
}

int32_t bldcm_set_pid(const struct ca_device *dev, PID_ATTR *pid_data) {
    struct bldcm_args *p_bldcm = NULL;
    if ((NULL == dev) || (NULL == dev->device_args)) {
        return -1;
    }

    p_bldcm = (struct bldcm_args *) dev->device_args;
    memcpy(p_bldcm->pid_parm, pid_data, sizeof(PID_ATTR));
    return 0;
}

//电机设置数据回调
int32_t bldcm_set_data_cb(const struct ca_device *dev, MOTOR_DATA_CB_ARG *cb_arg) {
    struct bldcm_args *p_bldcm = NULL;
    // struct bldcm_data_cb_node *p_callback = NULL;

    if ((NULL == dev) || (NULL == dev->device_args) || (NULL == cb_arg)) {
        return -1;
    }

    p_bldcm = (struct bldcm_args *) dev->device_args;

    // p_callback = mem_block_alloc(sizeof(struct bldcm_data_cb_node));
    // if (NULL == p_callback) {
    //     return -1;
    // }

    memcpy(&p_bldcm->data_cb, cb_arg, sizeof(MOTOR_DATA_CB_ARG));

    // list_add_tail(&p_callback->node, &p_bldcm->data_cb_list);

    return 0;
}

int32_t bldcm_get_run_time(const struct ca_device *dev, uint32_t *arg) {
    struct bldcm_args *p_args = NULL;
    p_args                    = (struct bldcm_args *) dev->device_args;
    uint8_t  index            = 0;
    uint32_t tmp              = 0;

    if ((NULL == arg) || (NULL == p_args)) {
        return -1;
    }
    for (index = 0; index < p_args->motor_num; index++) {
        if (p_args->bldcm_data[index].run_time > tmp) {
            tmp = p_args->bldcm_data[index].run_time;
        }
    }

    memcpy(arg, &tmp, sizeof(uint32_t));

    return 0;
}

int32_t bldcm_update_current_value(const struct ca_device *dev, MOTOR_CURRENT_INFO *current_info) {
    struct bldcm_args *p_args = NULL;
    p_args                    = (struct bldcm_args *) dev->device_args;

    if ((NULL == current_info) || (NULL == current_info->current_value)) {
        return -1;
    }

    current_info->open_flag = p_args->bldcm_data[current_info->motor_num].open_flag;

    return bldcm_get_current(dev, current_info->motor_num, current_info->current_value);
}

static void bldcm_pub_run(void *argument) {
    struct ca_device * dev     = (struct ca_device *) argument;
    struct bldcm_args *p_bldcm = NULL;
    // struct list_struct *       p_list     = NULL;
    // struct bldcm_data_cb_node *p_callback = NULL;

    MOTOR_DATA buffer[2];

    if ((NULL == dev) || (NULL == dev->bus) || (NULL == dev->device_args)) {
        return;
    }
    p_bldcm = dev->device_args;

    uint32_t tick = osKernelGetTickCount();
    while (1) {
        // p_list = &p_bldcm->data_cb_list;
        // while (list_is_last(p_list, &p_bldcm->data_cb_list) != 1) {
        //     p_callback = (struct bldcm_data_cb_node *) p_list->next;
        //     if (NULL != p_callback->data_cb.fn_callback) {

        if (is_timeout(tick, p_bldcm->data_cb.period) && p_bldcm->data_cb.fn_callback != NULL) {
            tick = osKernelGetTickCount();
            bldcm_get_data(dev, buffer, 2 * sizeof(MOTOR_DATA));
            p_bldcm->data_cb.fn_callback(buffer, 2 * sizeof(MOTOR_DATA));
        }

        // if ((tick++) % p_bldcm->data_cb.period == 0 && p_bldcm->data_cb.fn_callback != NULL) {
        //     bldcm_get_data(dev, buffer, 2 * sizeof(MOTOR_DATA));
        //     p_bldcm->data_cb.fn_callback(buffer, 2 * sizeof(MOTOR_DATA));
        // }

        //     }
        //     p_list = p_list->next;
        // }
        osDelay(1);
    }
}

int32_t bldcm_open(const struct ca_device *dev, int32_t flags) {
    return 0;
}

int32_t bldcm_close(const struct ca_device *dev) {
    if (NULL == dev) {
        return -1;
    }
    struct bldcm_args *p_bldcm_args = NULL;
    p_bldcm_args                    = (struct bldcm_args *) dev->device_args;
    osThreadSuspend(p_bldcm_args->bldcm_thread[1]);
    osThreadSuspend(p_bldcm_args->bldcm_thread[2]);
    osThreadSuspend(p_bldcm_args->bldcm_thread[3]);
    return 0;
}

int32_t bldcm_read(const struct ca_device *dev, void *buffer, uint32_t size) {
    // if (size != sizeof(MOTOR_DATA)) {
    //     return -1;
    // }
    return bldcm_get_data(dev, (MOTOR_DATA *) buffer, size);
}

int32_t bldcm_write(const struct ca_device *dev, void *buffer, uint32_t size) {
    if ((NULL == dev) || (NULL == dev->bus) || (NULL == dev->bus->handler) || (NULL == buffer)) {
        return -1;
    }

    return 0;
}

int32_t bldcm_get_gpio_error(const struct ca_device *dev, BLDCM_MOTORS motor) {
    struct bldcm_args *p_bldcm = NULL;
    p_bldcm                    = dev->device_args;
    int32_t gpio_state         = 0;

    gpio_read(p_bldcm->bldcm_bus[motor].gpio_error_det, (void *) &gpio_state, sizeof(gpio_state));

    return gpio_state;
}

/*****************************************************************/
/**
 * Function:       bldcm_ioctl
 * Description:    无刷直流电机控制指令
 * Calls:
 * Called By:
 * @param[in]
 * @param[out]
 * @return
 *  - 0 表示成功
 *  - 1 表示失败
 *
 * @author:
 * @date
 *
 * @par Modification History
 * @par Author:
 * @par Date:
 * @par Description:
 *
 * @see
 *
 */
/******************************************************************/
int32_t bldcm_ioctl(const struct ca_device *dev, uint32_t cmd, void *arg) {
    int8_t             index        = 0;
    struct bldcm_args *p_args       = NULL;
    uint8_t            brake_status = 0;
    p_args                          = (struct bldcm_args *) dev->device_args;
    MOTOR_CURRENT_INFO *m_info      = NULL;
    MOTOR_RPM_INFO *    m_rpm_info  = NULL;
    int32_t             ret         = 0;
    if (NULL == dev) {
        return -1;
    }

    switch (cmd) {
        case MOTOR_CMD_START:
            if (arg != NULL) {
                bldcm_pwr_on(dev, *(int32_t *) arg);
            }
            break;

        case MOTOR_CMD_STOP:
            if (arg != NULL) {
                bldcm_pwr_off(dev, *(int32_t *) arg);
            }
            break;

        case MOTOR_CMD_SET_SPEED:  //设置速度mm/s

            bldcm_set_speed(dev, *(MOTOR_SPEED *) arg);

            break;

        case MOTOR_CMD_SET_RPM: {  // 设置档位
            int32_t gear = 0;
            for (index = 0; index < p_args->motor_num; index++) {
                gear = pow(-1, index) * *(int32_t *) arg;

#ifdef USING_BLDCM_POWER_MANAGER
                if (gear != 0 && p_args->bldcm_disable_flag == 0) {
                    power_manager_on(dev, index);
                }
#endif

                bldcm_set_rpm(dev, index, gear);

#ifdef USING_BLDCM_POWER_MANAGER
                if (gear == 0 || p_args->bldcm_disable_flag == 1) {
                    power_manager_off(dev, index);
                }
#endif

                p_args->gear[index] = abs(gear);
            }
            break;
        }

        case MOTOR_CMD_GET_RPM: {  // 获取档位
            return p_args->gear[*(uint8_t *) arg];
            break;
        }

        case MOTOR_CMD_SET_PARAM:  //设置参数

            bldcm_set_param(dev, *(MOTOR_PARAM *) (arg));

            break;
        case MOTOR_CMD_GET_PARAM:  //读取参数

            bldcm_get_param(dev, (MOTOR_PARAM *) arg);

            break;
        case MOTOR_CMD_RESET:  //复位

            break;
        case MOTOR_CMD_ENABLE:
            for (index = 0; index < p_args->motor_num; index++) {
                tim_pwm_start(p_args->bldcm_bus[index].pwm, 0, 0);
                if (p_args->bldcm_bus[index].gpio_brake != NULL) {
                    brake_status = (p_args->brake_enable_state == 1) ? 0 : 1;
                    gpio_write(p_args->bldcm_bus[index].gpio_brake, (void *) &brake_status, sizeof(brake_status));
                }
                if (p_args->bldcm_data[index].rpm != 0) {
                    p_args->bldcm_data[index].tick = osKernelGetTickCount();
                }

#ifdef USING_BLDCM_POWER_MANAGER
                // 急停解除，恢复使能后，有速度控制会在MOTOR_CMD_SET_RPM中恢复电源
                // power_manager_on(dev, index);
#endif
            }
            p_args->bldcm_disable_flag = 0;
            break;
        case MOTOR_CMD_DISABLE:

            // 先设置标志，再将PWM设置为0，避免置零后刚好切到其他线程，pwm被改为了非零指
            p_args->bldcm_disable_flag = 1;

            for (index = 0; index < p_args->motor_num; index++) {
                tim_pwm_set_duty_cycle(p_args->bldcm_bus[index].pwm, 0);
                //设置占空比为0即可保持输出低电平，stop 会导致保持高电平输出。（风机PWM测试发现）
                // tim_pwm_stop(p_args->bldcm_bus[index].pwm);
                //若电机有配置刹车角 则失能电机时要把刹车角拉低
                if (p_args->bldcm_bus[index].gpio_brake != NULL) {
                    brake_status = (p_args->brake_enable_state == 1) ? 1 : 0;
                    gpio_write(p_args->bldcm_bus[index].gpio_brake, (void *) &brake_status, sizeof(brake_status));
                }
                p_args->bldcm_data[index].open_flag = 0;
                p_args->gear[index]                 = 0;

#ifdef USING_BLDCM_POWER_MANAGER
                power_manager_off(dev, index);
#endif
            }
            // p_args->bldcm_disable_flag = 1;
            break;
        case MOTOR_CMD_GET_CURRENT:
            for (index = 0; index < BLDCM_MOTOR_MAX; index++) {
                bldcm_update_current_error(dev, index);
            }
            break;
        case MOTOR_CMD_GET_CURRENT_VALUE:
            ret = bldcm_update_current_value(dev, (MOTOR_CURRENT_INFO *) arg);
            break;
        case MOTOR_CMD_SET_CURRENT:
            p_args->is_enable_current_detect = *(int32_t *) arg;
            break;
        case MOTOR_CMD_BRAKE_ENABLE:
            p_args->bldcm_brakes_enable = 1;
            break;
        case MOTOR_CMD_BRAKE_DISABLE:
            p_args->bldcm_brakes_enable = 0;
            break;
        case MOTOR_CMD_SET_BRAKE:
            for (index = 0; index < p_args->motor_num; index++) {
                bldcm_set_brake(dev, index, p_args->bldcm_data[index].rpm);
            }
            break;
        case MOTOR_CMD_SET_DATA_CB:
            bldcm_set_data_cb(dev, (MOTOR_DATA_CB_ARG *) arg);
            break;
        case MOTOR_CMD_SET_DETECT_TIME:
            p_args->bldcm_data->detect_time = *(uint16_t *) arg;
            break;

        case MOTOR_CMD_GET_RUN_TIME:
            bldcm_get_run_time(dev, (uint32_t *) arg);
            break;
        case MOTOR_CMD_SET_CURRENT_FILTERED_VALUE:
            m_info                                                  = (MOTOR_CURRENT_INFO *) arg;
            p_args->bldcm_current_filtered_value[m_info->motor_num] = *m_info->filtered_current_value;
            break;
        case MOTOR_CMD_GET_CURRENT_FILTERED_VALUE:
            m_info                          = (MOTOR_CURRENT_INFO *) arg;
            *m_info->filtered_current_value = p_args->bldcm_current_filtered_value[m_info->motor_num];
            m_info->open_flag               = p_args->bldcm_data[m_info->motor_num].open_flag;
            break;
        case MOTOR_CMD_GET_FG:
            m_rpm_info            = (MOTOR_RPM_INFO *) arg;
            *m_rpm_info->fg_value = p_args->bldcm_data[m_rpm_info->motor_num].fg_value;
            break;
        case MOTOR_CMD_GET_GPIO_ERROR:
            return bldcm_get_gpio_error(dev, *(int32_t *) arg);
            break;

            // case MOTOR_CMD_SET_PID:
            //     bldcm_set_pid(dev, (PID_ATTR *) arg);
            //     break;
        case MOTOR_CMD_GET_OPEN_STATE: {
            uint8_t motor_num = *((uint8_t *) arg);
            return p_args->bldcm_data[motor_num].open_flag;
            break;
        }
        case MOTOR_CMD_URGENT_BRAKE_ENABLE:
            p_args->bldcm_urgent_brake_enable = 1;
            break;
        case MOTOR_CMD_URGENT_BRAKE_DISABLE:
            p_args->bldcm_urgent_brake_enable = 0;
            break;
        default:
            return -1;
    }
    return ret;
}

/*****************************************************************/
/**
 * Function:       bldcm_init
 * Description:    初始化 bldcm
 * Calls:
 * Called By:
 * @param[in]
 * @param[out]
 * @return
 *  - 0 表示成功
 *  - 1 表示失败
 *
 * @author:
 * @date
 *
 * @par Modification History
 * @par Author:
 * @par Date:
 * @par Description:
 *
 * @see
 *
 */
/******************************************************************/
int32_t bldcm_init(struct ca_device *dev) {
    struct bldcm_args *p_args       = NULL;
    MOTOR_BLDCM_ATTR * p_bldcm_attr = NULL;
    MOTOR_BLDCM_BUS *  p_bus        = NULL;
    uint8_t            index        = 0;
    char               Thread_name[50];

    if ((NULL == dev) || (NULL == dev->bus) || (NULL == dev->device_args)) {
        return -1;
    }

    if (BLDCM_CORE_INIT) {
        bldcm_info_update_run_init();
        BLDCM_CORE_INIT = false;
    }

    p_bldcm_attr = dev->device_args;
    if ((p_bldcm_attr->bldcm_nums <= 0) && (p_bldcm_attr->bldcm_nums > BLDCM_MOTOR_MAX)) {
        return -1;
    }

    p_bus = p_bldcm_attr->bldcm_bus;
    if (NULL == p_bus) {
        return -1;
    }

    memset(&dev->ops, 0, sizeof(struct ca_device_ops));
    dev->ops.open  = bldcm_open;
    dev->ops.close = bldcm_close;
    dev->ops.read  = bldcm_read;
    dev->ops.write = bldcm_write;
    dev->ops.ioctl = bldcm_ioctl;

    p_args = (struct bldcm_args *) mem_block_alloc(sizeof(struct bldcm_args));
    if (NULL == p_args) {
        return -1;
    }

    memset(p_args, 0, sizeof(struct bldcm_args));
    p_args->motor_num             = p_bldcm_attr->bldcm_nums;
    p_args->gpio_cw_ccw_status    = p_bldcm_attr->gpio_cw_ccw_status;
    p_args->real_cw_ccw_status[0] = p_bldcm_attr->gpio_cw_ccw_status;
    p_args->real_cw_ccw_status[1] = p_bldcm_attr->gpio_cw_ccw_status ^ 0x01;
    p_args->pid_parm              = p_bldcm_attr->pid_parm;
    p_args->bldcm_brakes_enable   = p_bldcm_attr->brake_enable_state;
    p_args->open_state            = p_bldcm_attr->open_state;
    for (index = 0; index < p_bldcm_attr->bldcm_nums; index++) {
        bldcm_bus_find(&p_bus[index], &p_args->bldcm_bus[index]);

        if (NULL != p_args->bldcm_bus[index].fg_ic) {
            tim_ic_capture_register_callback(dev, p_args->bldcm_bus[index].fg_ic->handler, p_args->bldcm_bus[index].fg_ic->bus_addr,
                                             bldcm_ic_callback);
        }

        if (NULL != p_args->bldcm_bus[index].fg_exti) {
            gpio_exti_register_callback(dev, p_args->bldcm_bus[index].fg_exti->bus_addr, bldcm_exti_callback);
        }

        if (NULL != p_args->bldcm_bus[index].fg_encoding) {
            tim_period_elapsed_register_callback(dev, p_args->bldcm_bus[index].fg_encoding->handler, bldcm_encoding_reload_callback);
            tim_encoder_start(p_args->bldcm_bus[index].fg_encoding);
            tim_period_elapsed_start(p_args->bldcm_bus[index].fg_encoding);
        }
        if (NULL != p_args->bldcm_bus[index].pwm) {
            tim_pwm_start(p_args->bldcm_bus[index].pwm, 0, 0);  // pwm置0，防止上电就转
        }
    }
    dev->device_args = (void *) p_args;
    // list_head_init(&p_args->data_cb_list);
    p_args->bldcm_semphore = osSemaphoreNew(1, 0U, NULL);
    //信息上报线程
    memset(Thread_name, 0, sizeof(Thread_name));
    sprintf(Thread_name, "%s_%s", (char *) dev->device_name, "pub");
    bldcm_pub_thread_attributes.name = Thread_name;
    p_args->bldcm_thread[1]          = osThreadNew(bldcm_pub_run, (void *) dev, &bldcm_pub_thread_attributes);

    if (p_args->pid_parm != NULL) {
        // pid闭环控制线程
        memset(Thread_name, 0, sizeof(Thread_name));
        sprintf(Thread_name, "%s_%s", (char *) dev->device_name, "pid_ctrl0");
        bldcm_motor_ctrl_thread_attributes.name = Thread_name;
        p_args->bldcm_thread[2]                 = osThreadNew(bldcm_motor_pid_ctrl0, (void *) dev, &bldcm_motor_ctrl_thread_attributes);
        sprintf(Thread_name, "%s_%s", (char *) dev->device_name, "pid_ctrl1");
        bldcm_motor_ctrl_thread_attributes.name = Thread_name;
        p_args->bldcm_thread[3]                 = osThreadNew(bldcm_motor_pid_ctrl1, (void *) dev, &bldcm_motor_ctrl_thread_attributes);
    }
    bldcm_info_update_run_register(dev);
    p_args->bldcm_mutex = osMutexNew(&mutex_bldcm_attr);
    // TODO:参数配置
    memcpy(&p_args->bldcm_param, &bldcm_default_param, sizeof(MOTOR_PARAM));

#ifdef USING_BLDCM_POWER_MANAGER
    // 初始化时关闭电机电源
    for (index = 0; index < p_args->motor_num; index++) {
        bldcm_pwr_off(dev, index);
    }
#endif

    return 0;
}

/*****************************************************************/
/**
 * Function:       bldcm_deinit
 * Description:    去初始化 bldcm
 * Calls:
 * Called By:
 * @param[in]
 * @param[out]
 * @return
 *  - 0 表示成功
 *  - 1 表示失败
 *
 * @author:
 * @date
 *
 * @par Modification History
 * @par Author:
 * @par Date:
 * @par Description:
 *
 * @see
 *
 */
/******************************************************************/
int32_t bldcm_deinit(struct ca_device *dev) {
    return 0;
}

DEVICE_DRIVER_INIT(bldcm, tim, bldcm_init);

#ifdef __cplusplus
}
#endif

/* @} Robot_BLDCM */
/* @} Robot_DEVICES */
