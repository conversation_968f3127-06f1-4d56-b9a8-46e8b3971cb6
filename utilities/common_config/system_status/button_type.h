#ifndef BUTTON_TYPE_H
#define BUTTON_TYPE_H

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 按键接口类型枚举定义
 ******************************************************************/
typedef enum {
    BUTTON_TYPE_UNKNOWN = 0,  // 未知类型
    BUTTON_TYPE_IIC,          // I2C接口按键
    BUTTON_TYPE_DIRECT,       // 直连GPIO按键接口
    BUTTON_TYPE_DIRECT2,      // 直连GPIO按键接口（赛特）
    BUTTON_TYPE_AUTO          // 自动选择版本
} button_type_e;

/*****************************************************************
 * 全局变量声明
 ******************************************************************/
extern button_type_e button_type;

/*****************************************************************
 * 函数声明
 ******************************************************************/
/**
 * @brief 将字符串转换为按键类型枚举
 * @param str 按键类型字符串
 * @return 对应的按键类型枚举值
 */
button_type_e button_type_str_to_enum(const char *str);

/**
 * @brief 将按键类型枚举转换为字符串
 * @param type 按键类型枚举值
 * @return 对应的字符串，如果类型未知返回"unknown"
 */
const char *button_type_enum_to_str(button_type_e type);

#ifdef __cplusplus
}
#endif

#endif // BUTTON_TYPE_H
