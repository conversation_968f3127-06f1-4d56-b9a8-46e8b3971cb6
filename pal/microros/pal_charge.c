/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:
 ** Author:         曾曼云
 ** Version:        V0.0.1
 ** Date:           2021-3-25
 ** Description:		microROS充电和电池
 ** Others:
 ** Function List:
 ** History:        2021-11 曾曼云 创建
 ** <time>          <author>    <version >    <desc>
 ** 2021-3-25       曾曼云						1.0         创建文件
 ******************************************************************/

/*****************************************************************
 * 包含头文件
 ******************************************************************/
#define LOG_TAG "pal_charge"
#include "log.h"
#include "pal_charge.h"
#include "pal_zbus.h"
#include "devices.h"
#include "define.h"
#include <stdarg.h>
#include <stdio.h>
#include <string.h>
#include "fal_charge.h"
#include "std_msgs/msg/u_int8.h"
#include "std_msgs/msg/u_int16.h"
#include "std_msgs/msg/empty.h"
#include "std_msgs/msg/bool.h"
#include "std_msgs/msg/float32.h"
#include "std_msgs/msg/u_int8_multi_array.h"
#include "chassis_interfaces/srv/pile_request.h"
#include "std_srvs/srv/set_bool.h"
#include "sensor_msgs/msg/range.h"
#include "shell.h"
/**
 * @addtogroup Robot_PAL 协议适配层 - PAL
 * @{
 */

/**
 * @defgroup Robot_PAL_UROS microROS接口处理
 *
 * @brief
 * \n
 * @{
 */

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 私有宏定义
 ******************************************************************/

/*****************************************************************
 * 私有结构体/共用体/枚举定义
 ******************************************************************/

/*****************************************************************
 * 全局变量定义
 ******************************************************************/

/*****************************************************************
 * 私有全局变量定义
 ******************************************************************/
static publisher                      g_uros_ir_message;
static std_msgs__msg__UInt8MultiArray uros_ir_message;
static publisher                      g_uros_ir_position;
static sensor_msgs__msg__Range        uros_ir_position;
static publisher                      g_uros_charge_feedback;
static std_msgs__msg__UInt8           uros_charge_feedback;
static publisher                      g_uros_cable_state;
static std_msgs__msg__Bool            uros_cable_state;

static subscrption          g_uros_charge_feedback_update;
static std_msgs__msg__Empty uros_charge_feedback_update;

static subscrption           g_pile_request_id;
static std_msgs__msg__UInt16 pile_request_id = {0};

static service                                g_pile_request;
chassis_interfaces__srv__PileRequest_Request  pile_request_req = {0};
chassis_interfaces__srv__PileRequest_Response pile_request_res = {0};

static service                  g_charge_control;
std_srvs__srv__SetBool_Request  charge_control_req = {0};
std_srvs__srv__SetBool_Response charge_control_res = {0};

static publisher                      g_bluetooth_addr;
static std_msgs__msg__UInt8MultiArray bluetooth_addr;

static subscrption          g_bluetooth_addr_update;
static std_msgs__msg__Empty bluetooth_addr_update;
/*****************************************************************
 * 外部变量声明（如果全局变量没有在其它的H文件声明，引用时需在此处声明，
 *如果已在其它H文件声明，则只需包含此H文件即可）
 ******************************************************************/
extern int charge_en_handle;

/*****************************************************************
 * 私有函数原型声明
 ******************************************************************/
static void ir_message_pub_callback(void *data);
static void ir_position_pub_callback(void *data);
static void charge_feedback_pub_callback(void *data);
static void bluetooth_addr_pub_callback(void *data);

static void pile_request_service_callback(const void *req, void *res);
static void charge_control_service_callback(const void *req, void *res);
static void pile_request_id_callback(const void *msgin);
static void bluetooth_addr_update_subscription_callback(const void *msgin);
static void charge_feedback_update_subscription_callback(const void *msgin);
/*****************************************************************
 * 函数定义
 ******************************************************************/

/*****************************************************************
 * 函数定义
 *注意，编写函数需首先定义所有的局部变量等，不允许在
 *函数的中间出现新的变量的定义。
 ******************************************************************/
int pal_charge_init(void) {
    publisher_init(&g_uros_ir_message, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, UInt8MultiArray), "/auto_charge/ir_message", &uros_ir_message,
                   BEST, OVERWRITE, sizeof(std_msgs__msg__UInt8MultiArray));

    publisher_init(&g_uros_ir_position, ROSIDL_GET_MSG_TYPE_SUPPORT(sensor_msgs, msg, Range), "/auto_charge/ir_position", &uros_ir_position,
                   BEST, OVERWRITE, sizeof(sensor_msgs__msg__Range));

    publisher_init(&g_uros_charge_feedback, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, UInt8), "/auto_charge/charge_feedback",
                   &uros_charge_feedback, BEST, SEND, sizeof(std_msgs__msg__UInt8));

    publisher_init(&g_uros_cable_state, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Bool), "/auto_charge/cable_state", &uros_cable_state, BEST,
                   SEND, sizeof(std_msgs__msg__Bool));

    subscrption_init(&g_bluetooth_addr_update, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Empty), "/auto_charge/bluetooth/addr/update",
                     &bluetooth_addr_update, BEST, bluetooth_addr_update_subscription_callback);

    subscrption_init(&g_uros_charge_feedback_update, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Empty), "/auto_charge/charge_feedback/update",
                     &uros_charge_feedback_update, BEST, charge_feedback_update_subscription_callback);

    subscrption_init(&g_pile_request_id, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, UInt16), "/auto_charge/pile_request", &pile_request_id,
                     BEST, pile_request_id_callback);

    publisher_init(&g_bluetooth_addr, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, UInt8MultiArray), "/auto_charge/bluetooth/addr",
                   &bluetooth_addr, BEST, OVERWRITE, sizeof(std_msgs__msg__UInt8MultiArray));

    service_init(&g_pile_request, ROSIDL_GET_SRV_TYPE_SUPPORT(chassis_interfaces, srv, PileRequest), "/auto_charge/pile_request",
                 &pile_request_req, &pile_request_res, BEST, pile_request_service_callback);

    service_init(&g_charge_control, ROSIDL_GET_SRV_TYPE_SUPPORT(std_srvs, srv, SetBool), "/auto_charge/charge_control", &charge_control_req,
                 &charge_control_res, BEST, charge_control_service_callback);

    charge_pub_cb_set(ir_message_pub_callback, IR_MESSAGE);
    charge_pub_cb_set(ir_position_pub_callback, IR_POSITION);
    charge_pub_cb_set(charge_feedback_pub_callback, CHARGE_FEEDBACK);
    charge_pub_cb_set(bluetooth_addr_pub_callback, BLUETOOTH_ADDR);
    return 0;
}
PAL_MODULE_INIT(pal_charge_init);

static void ir_message_pub_callback(void *data) {
    static uint8_t ir_message_buff[2] = {0};
    memcpy(ir_message_buff, data, 2);
    uros_ir_message.data.data = ir_message_buff;
    uros_ir_message.data.size = sizeof(ir_message_buff);
    message_publish(&g_uros_ir_message);
}

static void ir_position_pub_callback(void *data) {
    uint8_t ir_message_buff[2] = {0};
    memcpy(ir_message_buff, data, 2);
    uros_ir_position.min_range = -1.0;
    uros_ir_position.max_range = 1.0;

    switch (ir_message_buff[0]) {
        case 0xAE:
            uros_ir_position.range = 0.0;
            break;
        case 0xEE:
            uros_ir_position.range = -0.3;
            break;
        case 0xAF:
            uros_ir_position.range = 0.3;
            break;
        case 0xFE:
            uros_ir_position.range = -0.8;
            break;
        case 0xBF:
            uros_ir_position.range = 0.8;
            break;
        default:
            return;
    }

    log_i("ir_range:%.1f", uros_ir_position.range);
    message_publish(&g_uros_ir_position);
}

static void charge_feedback_pub_callback(void *data) {
    static uint8_t charge_feedback_data = 0;
    memcpy(&charge_feedback_data, data, 1);
    uros_charge_feedback.data = charge_feedback_data;
    message_publish(&g_uros_charge_feedback);
}

static void bluetooth_addr_pub_callback(void *data) {
    static uint8_t bluetooth_addr_buff[10] = {0};
    memcpy(bluetooth_addr_buff, data, 10);
    bluetooth_addr.data.data = bluetooth_addr_buff;
    bluetooth_addr.data.size = sizeof(bluetooth_addr_buff);
    message_publish(&g_bluetooth_addr);
}

static void pile_request_id_callback(const void *msgin) {
    std_msgs__msg__UInt16 *req_in = (std_msgs__msg__UInt16 *) msgin;

    log_i("request pile_id: %d", (int) req_in->data);

    auto_charge_event_start();
    pile_id_set(req_in->data);
}

static void pile_request_service_callback(const void *req, void *res) {
    chassis_interfaces__srv__PileRequest_Request * req_in = (chassis_interfaces__srv__PileRequest_Request *) req;
    chassis_interfaces__srv__PileRequest_Response *res_in = (chassis_interfaces__srv__PileRequest_Response *) res;

    log_i("Service request pile_id: %d", (int) req_in->pile_id);

    if (req_in->pile_id == 0xffff) {
        auto_charge_event_end();
    } else {
        auto_charge_event_start();
        pile_id_set(req_in->pile_id);
    }

    res_in->success = 1;
}

static void charge_control_service_callback(const void *req, void *res) {
    std_srvs__srv__SetBool_Request * req_in = (std_srvs__srv__SetBool_Request *) req;
    std_srvs__srv__SetBool_Response *res_in = (std_srvs__srv__SetBool_Response *) res;

    fal_charge_control_en(req_in->data);
    log_i("charge_control: %d", req_in->data);
    res_in->success = 1;
}

static void bluetooth_addr_update_subscription_callback(const void *msgin) {
    log_i("/auto_charge/bluetooth/addr/update");
    bluetooth_pub_set(true);
}

static void charge_feedback_update_subscription_callback(const void *msgin) {
    log_i("/auto_charge/charge_feedback/update");
    message_publish(&g_uros_charge_feedback);
}
#ifdef __cplusplus
}
#endif

/* @} Robot_PAL_UROS */
/* @} Robot_PAL */
