/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:
 ** Author:         曾曼云
 ** Version:        V0.0.1
 ** Date:           2021-3-25
 ** Description:		microROS导航组件
 ** Others:
 ** Function List:
 ** History:        2021-11 曾曼云 创建
 ** <time>          <author>    <version >    <desc>
 ** 2021-3-25       曾曼云						1.0         创建文件
 ******************************************************************/

/*****************************************************************
 * 包含头文件
 ******************************************************************/
#define LOG_TAG "pal_navigation"
#include "log.h"
#include "pal_navigation.h"
#include "fal.h"
#include "devices.h"
#include "define_motor.h"
#include "define.h"
#include <stdarg.h>
#include <stdio.h>
#include <string.h>
#include "shell.h"
#include "pal_zbus.h"
#include "nav_msgs/msg/odometry.h"
#include "geometry_msgs/msg/twist.h"
#include "cvte_sensor_msgs/msg/encoding.h"
#include "std_msgs/msg/string.h"
#include "cJSON.h"
#include "mem_pool.h"
#ifdef USING_SEGGER
#include "SEGGER_RTT.h"
#endif
#include "fal_security.h"

/**
 * @addtogroup Robot_PAL 协议适配层 - PAL
 * @{
 */

/**
 * @defgroup Robot_PAL_UROS microROS接口处理
 *
 * @brief
 * \n
 * @{
 */

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 私有宏定义
 ******************************************************************/
#define VEL_LOG_TICK 2000
/*****************************************************************
 * 私有结构体/共用体/枚举定义
 ******************************************************************/
//森创默认参数
MOTOR_PARAM wheel_param = {.motor_type            = MOTOR_TYPE_BLDCM,  //电机类型
                           .wheel_diameter        = 95,                //车轮直径/mm
                           .wheel_space           = 350,               //车轮间距/mm
                           .motor_reduction_ratio = 1,                 //电机减速比
                           .motor_encoding_circle = 256 * 50.6 * 4,    //电机转一圈位置增加总量
                           .motor_rpm_max         = 1000,              //电机最大转速
                           .motor_acc_up          = 0,                 //电机控制上升加速度      mm/s^2
                           .motor_acc_down        = 0,                 //电机控制下降加速度      mm/s^2
                           .motor_pole            = 0,                 //电机极对数
                           .motor_mmps_max        = 0,                 //最大速度 mm/s
                           .motor_current_max     = 150};

/*****************************************************************
 * 全局变量定义
 ******************************************************************/

/*****************************************************************
 * 私有全局变量定义
 ******************************************************************/
static publisher               g_uros_odom;
static nav_msgs__msg__Odometry uros_odom = {0};

static publisher                       g_uros_encoding;
static cvte_sensor_msgs__msg__Encoding uros_encoding;

static int64_t encoding_buffer[2];

///< motor speed ctrl
static subscrption               g_motor_ctrl;
static geometry_msgs__msg__Twist motor_ctrl = {0};

/*****************************************************************
 * 外部变量声明（如果全局变量没有在其它的H文件声明，引用时需在此处声明，
 *如果已在其它H文件声明，则只需包含此H文件即可）
 ******************************************************************/
extern int wheel_handle;
/*****************************************************************
 * 私有函数原型声明
 ******************************************************************/

/*****************************************************************
 * 函数定义
 ******************************************************************/

/*****************************************************************
 * 函数定义
 *注意，编写函数需首先定义所有的局部变量等，不允许在
 *函数的中间出现新的变量的定义。
 ******************************************************************/
void navi_motor_data_callback(const MOTOR_DATA *data, uint32_t size) {
    if ((NULL == data) || (size < 2 * sizeof(MOTOR_DATA))) {
        return;
    }

    uros_encoding.encoding_num.data[1] = -1 * data[0].encoding.encoding_num;
    uros_encoding.encoding_circle      = data[0].encoding.encoding_circle;

    uros_encoding.encoding_num.data[0] = data[1].encoding.encoding_num;
    uros_encoding.encoding_circle      = data[1].encoding.encoding_circle;

    pal_zbus_ros_msg_set_timestamp(&uros_encoding.header.stamp);
    message_publish(&g_uros_encoding);
}

void navigation_set_speed_callback(const void *msgin) {
    const geometry_msgs__msg__Twist *msg = (const geometry_msgs__msg__Twist *) msgin;
    MOTOR_SPEED                      speed;
    static uint32_t                  log_tick = 0;

    if (is_timeout(log_tick, VEL_LOG_TICK)) {
        log_tick = osKernelGetTickCount();
        log_i("/vel_mcu: v[%d] w[%d]", (int32_t)(msg->linear.x * 1000), (int32_t)(msg->angular.z * 1000));
    }

    speed.speed_v_t = (int16_t)(msg->linear.x * 1000);
    speed.speed_w_t = (int16_t)(msg->angular.z * 1000);
    motor_speed_update(&speed);
    // device_ioctl(wheel_handle, MOTOR_CMD_SET_SPEED, (void *) &speed);
}

int pal_navigation_init(void) {
    publisher_init(&g_uros_odom, ROSIDL_GET_MSG_TYPE_SUPPORT(nav_msgs, msg, Odometry), "/odom", &uros_odom, BEST, OVERWRITE,
                   sizeof(nav_msgs__msg__Odometry));

    publisher_init(&g_uros_encoding, ROSIDL_GET_MSG_TYPE_SUPPORT(cvte_sensor_msgs, msg, Encoding), "/encoding", &uros_encoding, BEST, OVERWRITE,
                   sizeof(cvte_sensor_msgs__msg__Encoding));

    subscrption_init(&g_motor_ctrl, ROSIDL_GET_MSG_TYPE_SUPPORT(geometry_msgs, msg, Twist), "/vel_mcu", &motor_ctrl, BEST,
                     navigation_set_speed_callback);

    uros_encoding.encoding_num.data     = encoding_buffer;
    uros_encoding.encoding_num.capacity = 0;
    uros_encoding.encoding_num.size     = sizeof(encoding_buffer) / sizeof(encoding_buffer[0]);

    MOTOR_DATA_CB_ARG *cb = (MOTOR_DATA_CB_ARG *) mem_block_alloc(sizeof(MOTOR_DATA_CB_ARG));

    cb->fn_callback = navi_motor_data_callback;
    cb->period      = 10;
    device_ioctl(wheel_handle, MOTOR_CMD_SET_DATA_CB, (void *) cb);
#ifdef BLDCM_DRIVER
    // C3直流设置参数
    device_ioctl(wheel_handle, MOTOR_CMD_SET_PARAM, (void *) &wheel_param);
#endif
    //设置循环检测电流
    // int32_t eable = 1;
    // device_ioctl(wheel_handle, MOTOR_CMD_SET_CURRENT , (void *)&eable);

    return 0;
}
PAL_MODULE_INIT(pal_navigation_init);

int pal_navigation_deInit(void) {
    return 0;
}

#ifdef __cplusplus
}
#endif

/* @} Robot_PAL_UROS */
/* @} Robot_PAL */
