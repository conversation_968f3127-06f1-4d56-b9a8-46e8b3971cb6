#define LOG_TAG "pal_taskcontrol"
#include "log.h"
#include "pal_task_control.h"
#include "fal_task_control.h"
#include "fal_tall_led.h"
#include "cmsis_os.h"
#include "shell.h"
#include "std_msgs/msg/string.h"
#include "std_msgs/msg/bool.h"
#include "chassis_interfaces/msg/navi_work_status.h"
#include "chassis_interfaces/msg/map_work_status.h"
#include "chassis_interfaces/srv/get_string_map.h"
#include "chassis_interfaces/msg/string_maps.h"
#include "string.h"
#include "pubsub.h"
#include "cJSON.h"
#include "utils_tick.h"
#include "fal_security.h"
#include "fal_button.h"
#include "define_cliff.h"
#include "pal_cliff.h"
#include "button_type.h"
#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 私有宏定义
 ******************************************************************/

/*****************************************************************
 * 私有结构体/共用体/枚举定义
 ******************************************************************/
static publisher             g_key_state;
static std_msgs__msg__String key_state;

static subscrption                             g_nav_work_status;
static chassis_interfaces__msg__NaviWorkStatus nav_work_status_info;

static subscrption                            g_map_work_status;
static chassis_interfaces__msg__MapWorkStatus map_work_status_info;

static subscrption           g_area;
static std_msgs__msg__String area;

static subscrption           g_avoid_cliff_pulse;
static std_msgs__msg__String avoid_cliff_pulse_info;

static subscrption         g_aromath_switch;
static std_msgs__msg__Bool aromath_switch;

static subscrption         g_headlight_switch;
static std_msgs__msg__Bool headlight_switch;

#define CFG_KV_SIZE 5

static subscrption g_devicecfg_cg;

static client                                         g_client_get_device_cfg;
static chassis_interfaces__srv__GetStringMap_Request  g_get_device_cfg_request;
static chassis_interfaces__srv__GetStringMap_Response g_get_device_cfg_response;
static rosidl_runtime_c__String                       cfg_key_arr[CFG_KV_SIZE];
static chassis_interfaces__msg__StringMap             cfg_kv[CFG_KV_SIZE];

static subscrption                         g_device_cfg_cg;
static chassis_interfaces__msg__StringMaps cfg_kv_cg;

/*****************************************************************
 * 全局变量定义
 ******************************************************************/

/*****************************************************************
 * 私有全局变量定义
 ******************************************************************/

/*****************************************************************
 * 外部变量声明（如果全局变量没有在其它的H文件声明，引用时需在此处声明，
 *如果已在其它H文件声明，则只需包含此H文件即可）
 ******************************************************************/
bool clean_task_just_start_enable;
char last_work_status_str[32] = {0};

avoid_cliff_info_t avoid_cliff_info[AVOID_CLIFF_INFO_NUM_MAX];

const osThreadAttr_t aviod_cliff_handle_attr = {.name       = "aviod_cliff_handle",
                                                .priority   = (osPriority_t) osPriorityNormal,
                                                .stack_size = 256 * 4};

extern void aromatherapy_control(device_status_t state);
extern void position_light_control(device_status_t state);

extern int battery_handle;
/*****************************************************************
 * 私有函数原型声明
 ******************************************************************/
static void work_status_subscription_callback(const void *msgin);
static void map_status_subscription_callback(const void *msgin);
static void area_subscription_callback(const void *msgin);
static void avoid_cliff_pulse_subscription_callback(const void *msgin);
static void aromath_switch_subscription_callback(const void *msgin);
static void headlight_switch_subscription_callback(const void *msgin);
/*****************************************************************
 * 函数定义
 *注意，编写函数需首先定义所有的局部变量等，不允许在
 *函数的中间出现新的变量的定义。
 ******************************************************************/

void aviod_cliff_source_save(char *source) {
    uint8_t     idle_index   = AVOID_CLIFF_INFO_NUM_MAX;
    uint8_t     i            = 0;
    static bool is_recv_full = false;

    for (uint8_t i = 0; i < AVOID_CLIFF_INFO_NUM_MAX; i++) {
        if (avoid_cliff_info[i].source[0] == '\0') {
            idle_index = i;
            continue;
        }
        if (strncmp(avoid_cliff_info[i].source, source, AVOID_CLIFF_SOURCE_LEN_MAX - 1) == 0) {
            avoid_cliff_info[i].time_stamp = osKernelGetTickCount();
            return;
        }
    }

    // 数组中没有, 需要更新进数组
    if (idle_index >= AVOID_CLIFF_INFO_NUM_MAX) {
        // 数组已满
        if (is_recv_full != false) {
            return;
        }
        is_recv_full = true;
        log_e("recv avoid_source:%s, but now is full, drop it!!!", source);
        return;
    }

    is_recv_full                            = false;
    avoid_cliff_info[idle_index].time_stamp = osKernelGetTickCount();
    strncpy(avoid_cliff_info[idle_index].source, source, AVOID_CLIFF_SOURCE_LEN_MAX - 1);
    avoid_cliff_info[idle_index].source[AVOID_CLIFF_SOURCE_LEN_MAX - 1] = '\0';
    log_w("recv new avoid_source:%s,save:%s,in %d", source, avoid_cliff_info[idle_index].source, idle_index);
}

void aviod_cliff_handle(void *argument) {
    bool is_aviod_cliff_det      = false;
    bool is_last_aviod_cliff_det = false;
    security_start(CLIFF_BIT);
    while (1) {
        is_aviod_cliff_det = false;
        for (uint8_t i = 0; i < AVOID_CLIFF_INFO_NUM_MAX; i++) {
            if (avoid_cliff_info[i].source[0] == '\0') {
                continue;
            }

            if (is_timeout(avoid_cliff_info[i].time_stamp, AVOID_CLIFF_TIMEOUT_MAX)) {
                log_w("avoid_source:%s,timeout, del it!!!", avoid_cliff_info[i].source);
                avoid_cliff_info[i].source[0] = '\0';
            } else {
                is_aviod_cliff_det = true;
            }
        }

        if (is_last_aviod_cliff_det != is_aviod_cliff_det) {
            if (is_aviod_cliff_det) {
                log_w("security_stop cliff");
                security_stop(CLIFF_BIT);
            } else {
                log_w("security_start cliff");
                security_start(CLIFF_BIT);
            }
        }
        is_last_aviod_cliff_det = is_aviod_cliff_det;
        osDelay(5);
    }
}

volatile bool is_get_device_cfg_finish = false;

void client_get_device_cfg_callback(const void *msg) {
    // g_get_device_info_response = *((chassis_interfaces__srv__PileGetDevInfo_Response *) msg);

    if (strcmp(g_get_device_cfg_request.keys.data[0].data, g_get_device_cfg_response.maps.data[0].key.data) != 0) {
        // log_e("ask key[%s] not found!", g_get_device_cfg_request.keys.data[0].data);
    } else {
        /*
        log_i("client_get_device_cfg[%s]: code[%d] message[%s] key[%s] value[%s]", g_get_device_cfg_request.keys.data[0].data,
              g_get_device_cfg_response.code, g_get_device_cfg_response.message.data, g_get_device_cfg_response.maps.data[0].key.data,
              g_get_device_cfg_response.maps.data[0].value.data);
        */
    }

    is_get_device_cfg_finish = true;
}

extern char          emerg_type_str[32];
extern button_type_e button_type;

void device_cfg_cg_callback(const void *msg) {
    for (int i = 0; i < CFG_KV_SIZE; i++) {
        if (cfg_kv_cg.maps.data[i].key.data) {
            log_i("device_cfg changed : key[%s] value[%s]", cfg_kv_cg.maps.data[i].key.data, cfg_kv_cg.maps.data[i].value.data);

            if (strcmp("system.trigger_emerg_type", cfg_kv_cg.maps.data[i].key.data) == 0) {
                strcpy(emerg_type_str, cfg_kv_cg.maps.data[i].value.data);
            }

            if (strcmp("hardware.key.interface", cfg_kv_cg.maps.data[i].key.data) == 0) {
                button_type = button_type_str_to_enum(cfg_kv_cg.maps.data[i].value.data);
            }

            // 读取 sensors.psd_cali1 ～ sensors.psd_cali6 的值，更新对应cliff_sensor_objs
            if (strstr(cfg_kv_cg.maps.data[i].key.data, "sensors.psd_cali")) {
                char *cliff_num = cfg_kv_cg.maps.data[i].key.data + strlen("sensors.psd_cali");
                char  cliff_name[32];
                snprintf(cliff_name, sizeof(cliff_name), "cliff_oml%s", cliff_num);

                extern adc_sensor_obj_st cliff_sensor_objs[];
                extern int               cliff_handle;

                for (int j = 0; j < CLIFF_MAX; j++) {
                    if (strcmp((const char *) cliff_sensor_objs[j].dev_name, cliff_name) == 0) {
                        cliff_sensor_objs[j].adc_vol_thres = atof(cfg_kv_cg.maps.data[i].value.data);
                        log_i("Update %s vol threshold to %.2f", cliff_name, cliff_sensor_objs[j].adc_vol_thres);

                        device_ioctl(cliff_handle, CLIFF_CMD_SET_VOL_THRES, (void *) &cliff_sensor_objs[j]);

                        break;
                    }
                }
            }

            memset(cfg_kv_cg.maps.data[i].key.data, 0, cfg_kv_cg.maps.data[i].key.capacity);
            memset(cfg_kv_cg.maps.data[i].value.data, 0, cfg_kv_cg.maps.data[i].value.capacity);
        }
    }
}

int pal_task_control_init(void) {
    publisher_init(&g_key_state, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, String), "/key_state", &key_state, BEST, OVERWRITE,
                   sizeof(std_msgs__msg__String));

    subscrption_init(&g_nav_work_status, ROSIDL_GET_MSG_TYPE_SUPPORT(chassis_interfaces, msg, NaviWorkStatus),
                     "/navi_manager/state_publisher/work_status", &nav_work_status_info, BEST, work_status_subscription_callback);

    subscrption_init(&g_map_work_status, ROSIDL_GET_MSG_TYPE_SUPPORT(chassis_interfaces, msg, MapWorkStatus),
                     "/map_manager/state_publisher/work_status", &map_work_status_info, BEST, map_status_subscription_callback);

    subscrption_init(&g_area, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, String), "/area", &area, BEST, area_subscription_callback);

    subscrption_init(&g_avoid_cliff_pulse, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, String), "/avoid_cliff_pulse", &avoid_cliff_pulse_info,
                     BEST, avoid_cliff_pulse_subscription_callback);
    MallocString(&avoid_cliff_pulse_info.data, REQ, 128);
    subscrption_init(&g_aromath_switch, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Bool), "aromatherapy_switch", &aromath_switch, BEST,
                     aromath_switch_subscription_callback);
    subscrption_init(&g_headlight_switch, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Bool), "/headlight/switch", &headlight_switch, BEST,
                     headlight_switch_subscription_callback);

    MallocString(&map_work_status_info.work_status, REQ, 32);
    /*为了接受带多个string类型*/
    MallocString(&map_work_status_info.map_create_time, REQ, 32);

    MallocString(&nav_work_status_info.work_status, REQ, 32);
    MallocString(&nav_work_status_info.navigation_state, REQ, 32);
    /*为了接受带多个string类型*/
    MallocString(&nav_work_status_info.lift_state, REQ, 32);

    MallocString(&area.data, REQ, 32);

    client_init(&g_client_get_device_cfg, ROSIDL_GET_SRV_TYPE_SUPPORT(chassis_interfaces, srv, GetStringMap), "/device_info/config/get",
                &g_get_device_cfg_request, &g_get_device_cfg_response, BEST, client_get_device_cfg_callback, sizeof(g_get_device_cfg_request));

    g_get_device_cfg_request.keys.data = cfg_key_arr;
    g_get_device_cfg_request.keys.size = 1;
    MallocString(&cfg_key_arr[0], REQ, 32);

    g_get_device_cfg_response.maps.data     = cfg_kv;
    g_get_device_cfg_response.maps.capacity = CFG_KV_SIZE;
    MallocString(&g_get_device_cfg_response.message, REQ, 32);

    for (int i = 0; i < CFG_KV_SIZE; i++) {
        MallocString(&cfg_kv[i].key, REQ, 32);
        MallocString(&cfg_kv[i].value, REQ, 32);
    }

    /*
    MallocString(&cfg_kv[0].key, REQ, 32);
    MallocString(&cfg_kv[0].value, REQ, 32);
    MallocString(&cfg_kv[1].key, REQ, 32);
    MallocString(&cfg_kv[1].value, REQ, 32);
    MallocString(&cfg_kv[2].key, REQ, 32);
    MallocString(&cfg_kv[2].value, REQ, 32);
    MallocString(&cfg_kv[3].key, REQ, 32);
    MallocString(&cfg_kv[3].value, REQ, 32);
    MallocString(&cfg_kv[4].key, REQ, 32);
    MallocString(&cfg_kv[4].value, REQ, 32);
    */

    log_i("--------------------maps[%p]--------------------------", g_get_device_cfg_response.maps.data);

    subscrption_init(&g_device_cfg_cg, ROSIDL_GET_MSG_TYPE_SUPPORT(chassis_interfaces, msg, StringMaps), "/device_info/config/changed",
                     &cfg_kv_cg, BEST, device_cfg_cg_callback);

    cfg_kv_cg.maps.capacity = CFG_KV_SIZE;
    cfg_kv_cg.maps.data     = cfg_kv;

    // 线程解析
    osThreadNew(aviod_cliff_handle, NULL, &aviod_cliff_handle_attr);

    return 0;
}
PAL_MODULE_INIT(pal_task_control_init);

//在没有响应时，此接口会阻塞等待1秒
int ask_cfg(const char *key, char *val) {
    uint32_t timeout = 0;
    // strncpy(g_get_device_cfg_request.keys.data[0].data, item, sizeof(*(g_get_device_cfg_request.keys.data[0].data)));
    // log_i("--------------------maps[%p][%p][%p][%p]---", &g_get_device_cfg_response, (&(g_get_device_cfg_response.maps)),
    //(&(g_get_device_cfg_response.maps.data)), g_get_device_cfg_response.maps.data);

    strcpy(g_get_device_cfg_request.keys.data[0].data, key);

    g_get_device_cfg_response.maps.data[0].key.data[0] = '\0';

    is_get_device_cfg_finish = false;

    client_message_send(&g_client_get_device_cfg);

    while (!is_get_device_cfg_finish) {
        osDelay(100);

        if (timeout++ >= 10) {
            return -1;
        }
    }

    if (strcmp(g_get_device_cfg_request.keys.data[0].data, g_get_device_cfg_response.maps.data[0].key.data) != 0) {
        log_e("ask key[%s] not found!", g_get_device_cfg_request.keys.data[0].data);

        return -2;

    } else {
        log_i("client_get_device_cfg[%s]: code[%d] message[%s] key[%s] value[%s]", g_get_device_cfg_request.keys.data[0].data,
              g_get_device_cfg_response.code, g_get_device_cfg_response.message.data, g_get_device_cfg_response.maps.data[0].key.data,
              g_get_device_cfg_response.maps.data[0].value.data);

        if (val) {
            strcpy(val, g_get_device_cfg_response.maps.data[0].value.data);
        }

        return 0;
    }
}

SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), ask_cfg, ask_cfg, ask_cfg);

void key_state_pub(char *key, char *state) {
    cJSON *root       = NULL;
    char * string_msg = NULL;

    if (key == NULL || state == NULL)
        return;

    root = cJSON_CreateObject();
    if (root != NULL) {
        cJSON_AddStringToObject(root, "key", key);
        cJSON_AddStringToObject(root, "state", state);
        string_msg = cJSON_PrintUnformatted(root);
        if (string_msg == NULL) {
            cJSON_Delete(root);
            log_w("can't create publisher msg on line :%d", __LINE__);
            return;
        } else {
            key_state.data.data     = string_msg;
            key_state.data.size     = strlen(string_msg);
            key_state.data.capacity = 0;
            message_publish(&g_key_state);

            // 临时解决异步 publish 局部变量提前释放的问题
            // osDelay(20);

            cJSON_free(string_msg);
            cJSON_Delete(root);
        }
    } else {
        log_w("creat json obj failed on line %d\r\n", __LINE__);
    }
}

#include "fal_led.h"
#include "fal_charge.h"
#include "devices.h"
#include "define_bms.h"
// 使用全局变量，避免大量数据出栈入栈
BMS_DATA battery_temp = {0};

static void work_status_subscription_callback(const void *msgin) {
    if (msgin == NULL) {
        return;
    }
    const chassis_interfaces__msg__NaviWorkStatus *pnavi_state_msg = (const chassis_interfaces__msg__NaviWorkStatus *) msgin;

    if (0 == strncmp(pnavi_state_msg->work_status.data, "task_navigating", strlen("task_navigating"))) {
        if (0 != strncmp(last_work_status_str, "task_navigating", strlen("task_navigating"))) {
            clean_task_just_start_enable = true;
            log_d("is task_navigating : true");
            clear_motor_led_state();
        }
    } else {
        clean_task_just_start_enable = false;  //防止在自清洁阶段停止任务
        log_d("not is task_navigating : false");
    }

    // 由其他状态切换到任务导航状态，闪烁一下蓝灯（2秒）
    if (0 != strncmp(last_work_status_str, "task_navigating", strlen("task_navigating")) &&
        0 == strncmp(pnavi_state_msg->work_status.data, "task_navigating", strlen("task_navigating"))) {
        cycle_led_t task_led_info;
        task_led_info.cycle      = 200;
        task_led_info.duty_cycle = 0.5;

        device_ioctl(battery_handle, BMS_GET_DATA, &battery_temp);
        // 是否电量低
        if (battery_temp.bms_percentage < LOW_BATTERY_POWER) {
            priority_control_set(&led_control, INFO_LVL + INFO_LVL, led_red_cycle, &task_led_info, "task");
        } else {
            priority_control_set(&led_control, INFO_LVL + INFO_LVL, led_blue_cycle, &task_led_info, "task");
        }
    }

    memcpy(last_work_status_str, pnavi_state_msg->work_status.data, strlen(pnavi_state_msg->work_status.data));
    log_d("work_status      :%s", pnavi_state_msg->work_status.data);
    log_d("navigation_state :%s", pnavi_state_msg->navigation_state.data);
    PS_PUB_STR_FL("work_status", pnavi_state_msg->work_status.data, PS_FL_STICKY);
    PS_PUB_STR_FL("navigation_state", pnavi_state_msg->navigation_state.data, PS_FL_STICKY);
}

static void map_status_subscription_callback(const void *msgin) {
    if (msgin == NULL) {
        return;
    }
    const chassis_interfaces__msg__MapWorkStatus *pmap_state_msg = (const chassis_interfaces__msg__MapWorkStatus *) msgin;

    log_d("map_status      :%s", pmap_state_msg->work_status.data);
    PS_PUB_STR_FL("map_status", pmap_state_msg->work_status.data, PS_FL_STICKY);
}

static void area_subscription_callback(const void *msgin) {
    if (msgin == NULL) {
        return;
    }

    const std_msgs__msg__String *area_msg = (const std_msgs__msg__String *) msgin;
    log_d("area :%s", area_msg->data.data);

    if (!strncmp(area_msg->data.data, "elevator", strlen("elevator")) || !strncmp(area_msg->data.data, "pit", strlen("pit"))) {
        aviod_cliff_source_save(area_msg->data.data);
    }
}

static void avoid_cliff_pulse_subscription_callback(const void *msgin) {
    if (msgin == NULL) {
        return;
    }
    cJSON *root      = NULL;
    cJSON *json_temp = NULL;

    const std_msgs__msg__String *avoid_cliff_pulse_info = (const std_msgs__msg__String *) msgin;
    log_d("avoid_cliff_pulse_info:%s", avoid_cliff_pulse_info->data.data);

    root      = cJSON_Parse(avoid_cliff_pulse_info->data.data);
    json_temp = cJSON_GetObjectItemCaseSensitive(root, "name");
    if (!json_temp) {
        log_e("parse name error!");
        cJSON_Delete(root);
        return;
    }
    aviod_cliff_source_save(json_temp->valuestring);
    cJSON_Delete(root);
}

static void aromath_switch_subscription_callback(const void *msgin) {
    if (msgin == NULL) {
        return;
    }

    if (!get_is_tall_version()) {
        log_e("it's not tall version, aromatherapy control error");
        return;
    }

    const std_msgs__msg__Bool *aromath_switch_msg = (const std_msgs__msg__Bool *) msgin;

    aromath_switch.data = aromath_switch_msg->data;

    log_i("aromath_switch :[%d]", aromath_switch.data);

    if (aromath_switch.data) {
        aromatherapy_control(OPEN);
    } else {
        aromatherapy_control(CLOSE);
    }
}

static void headlight_switch_subscription_callback(const void *msgin) {
    if (msgin == NULL) {
        return;
    }

    if (!get_is_tall_version()) {
        log_e("it's not tall version, headlight control error");
        return;
    }

    const std_msgs__msg__Bool *headlight_switch_msg = (const std_msgs__msg__Bool *) msgin;

    headlight_switch.data = headlight_switch_msg->data;

    log_i("headlight_switch :[%d]", headlight_switch.data);

    if (headlight_switch.data) {
        position_light_control(OPEN);
    } else {
        position_light_control(CLOSE);
    }
}

#ifdef __cplusplus
}
#endif