/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:
 ** Author:         曾曼云
 ** Version:        V0.0.1
 ** Date:           2021-3-25
 ** Description:		microROS清洁电机控制
 ** Others:
 ** Function List:
 ** History:        2021-11 曾曼云 创建
 ** <time>          <author>    <version >    <desc>
 ** 2021-3-25       曾曼云						1.0
 *创建文件
 ******************************************************************/

/*****************************************************************
 * 包含头文件
 ******************************************************************/
#define LOG_TAG "pal_motor"
#include "log.h"
#include "pal_motor.h"
#include "fal.h"
#include "devices.h"
#include "define.h"
#include <stdarg.h>
#include <stdio.h>
#include <string.h>
#include "pal_zbus.h"
#include "shell.h"
#include "define_motor.h"
#include "gpio.h"
#include "std_msgs/msg/string.h"
#include "std_msgs/msg/empty.h"
#include "cJSON.h"
#include "sensor_msgs/msg/range.h"
#include "chassis_interfaces/msg/detect_result.h"
#include "fal_motor.h"
#include "utils_tick.h"
#include "fal_security.h"
#include "fal_clean.h"
#include "pal_cliff.h"
#include "fal_common.h"
#include "std_msgs/msg/u_int16.h"
/**
 * @addtogroup Robot_PAL 协议适配层 - PAL
 * @{
 */

/**
 * @defgroup Robot_PAL_UROS microROS接口处理
 *
 * @brief
 * \n
 * @{
 */

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 私有宏定义
 ******************************************************************/

/*****************************************************************
 * 私有结构体/共用体/枚举定义
 ******************************************************************/

/*****************************************************************
 * 全局变量定义
 ******************************************************************/
static subscrption           g_clean_component_control;
static std_msgs__msg__String clean_component_control = {0};
static char                  clean_component_control_buffer[512];

static subscrption           g_component_state_update_sub;
static std_msgs__msg__String component_state_update_info   = {0};
bool                         component_state_update_enable = true;

static SECURITY_CB_T motor_cb_attr        = {0};
static SECURITY_CB_T clean_button_cb_attr = {0};
static SECURITY_CB_T clean_sensor_cb_attr = {0};

/* 电控Mcu设备JSON名 */
static char *mcu_dev_name_pub[MOTOR_MAX];
static char *mcu_clean_dev_name[BUTTON_CLEAN_MODULE_MAX];
static char *mcu_sensor_dev_name[SENSOR_MAX];

static uint8_t change_clean_module_rpm_flag = 0;                    //临时措施：用app改变执行任务的清洁组建rpm的大小标志
static char *  sewage_valve_name            = "waste_water_state";  // app控制排污阀下发的键值
/*****************************************************************
 * 私有全局变量定义
 ******************************************************************/
/* 单个清洁组件控制信息 */
static subscrption    g_single_clean_component_ctrl;
std_msgs__msg__String single_clean_component_ctrl_info;

/* 清洁设备状态 */
static publisher      g_clean_dev_status;
std_msgs__msg__String clean_dev_status_info;

static publisher      g_light_det;
std_msgs__msg__UInt16 light_det_value;

clean_ctrl_component_t clean_ctrl_comp_info = {0};
/*****************************************************************
 * 外部变量声明（如果全局变量没有在其它的H文件声明，引用时需在此处声明，
 *如果已在其它H文件声明，则只需包含此H文件即可）
 ******************************************************************/
extern int clean_water_pump_handle;
extern int gpio_ctl_cwde_handle;
extern int wheel_handle;
extern int side_brush_handle;
extern int roller_brush_handle;
extern int roller_tube_handle;
extern int clean_water_pump_handle;
extern int sewage_water_pump_handle;
extern int fan_motor_handle;
extern int fan_rpm;
extern int sewage_valve_handle;
extern int ruller_tube_rpm;
extern int side_rush_rpm;
extern int ruller_rush_rpm;
/*****************************************************************
 * 私有函数原型声明
 ******************************************************************/

/*****************************************************************
 * 函数定义
 ******************************************************************/

/*****************************************************************
 * 函数定义
 *注意，编写函数需首先定义所有的局部变量等，不允许在
 *函数的中间出现新的变量的定义。
 ******************************************************************/

int fan_vcc_ctrl(uint8_t enable) {
    int32_t motorNum = 0;
    if (enable) {
        device_ioctl(fan_motor_handle, MOTOR_CMD_START, &motorNum);
    } else {
        device_ioctl(fan_motor_handle, MOTOR_CMD_STOP, &motorNum);
    }

    return 0;
}
int cwde_vcc_ctrl(uint8_t enable) {
    if (enable) {
        device_ioctl(gpio_ctl_cwde_handle, GPIO_ACTIVE_HIGH, NULL);
    } else {
        device_ioctl(gpio_ctl_cwde_handle, GPIO_ACTIVE_LOW, NULL);
    }

    return 0;
}

int clean_ops_test(uint8_t clean_module_index, uint8_t speed_switch) {
    log_d("clean_module_index = %d,  speed_switch = %d", clean_module_index, speed_switch);
    int32_t motorNum = 0;
    int     rpm      = speed_switch * 10;
    switch (clean_module_index) {
        case UP_DOWM_PUSH_ROD:
            if (rpm == 0) {
                ctrl_elevator_down();
                log_i("----------ctrl_elevator_down");
            } else if (rpm == 10) {
                ctrl_elevator_up();
                log_i("----------ctrl_elevator_up");
            } else {
                ctrl_elevator_sewage();
                log_i("----------ctrl_elevator_sewage");
            }

            break;
        case SIDE_BRUSH:

            device_ioctl(side_brush_handle, MOTOR_CMD_SET_RPM, (void *) &rpm);

            break;
        case ROLLER_BRUSH:
            if (change_clean_module_rpm_flag) {
                ruller_rush_rpm = rpm;
            }
            device_ioctl(roller_brush_handle, MOTOR_CMD_SET_RPM, (void *) &rpm);

            break;
        case ROLLER_TUBE:
            if (change_clean_module_rpm_flag) {
                ruller_tube_rpm = rpm;
            }
            device_ioctl(roller_tube_handle, MOTOR_CMD_SET_RPM, (void *) &rpm);

            break;
        case CLEAN_WATER_PUMP:
            device_ioctl(clean_water_pump_handle, MOTOR_CMD_SET_RPM, (void *) &rpm);

            break;
        case SEWAGE_WATER_PUMP:
            device_ioctl(sewage_water_pump_handle, MOTOR_CMD_SET_RPM, (void *) &rpm);
            break;

        case FAN_MOTOR:
            if (rpm) {
                device_ioctl(fan_motor_handle, MOTOR_CMD_START, &motorNum);
                osDelay(50);
            }
            device_ioctl(fan_motor_handle, MOTOR_CMD_SET_RPM, (void *) &rpm);
            if (!rpm) {
                osDelay(50);
                device_ioctl(fan_motor_handle, MOTOR_CMD_STOP, &motorNum);
            }

            break;

        case SEWAGE_WATER_VALVE:
            device_ioctl(sewage_valve_handle, MOTOR_CMD_SET_RPM, (void *) &rpm);
            break;

        default:
            break;
    }
    return 0;
}

int test_help(void) {
    log_i("清洁组件测试命令");
    log_i("********************************************************");
    log_i("* 0.升降电机  下降    clean_ops_test 0 0               *");
    log_i("*          上升    clean_ops_test 0 1                  *");
    log_i("*          排污    clean_ops_test 0 2                  *");
    log_i("* 1.边刷     开启    clean_ops_test 1 5（比例百分之50）*");
    log_i("*          关闭    clean_ops_test 1 0                  *");
    osDelay(100);
    log_i("* 2.中扫     开启    clean_ops_test 2 5（比例百分之50）*");
    log_i("*          关闭    clean_ops_test 2 0                  *");
    log_i("* 3.滚筒     开启    clean_ops_test 3 5（比例百分之50）*");
    log_i("*          关闭    clean_ops_test 3 0                  *");
    log_i("* 4.清水泵   开启    clean_ops_test 4 5（比例百分之50）*");
    log_i("*          关闭    clean_ops_test 4 0                  *");
    osDelay(100);
    log_i("* 5.污水泵   开启    clean_ops_test 5 1                *");
    log_i("*          关闭    clean_ops_test 5 0                  *");
    log_i("* 6.风机     开启    clean_ops_test 6 5（比例百分之50）*");
    log_i("*          关闭    clean_ops_test 6 0                  *");
    osDelay(100);
    log_i("移动控制测试命令，mcu与rk通信正常可用，注意还原");
    log_i("*mcu_motor_speed_vw 100(线速度mm/s) 50(角速度rad/s)    *");
    log_i("*mcu_motor_speed_vw 0 0 关闭                           *");
    log_i("********************************************************");
    osDelay(100);
    return 0;
}

void get_single_clean_component_ctrl_info(const void *msgin) {
    cJSON * json_msg  = NULL;
    cJSON * json_temp = NULL;
    uint8_t dev_index = 0xff;
    float   set_value = 0;
    if (msgin == NULL)
        return;

    const std_msgs__msg__String *string_msg = (const std_msgs__msg__String *) msgin;
    if (string_msg->data.data != NULL) {
        /* print origin json message */
        log_d("clean component ctrl json:%s", string_msg->data.data);

        json_msg = cJSON_Parse(string_msg->data.data);
        if (json_msg == NULL)
            return;

        for (uint8_t i = 0; i < CLEAN_MODULE_TYPE_MAX; i++) {
            json_temp = cJSON_GetObjectItemCaseSensitive(json_msg, mcu_dev_name[i]);
            if (json_temp != NULL) {
                dev_index = i;
                set_value = json_temp->valuedouble;
                break;
            } else {
                // log_d("%s get error", // mcu_dev_name[i]);
            }
        }

        json_temp = cJSON_GetObjectItemCaseSensitive(json_msg, sewage_valve_name);
        if (json_temp != NULL) {
            set_value = json_temp->valuedouble;
            clean_ops_test(SEWAGE_WATER_VALVE, set_value);
        }

        if (dev_index < CLEAN_MODULE_TYPE_MAX) {
            clean_ops_test(dev_index, set_value);
        } else {
            json_temp = cJSON_GetObjectItemCaseSensitive(json_msg, "cliff");
            if (json_temp != NULL) {
                cliff_pub_update();
            }
        }

        cJSON_Delete(json_msg);
    }
}

// 设置组件控制信息
void set_component_info_callback(const void *msgin) {
    cJSON * root          = NULL;
    cJSON * component_obj = NULL;
    cJSON * json_temp     = NULL;
    uint8_t dev_index     = 0xff;
    float   set_value     = 0;

    float level = 0.0f;
    int   time  = 0;
    char *name;

    if (msgin == NULL)
        return;

    const std_msgs__msg__String *string_msg = (const std_msgs__msg__String *) msgin;
    if (string_msg->data.data != NULL) {
        /* print origin json message */
        log_d("clean component ctrl json:%s", string_msg->data.data);

        root = cJSON_Parse(string_msg->data.data);
        if (!root) {
            log_e("parse root error!");
            return;
        }

        component_obj = cJSON_GetObjectItemCaseSensitive(root, "component");
        if (!component_obj) {
            log_e("parse component_obj error!");
            return;
        }

        json_temp = cJSON_GetObjectItemCaseSensitive(component_obj, "level");
        if (!json_temp) {
            log_e("parse level error!");
            log_e("clean component ctrl json:%s", string_msg->data.data);
            return;
        }
        level = json_temp->valuedouble;

        json_temp = cJSON_GetObjectItemCaseSensitive(component_obj, "name");
        if (!json_temp) {
            log_e("parse name error!");
            return;
        }
        name = json_temp->valuestring;

        json_temp = cJSON_GetObjectItemCaseSensitive(component_obj, "time");
        if (!json_temp) {
            log_e("parse time error!");
            return;
        }
        time = json_temp->valueint;

        for (uint8_t i = 0; i < CLEAN_MODULE_TYPE_MAX; i++) {
            if (strncmp(name, mcu_dev_name[i], 20) == 0) {
                if (!is_floats_equl(level, 0.0f) && !is_timeout(clean_ctrl_comp_info.component_attr[i].stoped_time_ms,
                                                                clean_ctrl_comp_info.component_attr[i].config_stoped_tims_ms)) {
                    log_w("%s last stoped time:%d, not over %d ms, can't contrl:%2.f", mcu_dev_name[i],
                          clean_ctrl_comp_info.component_attr[i].stoped_time_ms, clean_ctrl_comp_info.component_attr[i].config_stoped_tims_ms,
                          level);
                    break;
                }

                if (!is_floats_equl(clean_ctrl_comp_info.component_attr[i].level, level) ||
                    clean_ctrl_comp_info.component_attr[i].control_time != time) {
                    log_i("clean component ctrl json change:%s", string_msg->data.data);
                }
                clean_ctrl_comp_info.component_attr[i].level          = level;
                clean_ctrl_comp_info.component_attr[i].control_time   = time;
                clean_ctrl_comp_info.component_attr[i].last_recv_time = osKernelGetTickCount();
                if (is_floats_equl(clean_ctrl_comp_info.component_attr[i].level, 0.0f)) {
                    clean_ctrl_comp_info.component_attr[i].stoped_time_ms = osKernelGetTickCount();
                }
                break;
            }
        }

        cJSON_Delete(root);
    }
}

//清洁组件异常发布函数
void clean_dev_status_info_pub(uint8_t motor_id, uint32_t error_code, SECURITY_TYPE_E type, bool is_update) {
    cJSON *root       = NULL;
    char * string_msg = NULL;

    if ((motor_id >= MOTOR_MAX) && (type == SECURITY_MOTOR))
        return;

    root = cJSON_CreateObject();
    if (root != NULL) {
        if (type == SECURITY_MOTOR) {
            cJSON_AddStringToObject(root, "dev_name", mcu_dev_name_pub[motor_id]);
        } else if (type == SECURITY_CLEAN_MODULE) {
            cJSON_AddStringToObject(root, "dev_name", mcu_clean_dev_name[motor_id]);
        } else if (type == SECURITY_SENSOR) {
            cJSON_AddStringToObject(root, "dev_name", mcu_sensor_dev_name[motor_id]);
        }

        cJSON_AddNumberToObject(root, "error_code", error_code);
        cJSON_AddBoolToObject(root, "update", is_update);
        string_msg = cJSON_PrintUnformatted(root);
        if (string_msg == NULL) {
            cJSON_Delete(root);
            log_d("can't create publisher msg on line :%d", __LINE__);
            return;
        } else {
            clean_dev_status_info.data.data     = string_msg;
            clean_dev_status_info.data.size     = strlen(string_msg);
            clean_dev_status_info.data.capacity = 0;
            message_publish(&g_clean_dev_status);

            // 临时解决异步 publish 局部变量提前释放的问题
            // osDelay(20);

            cJSON_free(string_msg);
            cJSON_Delete(root);
        }
    } else {
        log_w("creat json obj failed on line %d", __LINE__);
    }
    return;
}

void security_motor_public_callback(void *args) {
    MOTOR_CB_ARGS_T cb_args = {0};
    memcpy(&cb_args, args, sizeof(MOTOR_CB_ARGS_T));
    clean_dev_status_info_pub(cb_args.index, cb_args.error_code, SECURITY_MOTOR, cb_args.is_update);

    return;
}

void security_clean_public_callback(void *args) {
    MOTOR_CB_ARGS_T cb_args = {0};
    memcpy(&cb_args, args, sizeof(MOTOR_CB_ARGS_T));
    clean_dev_status_info_pub(cb_args.index, cb_args.error_code, SECURITY_CLEAN_MODULE, cb_args.is_update);

    return;
}

void security_clean_sensor_callback(void *args) {
    SENSOR_CB_ARGS_T cb_args = {0};
    memcpy(&cb_args, args, sizeof(SENSOR_CB_ARGS_T));
    clean_dev_status_info_pub(cb_args.index, cb_args.error_code, SECURITY_SENSOR, cb_args.is_update);
    return;
}

int pal_clean_topic_init(void) {
    clean_button_cb_attr.security_type = SECURITY_CLEAN_MODULE;
    clean_button_cb_attr.callback      = security_clean_public_callback;

    clean_sensor_cb_attr.security_type = SECURITY_SENSOR;
    clean_sensor_cb_attr.callback      = security_clean_sensor_callback;

#ifdef CLEAN_WATER_SENSOR
    mcu_sensor_dev_name[SENSOR_WATER] = "water_level_sensor";
#endif

#ifdef SEWAGE_TANK_FULL_DET_SENSOR
    mcu_sensor_dev_name[SENSOR_SEWAGE_TANK_FULL] = "sewage_tank_full_sensor";
#endif

#ifdef SEWAGE_WATRE_GROOVE
    mcu_clean_dev_name[BUTTON_CLEAN_SEWAGE_WATRE_GROOVE] = "sewage_water_groove";
#endif

#ifdef DIRT_BOX
    mcu_clean_dev_name[BUTTON_CLEAN_DIRT_BOX] = "dust_box";
#endif

    return 0;
}

int pal_motor_topic_init(void) {
    //设置安全事件类型
    motor_cb_attr.security_type = SECURITY_MOTOR;
    //注册发布回调函数
    motor_cb_attr.callback = security_motor_public_callback;

#ifdef DEFINE_DRIVER
    mcu_dev_name_pub[MOTOR_SECURITY_DRIVER] = "driver";
#endif

#ifdef DEFINE_UP_DOWN_PUSH_ROD
    mcu_dev_name_pub[MOTOR_SECURITY_UP_DOWN_PUSH_ROD] = "lift_motor";
#endif

#ifdef DEFINE_SIDE_BRUSH
    mcu_dev_name_pub[MOTOR_SECURITY_SIDE_BRUSH] = "side_brush";
#endif

#ifdef DEFINE_ROLLER_BRUSH
    mcu_dev_name_pub[MOTOR_SECURITY_ROLLER_BRUSH] = "roll_brush";
#endif

#ifdef DEFINE_ROLLER_TUBE
    mcu_dev_name_pub[MOTOR_SECURITY_ROLLER_TUBE] = "roll_tube";
#endif

#ifdef DEFINE_CLEAN_WATER_PUMP
    mcu_dev_name_pub[MOTOR_SECURITY_CLEAN_WATER_PUMP] = "clean_water_pump";
#endif

#ifdef DEFINE_SEWAGE_WATER_PUMP
    mcu_dev_name_pub[MOTOR_SECURITY_SEWAGE_WATER_PUMP] = "filter_water_pump";
#endif

#ifdef DEFINE_FAN_MOTOR
    mcu_dev_name_pub[MOTOR_SECURITY_FAN_MOTOR] = "fan";
#endif

#ifdef DEFINE_CLEAN_WATER_VALVE
    mcu_dev_name_pub[MOTOR_SECURITY_CLEAN_WATER_VALVE] = "clean_water_valve";
#endif

#ifdef DEFINE_SEWAGE_WATER_VALVE
    mcu_dev_name_pub[MOTOR_SECURITY_SEWAGE_WATER_VALVE] = "sewage_water_valve";
#endif

#ifdef DEFINE_FAN_HEPA
    mcu_dev_name_pub[MOTOR_SECURITY_HEPA] = "hepa";
#endif

    return 0;
}

void register_pub_cb(void) {
    security_pub_register((void *) &motor_cb_attr);
    security_pub_register((void *) &clean_button_cb_attr);
    security_pub_register((void *) &clean_sensor_cb_attr);
}

void get_component_state_update(const void *msgin) {
    if (msgin == NULL)
        return;
    component_state_update_enable = true;
}

void pub_light_det_value(uint16_t val) {
    light_det_value.data = val;
    message_publish(&g_light_det);
}

int pal_motor_init(void) {
    // 订阅组件控制信息
    subscrption_init(&g_clean_component_control, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, String), "/mcu/component_control",
                     &clean_component_control, BEST, set_component_info_callback);

    /* 订阅单个清洁控制信息 */
    subscrption_init(&g_single_clean_component_ctrl, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, String), "/single_clean_component/control",
                     &single_clean_component_ctrl_info, BEST, get_single_clean_component_ctrl_info);
    MallocString(&single_clean_component_ctrl_info.data, REQ, 128);

    /* 发布清洁设备状态 */
    publisher_init(&g_clean_dev_status, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, String), "/component_state/pub", &clean_dev_status_info,
                   BEST, OVERWRITE, sizeof(std_msgs__msg__String));

    publisher_init(&g_light_det, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, UInt16), "/light_det/adc", &light_det_value, BEST, OVERWRITE,
                   sizeof(std_msgs__msg__UInt16));

    /* 设备组件异常状态update更新 */
    subscrption_init(&g_component_state_update_sub, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Empty), "/component_state/update",
                     &component_state_update_info, BEST, get_component_state_update);

    pal_motor_topic_init();
    pal_clean_topic_init();
    register_pub_cb();
    motor_param_update(&clean_ctrl_comp_info);

    clean_component_control.data.data     = clean_component_control_buffer;
    clean_component_control.data.size     = 0;
    clean_component_control.data.capacity = sizeof(clean_component_control_buffer);

    return 0;
}
PAL_MODULE_INIT(pal_motor_init);
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), test_help, test_help, test_help);
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), clean_ops_test, clean_ops_test, clean_ops_test);
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), fan_vcc_ctrl, fan_vcc_ctrl, fan_vcc_ctrl);
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), cwde_vcc_ctrl, cwde_vcc_ctrl, cwde_vcc_ctrl);
#ifdef __cplusplus
}
#endif

/* @} Robot_PAL_UROS */
/* @} Robot_PAL */
