#ifndef __PAL_TASK_CONTROL_H__
#define __PAL_TASK_CONTROL_H__

#include "stdio.h"
#include "pal_zbus.h"

#define AVOID_CLIFF_INFO_NUM_MAX     10
#define AVOID_CLIFF_SOURCE_LEN_MAX   16
#define AVOID_CLIFF_TIMEOUT_MAX      1000
#define AVOID_CLIFF_INTERNAL_PUB_STR "close_cliff"
#define OPEN_CLIFF_INTERNAL_PUB_STR  "open_cliff"

typedef struct avoid_cliff_info_t {
    char     source[AVOID_CLIFF_SOURCE_LEN_MAX];
    uint32_t time_stamp;
    bool     is_used;
} avoid_cliff_info_t;

void key_state_pub(char *key, char *state);
int  ask_cfg(const char *key, char *val);

#endif
