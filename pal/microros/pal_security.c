/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:
 ** Author:         曾曼云
 ** Version:        V0.0.1
 ** Date:           2021-3-25
 ** Description:		microROS安全管理
 ** Others:
 ** Function List:
 ** History:        2021-11 曾曼云 创建
 ** <time>          <author>    <version >    <desc>
 ** 2021-3-25       曾曼云						1.0         创建文件
 ******************************************************************/

/*****************************************************************
 * 包含头文件
 ******************************************************************/
#define LOG_TAG "pal_security"
#include "log.h"
#include "pal_security.h"
#include "fal.h"
#include "devices.h"
#include "define.h"
#include <stdarg.h>
#include <stdio.h>
#include <string.h>
#include "define_button.h"
#include "std_msgs/msg/bool.h"
#include "sensor_msgs/msg/range.h"
#include "pal_zbus.h"
#include "define_motor.h"
#include "fal_security.h"
#ifdef USING_SEGGER
#include "SEGGER_RTT.h"
#endif
#include "std_srvs/srv/set_bool.h"
#include "std_msgs/msg/empty.h"
#include "geometry_msgs/msg/pose_stamped.h"
#include "std_msgs/msg/u_int32.h"
#include "cJSON.h"

/**
 * @addtogroup Robot_PAL 协议适配层 - PAL
 * @{
 */

/**
 * @defgroup Robot_PAL_UROS microROS接口处理
 *
 * @brief
 * \n
 * @{
 */

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 私有宏定义
 ******************************************************************/
#define PUB_TICK 100
/*****************************************************************
 * 私有结构体/共用体/枚举定义
 ******************************************************************/

typedef struct Button_Security_Ros_Attr {
    publisher                            g_pub_uros_button;  //发布
    subscrption                          g_sub_uros_button;  //订阅
    void *                               pub_msg_data;       //发布数据地址
    void *                               sub_msg_data;       //订阅数据地址
    char *                               button_pub_name;    //发布者名称
    char *                               button_sub_name;    //订阅者名称
    const rosidl_message_type_support_t *pub_stMsg;          // pub消息数据结构
    const rosidl_message_type_support_t *sub_stMsg;          // sub消息数据结构
    UBaseType_t                          pub_uxItemSize;     // pub消息大小
    BUTTON_PUB_DATA_TYPE_E               pub_data_type;      //消息类型
    uint8_t                              index;              // button句柄

} BUTTON_SECURITY_ROS_ATTR_T, *BUTTON_SECURITY_ROS_ATTR_P;  // microros属性信息

typedef struct Button_Security_Ros_Pub_Attr {
    uint8_t            index;               // button句柄
    BUTTON_STATUS_TYPE state;               // button当前状态
    BUTTON_STATUS_TYPE last_state;          //上一次的状态
    uint16_t           up_count;            //上报触发计数（此计数为单次触发的计数，触发解除后即会清零）
    uint16_t           down_count;          //上报解除触发计数
    uint16_t           tick_count;          //计数时钟
    uint16_t           time;                //最大触发时间（如：触边传感器触发后两秒需要解除，则time等于2000）
    uint16_t           period;              //周期
    uint8_t            pub_flag;            //发布标志位
    uint8_t            trigger_state;       //触发状态
    uint8_t            last_trigger_state;  //上次的触发状态
} BUTTON_SECURITY_PUB_ATTR_T, *BUTTON_SECURITY_PUB_ATTR_P;  // microros发布属性信息

/*****************************************************************
 * 全局变量定义
 ******************************************************************/

/*****************************************************************
 * 私有全局变量定义
 ******************************************************************/
///< sub emerg_release
static subscrption          g_emerg_release;
static std_msgs__msg__Empty emerg_release;

static std_msgs__msg__Empty uros_emerg_sub;
static std_msgs__msg__Bool  uros_emerg_pub;

static std_msgs__msg__Empty    uros_crash_sub;
static sensor_msgs__msg__Range uros_crash_pub;

static std_msgs__msg__Empty    uros_virtul_wall_sub;
static sensor_msgs__msg__Range uros_virtul_wall_pub;

static subscrption                     g_pose_sub;
static geometry_msgs__msg__PoseStamped pose_stamped;
static subscrption                     g_security_stop;
static std_msgs__msg__UInt32           security_stop_time;

static subscrption           g_security_slow;
static std_msgs__msg__String security_slow_msg = {0};
static char                  security_slow_msg_buff[64];

static BUTTON_SECURITY_ROS_ATTR_T button_ros_attr[SECURITY_BUTTON_CLASS_MAX];
static BUTTON_SECURITY_PUB_ATTR_T button_pub_attr[SECURITY_BUTTON_MAX];
static SECURITY_CB_T              button_cb_attr[SECURITY_BUTTON_MAX];

int pub_data_fill(BUTTON_STATUS_TYPE state, uint8_t index);

// /*****************************************************************
//  * 外部变量声明（如果全局变量没有在其它的H文件声明，引用时需在此处声明，
//  *如果已在其它H文件声明，则只需包含此H文件即可）
//  ******************************************************************/
extern int button_handle;

/*****************************************************************
 * 私有函数原型声明
 ******************************************************************/

void security_public_callback(void *args) {
    BUTTON_CB_ARGS_T cb_args;
    memcpy(&cb_args, args, sizeof(BUTTON_CB_ARGS_T));

    vPortEnterCritical();
    button_pub_attr[cb_args.index].state         = cb_args.state;
    button_pub_attr[cb_args.index].trigger_state = cb_args.trigger_state;
    vPortExitCritical();

    log_i("security pub index[%d] state:[%d]", cb_args.index, cb_args.state);

    return;
}
void update_crash_state_callback(void) {
    uint8_t index = 0;

    for (index = BUTTON_SECURITY_CRASH_1; index <= BUTTON_SECURITY_CRASH_4; index++) {
        pub_data_fill(button_pub_attr[index].state, index);
    }

    return;
}

static void button_state_sub_callback(const void *msgin) {
    int index = 0;
    for (index = 0; index < SECURITY_BUTTON_CLASS_MAX; index++) {
        if (msgin == button_ros_attr[index].sub_msg_data) {
            if (index == BUTTON_SECURITY_CLASS_CRASH) {
                update_crash_state_callback();
                break;
            }

            // 磁条虚拟墙也要用 pub_data_fill 接口，磁条检测Range 类型的frame_id data 没有全局变量
            else if (index == BUTTON_SECURITY_CLASS_VIRTUL_WALL) {
                pub_data_fill(button_pub_attr[BUTTON_VIRTUL_WALL].state, BUTTON_VIRTUL_WALL);
                break;
            }

            message_publish(&button_ros_attr[index].g_pub_uros_button);
        }
    }
}

static void emerg_release_callback(const void *msgin) {
    net_release_emerg_handle();
}

static void pose_callback(const void *msgin) {
    quaternion_t                           quaternion_msg  = {0};
    const geometry_msgs__msg__PoseStamped *pnavi_state_msg = (const geometry_msgs__msg__PoseStamped *) msgin;
    quaternion_msg.x                                       = pnavi_state_msg->pose.orientation.x;
    quaternion_msg.y                                       = pnavi_state_msg->pose.orientation.y;
    quaternion_msg.z                                       = pnavi_state_msg->pose.orientation.z;
    quaternion_msg.w                                       = pnavi_state_msg->pose.orientation.w;
    pose_handle(quaternion_msg);
}

static void security_stop_callback(const void *msgin) {
    if (!msgin) {
        return;
    }

    std_msgs__msg__UInt32 *msg = (std_msgs__msg__UInt32 *) msgin;

    uint32_t time_ms = msg->data;

    log_i("recv security stop :%d", time_ms);

    security_stop_ms(time_ms);
}

static void security_wheel_slow_callback(const void *msgin) {
    cJSON *root        = NULL;
    cJSON *percent_obj = NULL;
    cJSON *time_obj    = NULL;

    uint32_t slow_percent = 0;
    uint32_t slow_ms      = 0;

    if (msgin == NULL)
        return;

    const std_msgs__msg__String *string_msg = (const std_msgs__msg__String *) msgin;
    if (string_msg->data.data == NULL) {
        return;
    }

    root = cJSON_Parse(string_msg->data.data);
    if (!root) {
        log_e("parse root error!");
        return;
    }

    cJSON_GetNumberValue(root);

    percent_obj = cJSON_GetObjectItemCaseSensitive(root, "slow_percent");
    if (!percent_obj) {
        log_e("parse percent_obj error!");
        return;
    }
    slow_percent = percent_obj->valueint;

    time_obj = cJSON_GetObjectItemCaseSensitive(root, "keep_time");
    if (!time_obj) {
        log_e("parse time_obj error!");
        return;
    }
    slow_ms = time_obj->valueint;

    cJSON_Delete(root);

    log_i("slow speed %d percent keep %d time_ms", slow_percent, slow_ms);

    security_slow_wheel(slow_percent, slow_ms);
}

/*****************************************************************
 * 函数定义
 ******************************************************************/
void security_button_attr_init(void) {
#ifdef EMERG

    memset(&button_ros_attr[BUTTON_SECURITY_EMERG], 0, sizeof(BUTTON_SECURITY_ROS_ATTR_T));

    button_ros_attr[BUTTON_SECURITY_CLASS_EMERG].pub_msg_data    = &uros_emerg_pub;
    button_ros_attr[BUTTON_SECURITY_CLASS_EMERG].sub_msg_data    = &uros_emerg_sub;
    button_ros_attr[BUTTON_SECURITY_CLASS_EMERG].button_pub_name = "/emerg";
    button_ros_attr[BUTTON_SECURITY_CLASS_EMERG].button_sub_name = "/emerg/update";
    button_ros_attr[BUTTON_SECURITY_CLASS_EMERG].pub_stMsg       = ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Bool);
    button_ros_attr[BUTTON_SECURITY_CLASS_EMERG].sub_stMsg       = ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Empty);
    button_ros_attr[BUTTON_SECURITY_CLASS_EMERG].pub_uxItemSize  = sizeof(std_msgs__msg__Bool);
    button_pub_attr[BUTTON_SECURITY_EMERG].state                 = BUTTON_PRESS_UP;
    button_pub_attr[BUTTON_SECURITY_EMERG].last_state            = BUTTON_PRESS_UP;

    button_cb_attr[BUTTON_SECURITY_EMERG].callback      = security_public_callback;
    button_cb_attr[BUTTON_SECURITY_EMERG].security_type = SECURITY_EMERG;

#endif

#ifdef CRASH
    button_ros_attr[BUTTON_SECURITY_CLASS_CRASH].pub_msg_data    = &uros_crash_pub;
    button_ros_attr[BUTTON_SECURITY_CLASS_CRASH].sub_msg_data    = &uros_crash_sub;
    button_ros_attr[BUTTON_SECURITY_CLASS_CRASH].button_pub_name = "/touch_edge";
    button_ros_attr[BUTTON_SECURITY_CLASS_CRASH].button_sub_name = "/touch_edge/update";
    button_ros_attr[BUTTON_SECURITY_CLASS_CRASH].pub_stMsg       = ROSIDL_GET_MSG_TYPE_SUPPORT(sensor_msgs, msg, Range);
    button_ros_attr[BUTTON_SECURITY_CLASS_CRASH].sub_stMsg       = ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Empty);
    button_ros_attr[BUTTON_SECURITY_CLASS_CRASH].pub_uxItemSize  = sizeof(sensor_msgs__msg__Range);
#endif

#ifdef CRASH_LEFT

    memset(&button_pub_attr[BUTTON_SECURITY_CRASH_1], 0, sizeof(BUTTON_SECURITY_PUB_ATTR_T));

    button_pub_attr[BUTTON_SECURITY_CRASH_1].state      = BUTTON_PRESS_UP;
    button_pub_attr[BUTTON_SECURITY_CRASH_1].last_state = BUTTON_PRESS_UP;
    button_pub_attr[BUTTON_SECURITY_CRASH_1].time       = 1000;
    button_pub_attr[BUTTON_SECURITY_CRASH_1].period     = 100;

    button_cb_attr[BUTTON_SECURITY_CRASH_1].callback      = security_public_callback;
    button_cb_attr[BUTTON_SECURITY_CRASH_1].security_type = SECURITY_CRASH_LEFT;

    // memset(&button_ros_attr[BUTTON_SECURITY_CRASH_2], 0, sizeof(BUTTON_SECURITY_ROS_ATTR_T));

    button_pub_attr[BUTTON_SECURITY_CRASH_2].state      = BUTTON_PRESS_UP;
    button_pub_attr[BUTTON_SECURITY_CRASH_2].last_state = BUTTON_PRESS_UP;
    button_pub_attr[BUTTON_SECURITY_CRASH_2].time       = 1000;
    button_pub_attr[BUTTON_SECURITY_CRASH_2].period     = 100;

    button_cb_attr[BUTTON_SECURITY_CRASH_2].callback      = security_public_callback;
    button_cb_attr[BUTTON_SECURITY_CRASH_2].security_type = SECURITY_CRASH_LEFT2;

#endif

#ifdef CRASH_RIGHT

    memset(&button_pub_attr[BUTTON_SECURITY_CRASH_4], 0, sizeof(BUTTON_SECURITY_PUB_ATTR_T));

    button_pub_attr[BUTTON_SECURITY_CRASH_4].state      = BUTTON_PRESS_UP;
    button_pub_attr[BUTTON_SECURITY_CRASH_4].last_state = BUTTON_PRESS_UP;
    button_pub_attr[BUTTON_SECURITY_CRASH_4].time       = 1000;
    button_pub_attr[BUTTON_SECURITY_CRASH_4].period     = 100;

    button_cb_attr[BUTTON_SECURITY_CRASH_4].callback      = security_public_callback;
    button_cb_attr[BUTTON_SECURITY_CRASH_4].security_type = SECURITY_CRASH_RIGHT;

    memset(&button_pub_attr[BUTTON_SECURITY_CRASH_3], 0, sizeof(BUTTON_SECURITY_PUB_ATTR_T));

    button_pub_attr[BUTTON_SECURITY_CRASH_3].state      = BUTTON_PRESS_UP;
    button_pub_attr[BUTTON_SECURITY_CRASH_3].last_state = BUTTON_PRESS_UP;
    button_pub_attr[BUTTON_SECURITY_CRASH_3].time       = 1000;
    button_pub_attr[BUTTON_SECURITY_CRASH_3].period     = 100;

    button_cb_attr[BUTTON_SECURITY_CRASH_3].callback      = security_public_callback;
    button_cb_attr[BUTTON_SECURITY_CRASH_3].security_type = SECURITY_CRASH_RIGHT2;

#endif

    memset(&button_ros_attr[BUTTON_SECURITY_CLASS_VIRTUL_WALL], 0, sizeof(BUTTON_SECURITY_ROS_ATTR_T));

    button_ros_attr[BUTTON_SECURITY_CLASS_VIRTUL_WALL].pub_msg_data    = &uros_virtul_wall_pub;
    button_ros_attr[BUTTON_SECURITY_CLASS_VIRTUL_WALL].sub_msg_data    = &uros_virtul_wall_sub;
    button_ros_attr[BUTTON_SECURITY_CLASS_VIRTUL_WALL].button_pub_name = "/magnetic";
    button_ros_attr[BUTTON_SECURITY_CLASS_VIRTUL_WALL].button_sub_name = "/magnetic/update";
    button_ros_attr[BUTTON_SECURITY_CLASS_VIRTUL_WALL].pub_stMsg       = ROSIDL_GET_MSG_TYPE_SUPPORT(sensor_msgs, msg, Range);
    button_ros_attr[BUTTON_SECURITY_CLASS_VIRTUL_WALL].sub_stMsg       = ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Empty);
    button_ros_attr[BUTTON_SECURITY_CLASS_VIRTUL_WALL].pub_uxItemSize  = sizeof(sensor_msgs__msg__Range);
    button_pub_attr[BUTTON_VIRTUL_WALL].state                          = BUTTON_PRESS_UP;
    button_pub_attr[BUTTON_VIRTUL_WALL].last_state                     = BUTTON_PRESS_UP;

    button_cb_attr[BUTTON_VIRTUL_WALL].callback      = security_public_callback;
    button_cb_attr[BUTTON_VIRTUL_WALL].security_type = SECURITY_VIRTUAL_WALL;

    return;
}
int pub_data_fill(BUTTON_STATUS_TYPE state, uint8_t index) {
    char touch_edge_buf[20] = {0};
    switch (index) {
        case BUTTON_SECURITY_EMERG:
            ((std_msgs__msg__Bool *) button_ros_attr[BUTTON_SECURITY_CLASS_EMERG].pub_msg_data)->data =
                (button_pub_attr[index].trigger_state ? 1 : 0);
            message_publish(&button_ros_attr[BUTTON_SECURITY_CLASS_EMERG].g_pub_uros_button);
            button_pub_attr[BUTTON_SECURITY_EMERG].last_trigger_state = button_pub_attr[BUTTON_SECURITY_EMERG].trigger_state;
            break;

        case BUTTON_SECURITY_CRASH_1:
            strcpy(touch_edge_buf, "touch_1");
            break;

        case BUTTON_SECURITY_CRASH_2:
            strcpy(touch_edge_buf, "touch_2");
            break;

        case BUTTON_SECURITY_CRASH_3:
            strcpy(touch_edge_buf, "touch_3");
            break;

        case BUTTON_SECURITY_CRASH_4:
            strcpy(touch_edge_buf, "touch_4");
            break;

        case BUTTON_VIRTUL_WALL:
            strcpy(touch_edge_buf, "virtul_wall_0");
            pal_zbus_ros_msg_set_timestamp(
                &((sensor_msgs__msg__Range *) button_ros_attr[BUTTON_SECURITY_CLASS_VIRTUL_WALL].pub_msg_data)->header.stamp);
            ((sensor_msgs__msg__Range *) button_ros_attr[BUTTON_SECURITY_CLASS_VIRTUL_WALL].pub_msg_data)->header.frame_id.data =
                touch_edge_buf;
            ((sensor_msgs__msg__Range *) button_ros_attr[BUTTON_SECURITY_CLASS_VIRTUL_WALL].pub_msg_data)->header.frame_id.capacity =
                sizeof(touch_edge_buf);
            if (state == BUTTON_PRESS_UP) {
                ((sensor_msgs__msg__Range *) button_ros_attr[BUTTON_SECURITY_CLASS_VIRTUL_WALL].pub_msg_data)->range = 0;
            } else if (state == BUTTON_PRESS_DOWN) {
                ((sensor_msgs__msg__Range *) button_ros_attr[BUTTON_SECURITY_CLASS_VIRTUL_WALL].pub_msg_data)->range = 1;
            }
            ((sensor_msgs__msg__Range *) button_ros_attr[BUTTON_SECURITY_CLASS_VIRTUL_WALL].pub_msg_data)->max_range = 1;
            ((sensor_msgs__msg__Range *) button_ros_attr[BUTTON_SECURITY_CLASS_VIRTUL_WALL].pub_msg_data)->min_range = 1;
            message_publish(&button_ros_attr[BUTTON_SECURITY_CLASS_VIRTUL_WALL].g_pub_uros_button);
            break;
        default:
            break;
    }

    if (index >= BUTTON_SECURITY_CRASH_1 && index <= BUTTON_SECURITY_CRASH_4) {  //碰撞传感器是range类型

        if (osMutexAcquire(button_ros_attr[BUTTON_SECURITY_CLASS_CRASH].g_pub_uros_button.mutex_id, 100) != osOK) {
            return false;
        }

        pal_zbus_ros_msg_set_timestamp(&((sensor_msgs__msg__Range *) button_ros_attr[BUTTON_SECURITY_CLASS_CRASH].pub_msg_data)->header.stamp);
        ((sensor_msgs__msg__Range *) button_ros_attr[BUTTON_SECURITY_CLASS_CRASH].pub_msg_data)->header.frame_id.data = touch_edge_buf;
        ((sensor_msgs__msg__Range *) button_ros_attr[BUTTON_SECURITY_CLASS_CRASH].pub_msg_data)->header.frame_id.capacity =
            sizeof(touch_edge_buf);
        ((sensor_msgs__msg__Range *) button_ros_attr[BUTTON_SECURITY_CLASS_CRASH].pub_msg_data)->header.frame_id.size = strlen(touch_edge_buf);
        if (BUTTON_PRESS_DOWN == state) {
            ((sensor_msgs__msg__Range *) button_ros_attr[BUTTON_SECURITY_CLASS_CRASH].pub_msg_data)->range = 1;
        } else if (BUTTON_PRESS_UP == state) {
            ((sensor_msgs__msg__Range *) button_ros_attr[BUTTON_SECURITY_CLASS_CRASH].pub_msg_data)->range = 0;
        }
        message_publish(&button_ros_attr[BUTTON_SECURITY_CLASS_CRASH].g_pub_uros_button);
        log_d("crash pub index[%d]", index);

        osMutexRelease(button_ros_attr[BUTTON_SECURITY_CLASS_CRASH].g_pub_uros_button.mutex_id);
    }

    return 0;
}

void security_button_pub_handle(void) {
    uint8_t          index = 0;
    BUTTON_CB_ARGS_T button_args;

    for (index = 0; index < SECURITY_BUTTON_MAX; index++) {
        if (0 == button_pub_attr[index].period) {
            button_pub_attr[index].period = 1;
            button_pub_attr[index].time   = 1;
        }
        if (button_pub_attr[index].pub_flag) {
            button_pub_attr[index].tick_count++;
        }

        if ((button_pub_attr[index].last_trigger_state != button_pub_attr[index].trigger_state) ||
            (button_pub_attr[index].state != button_pub_attr[index].last_state) ||
            ((button_pub_attr[index].period != 1) && (button_pub_attr[index].tick_count >= button_pub_attr[index].period / PUB_TICK))) {
            switch (button_pub_attr[index].state) {
                case BUTTON_PRESS_DOWN:
                    if (0 == button_pub_attr[index].down_count) {
                        button_pub_attr[index].up_count = 0;
                    }
                    button_pub_attr[index].pub_flag = 1;
                    pub_data_fill(BUTTON_PRESS_DOWN, index);
                    log_d("button index = %d  state = 1   time = %d\r\n", index, osKernelGetTickCount());
                    button_pub_attr[index].down_count++;
                    break;

                case BUTTON_PRESS_UP:
                    if (0 == button_pub_attr[index].up_count) {
                        button_pub_attr[index].down_count = 0;
                    }
                    // 远程解除急停的情况下，没有按下事件将up_count清零。所以需要单独处理
                    if (index == BUTTON_SECURITY_EMERG) {
                        pub_data_fill(BUTTON_PRESS_UP, index);
                        log_i("button index = %d  state = 0   time = %d\r\n", index, osKernelGetTickCount());
                    } else if (button_pub_attr[index].up_count < (button_pub_attr[index].time / button_pub_attr[index].period)) {
                        button_pub_attr[index].pub_flag = 1;
                        pub_data_fill(BUTTON_PRESS_UP, index);
                        log_i("button index = %d  state = 0   time = %d\r\n", index, osKernelGetTickCount());
                        button_pub_attr[index].up_count++;
                    } else {
                        button_pub_attr[index].pub_flag = 0;
                    }
                    break;

                case BUTTON_SINGLE_CLICK:
                    if (index == BUTTON_SECURITY_EMERG) {
                        pub_data_fill(BUTTON_SINGLE_CLICK, index);
                        log_d("button index = %d  state = 0   time = %d\r\n", index, osKernelGetTickCount());
                    }
                    break;

                case BUTTON_LONG_PRESS_START:
                    if (index == BUTTON_SECURITY_EMERG) {
                        pub_data_fill(BUTTON_LONG_PRESS_START, index);
                        log_d("button index = %d  state = 0   time = %d\r\n", index, osKernelGetTickCount());
                    }
                    break;

                default:
                    break;
            }
            button_pub_attr[index].last_state = button_pub_attr[index].state;
            button_pub_attr[index].tick_count = 0;
        }
    }

    return;
}

const osThreadAttr_t security_button_attributes = {
    .name       = "security_button",
    .priority   = (osPriority_t) osPriorityNormal,
    .stack_size = 256 * 4,
};

static void security_button_run(void *argument) {
    uint8_t index = 0;

    while (1) {
        security_button_pub_handle();
        osDelay(PUB_TICK);
    }

    return;
}

void security_button_init(void) {
    int          index           = 0;
    osThreadId_t security_thread = osThreadNew(security_button_run, NULL, &security_button_attributes);
    security_button_attr_init();

    for (index = 0; index < SECURITY_BUTTON_CLASS_MAX; index++) {
        publisher_init(&button_ros_attr[index].g_pub_uros_button, button_ros_attr[index].pub_stMsg, button_ros_attr[index].button_pub_name,
                       button_ros_attr[index].pub_msg_data, BEST, OVERWRITE, button_ros_attr[index].pub_uxItemSize);
        subscrption_init(&button_ros_attr[index].g_sub_uros_button, button_ros_attr[index].sub_stMsg, button_ros_attr[index].button_sub_name,
                         button_ros_attr[index].sub_msg_data, BEST, button_state_sub_callback);
    }

    return;
}
void register_cb(void) {
    uint8_t index = 0;

    for (index = 0; index < SECURITY_BUTTON_MAX; index++) {
        security_pub_register((void *) &(button_cb_attr[index]));  //向fal层注册处理函数
    }
}

int pal_security_init(void) {
    subscrption_init(&g_emerg_release, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Empty), "/emerg/release", &emerg_release, BEST,
                     emerg_release_callback);
    subscrption_init(&g_pose_sub, ROSIDL_GET_MSG_TYPE_SUPPORT(geometry_msgs, msg, PoseStamped), "/mini_odom", &pose_stamped, BEST,
                     pose_callback);

    subscrption_init(&g_security_stop, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, UInt32), "/mcu/security_stop", &security_stop_time, BEST,
                     security_stop_callback);

    subscrption_init(&g_security_slow, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, String), "/mcu/security_slow", &security_slow_msg, BEST,
                     security_wheel_slow_callback);

    security_slow_msg.data.data     = security_slow_msg_buff;
    security_slow_msg.data.size     = 0;
    security_slow_msg.data.capacity = sizeof(security_slow_msg_buff);

    security_button_init();
    register_cb();

    return 0;
}

PAL_MODULE_INIT(pal_security_init);

/*****************************************************************
 * 函数定义
 *注意，编写函数需首先定义所有的局部变量等，不允许在
 *函数的中间出现新的变量的定义。
 ******************************************************************/

#ifdef __cplusplus
}
#endif

/* @} Robot_PAL_UROS */
/* @} Robot_PAL */
