/******************************************************************
 ** Copyright (C),  CVTE Electronics CO.Ltd 2020-2021.
 ** File name:
 ** Author:         曾曼云
 ** Version:        V0.0.1
 ** Date:           2021-3-25
 ** Description:		microROS充电和电池
 ** Others:
 ** Function List:
 ** History:        2021-11 曾曼云 创建
 ** <time>          <author>    <version >    <desc>
 ** 2021-3-25       曾曼云						1.0         创建文件
 ******************************************************************/

/*****************************************************************
 * 包含头文件
 ******************************************************************/
#define LOG_TAG "pal_cliff"
#include "log.h"
#include "pal_cliff.h"
#include "fal.h"
#include "devices.h"
#include "define.h"
#include <stdarg.h>
#include <stdio.h>
#include <string.h>
#include "pal_zbus.h"
#include "mem_pool.h"
#include "sensor_msgs/msg/range.h"
#include "std_msgs/msg/empty.h"
#include "define_motor.h"
#include "fal_security.h"
#ifdef USING_SEGGER
#include "SEGGER_RTT.h"
#endif
#include "cliff_oml.h"
#include "define_cliff.h"
/**
 * @addtogroup Robot_PAL 协议适配层 - PAL
 * @{
 */

/**
 * @defgroup Robot_PAL_UROS microROS接口处理
 *
 * @brief
 * \n
 * @{
 */

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************
 * 私有宏定义
 ******************************************************************/

/*****************************************************************
 * 私有结构体/共用体/枚举定义
 ******************************************************************/
typedef enum { CLIFF_NO, CLIFF_YES, CLIFF_YES_TO_NO } CLIFF_STATUS1;
/*****************************************************************
 * 全局变量定义
 ******************************************************************/
const osThreadAttr_t           cliff_pub_thread_attributes = {.name       = "cliff_pub_thread",
                                                    .priority   = (osPriority_t) osPriorityNormal,
                                                    .stack_size = 256 * 4};
static publisher               g_uros_cliff;
static sensor_msgs__msg__Range uros_cliff;
static char                    cliff_buffer[20];
uint8_t                        cliff_state[CLIFF_MAX];
float                          cliff_vol[CLIFF_MAX];
bool                           is_debug_always_pub_cliff = false;
uint32_t                       cliff_raw_pub_ts          = 0;
extern uint8_t                 pub_raw_cliff_state[CLIFF_MAX];
static subscrption             g_cliff_update;
static std_msgs__msg__Empty    cliff_update_msg;
/*****************************************************************
 * 私有全局变量定义
 ******************************************************************/

/*****************************************************************
 * 外部变量声明（如果全局变量没有在其它的H文件声明，引用时需在此处声明，
 *如果已在其它H文件声明，则只需包含此H文件即可）
 ******************************************************************/
extern int cliff_oml_handle1;
extern int cliff_handle;

extern adc_sensor_obj_st cliff_sensor_objs[];
/*****************************************************************
 * 私有函数原型声明
 ******************************************************************/

/*****************************************************************
 * 函数定义
 ******************************************************************/

/*****************************************************************
 * 函数定义
 *注意，编写函数需首先定义所有的局部变量等，不允许在
 *函数的中间出现新的变量的定义。
 ******************************************************************/
void cliff_pub_thread(void *argument) {
    uint8_t last_pub_raw_cliff_state[CLIFF_MAX];
    while (1) {
        uros_cliff.min_range = 0.0;
        uros_cliff.max_range = 1.0;
        for (uint8_t index = 0; index < CLIFF_MAX - 3; index++) {
            osDelay(16);

            if (is_debug_always_pub_cliff || (cliff_raw_pub_ts + 3000 >= osKernelGetTickCount())) {
                log_d("cliff[%d]  = %d\n\t", index + 1, cliff_state[index]);
                uros_cliff.field_of_view = cliff_vol[index];
                uros_cliff.range         = cliff_state[index];
                sprintf(uros_cliff.header.frame_id.data, "cliff_%d", index + 1);
                uros_cliff.header.frame_id.size = strlen(uros_cliff.header.frame_id.data);
                pal_zbus_ros_msg_set_timestamp(&uros_cliff.header.stamp);
                message_publish(&g_uros_cliff);

            } else {
                log_d("cliff[%d]  = %d\n\t", index + 1, pub_raw_cliff_state[index]);
                uros_cliff.field_of_view = cliff_vol[index];
                uros_cliff.range         = pub_raw_cliff_state[index];
                sprintf(uros_cliff.header.frame_id.data, "cliff_%d", index + 1);
                uros_cliff.header.frame_id.size = strlen(uros_cliff.header.frame_id.data);
                pal_zbus_ros_msg_set_timestamp(&uros_cliff.header.stamp);
                if (pub_raw_cliff_state[index] == 1) {
                    message_publish(&g_uros_cliff);
                } else {
                    if (pub_raw_cliff_state[index] != last_pub_raw_cliff_state[index]) {
                        for (uint8_t j = 0; j < CLIFF_PUB_REPEAT_CNT; j++) {
                            message_publish(&g_uros_cliff);
                            osDelay(5);
                        }
                    }
                }
                last_pub_raw_cliff_state[index] = pub_raw_cliff_state[index];
            }
        }
    }
}

void cliff_pub_update(void) {
    uros_cliff.min_range = 0.0;
    uros_cliff.max_range = 1.0;
    for (uint8_t index = 0; index < CLIFF_MAX - 3; index++) {
        cliff_state[index] = device_ioctl(cliff_oml_handle1, CLIFF_CMD_GET_STATE, (void *) &cliff_sensor_objs[index + 1]);
        uros_cliff.range   = cliff_state[index];
        if (cliff_handle != -1) {
            cliff_state[index] = device_ioctl(cliff_handle, CLIFF_CMD_GET_STATE, (void *) &cliff_sensor_objs[index + 1]);
        } else {
            cliff_state[index] = device_ioctl(cliff_oml_handle1, CLIFF_CMD_GET_STATE, (void *) &cliff_sensor_objs[index + 1]);
        }
        uros_cliff.range = cliff_state[index];

        sprintf(uros_cliff.header.frame_id.data, "cliff_%d", index + 1);
        uros_cliff.header.frame_id.size = strlen(uros_cliff.header.frame_id.data);
        pal_zbus_ros_msg_set_timestamp(&uros_cliff.header.stamp);
        message_publish(&g_uros_cliff);
    }
}

static void cliff_update_callback(const void *msgin) {
    if (msgin == NULL) {
        return;
    }
    cliff_raw_pub_ts = osKernelGetTickCount();
}

int pal_cliff_init(void) {
    publisher_init(&g_uros_cliff, ROSIDL_GET_MSG_TYPE_SUPPORT(sensor_msgs, msg, Range), "/cliff", &uros_cliff, BEST, OVERWRITE,
                   sizeof(sensor_msgs__msg__Range));
    uros_cliff.header.frame_id.data     = cliff_buffer;
    uros_cliff.header.frame_id.capacity = 20;

    subscrption_init(&g_cliff_update, ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Empty), "/cliff/update", &cliff_update_msg, BEST,
                     cliff_update_callback);

    osThreadNew(cliff_pub_thread, NULL, &cliff_pub_thread_attributes);
    return 0;
}
PAL_MODULE_INIT(pal_cliff_init);

int pal_cliff_deInit(void) {
    return 0;
}

#ifdef __cplusplus
}
#endif

/* @} Robot_PAL_UROS */
/* @} Robot_PAL */
